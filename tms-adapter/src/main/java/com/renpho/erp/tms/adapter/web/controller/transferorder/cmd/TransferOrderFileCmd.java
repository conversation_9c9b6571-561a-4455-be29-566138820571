package com.renpho.erp.tms.adapter.web.controller.transferorder.cmd;

import com.renpho.erp.tms.adapter.web.controller.command.group.CreateGroup;
import com.renpho.erp.tms.adapter.web.controller.command.group.OtherGroup;
import com.renpho.erp.tms.adapter.web.controller.command.group.QueryGroup;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 调拨单附件
 *
 * <AUTHOR>
 * @date 2025/8/28
 */
@Data
public class TransferOrderFileCmd implements Serializable {

    /**
     * 业务单据id
     */
    @NotNull(message = "tsId.is.not.null", groups = {CreateGroup.class, QueryGroup.class})
    private Integer tsId;

    /**
     * 业务单据号
     */
    @NotBlank(message = "tsNo.is.not.null", groups = {CreateGroup.class})
    private String tsNo;

    /**
     * 入库单号
     */
    @NotBlank(message = "shipmentId.is.not.null", groups = {CreateGroup.class})
    private String shipmentId;

    /**
     * ReferenceID
     */
    private String referenceId;

    /**
     * 文件id
     */
    @Valid
    @NotEmpty(message = "fileIds.is.not.null", groups = {CreateGroup.class, OtherGroup.class})
    @Size(max = 5, message = "fileIds.max.size.is.5", groups = {CreateGroup.class})
    private List<String> fileIds;

}
