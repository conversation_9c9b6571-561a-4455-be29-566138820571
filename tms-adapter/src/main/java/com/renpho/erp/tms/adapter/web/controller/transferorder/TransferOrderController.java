package com.renpho.erp.tms.adapter.web.controller.transferorder;

import com.fhs.core.trans.anno.TransMethodResult;
import com.renpho.erp.log.repository.annotation.LogRecord;
import com.renpho.erp.log.repository.support.LogRecordContextHolder;
import com.renpho.erp.tms.adapter.web.controller.command.validator.*;
import com.renpho.erp.tms.adapter.web.controller.transferorder.cmd.*;
import com.renpho.erp.tms.adapter.web.controller.transferorder.cmd.converter.TransferOrderCmdConverter;
import com.renpho.erp.tms.adapter.web.controller.transferorder.vo.TransferOrderDetailVO;
import com.renpho.erp.tms.adapter.web.controller.transferorder.vo.TransferOrderVO;
import com.renpho.erp.tms.adapter.web.controller.transferorder.vo.converter.TransferOrderVoConverter;
import com.renpho.erp.tms.application.transferorder.TransferOrderQueryService;
import com.renpho.erp.tms.application.transferorder.TransferOrderService;
import com.renpho.erp.tms.application.transportrequest.PushTaskQueryService;
import com.renpho.erp.tms.domain.exception.BizErrorCode;
import com.renpho.erp.tms.domain.exception.BusinessException;
import com.renpho.erp.tms.domain.transferorder.TransferOrder;
import com.renpho.erp.tms.domain.transferorder.TransferOrderDataSource;
import com.renpho.erp.tms.domain.transferorder.TransferOrderQuery;
import com.renpho.erp.tms.domain.transferorder.TransferOrderStatus;
import com.renpho.erp.tms.domain.transportrequest.PushTask;
import com.renpho.erp.tms.infrastructure.common.oplog.LogModule;
import com.renpho.karma.dto.Paging;
import com.renpho.karma.dto.R;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.apache.shenyu.client.springcloud.annotation.ShenyuSpringCloudClient;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * 调拨单接口.
 *
 * <AUTHOR>
 * @since 2025/8/23
 */
@Validated
@RestController
@RequestMapping("/transfer/order")
@ShenyuSpringCloudClient("/transfer/order/**")
@RequiredArgsConstructor
public class TransferOrderController {

    private final TransferOrderCmdConverter transferOrderCmdConverter;
    private final TransferOrderService transferOrderService;
    private final TransferOrderVoConverter transferOrderVoConverter;
    private final TransferOrderQueryService transferOrderQueryService;
    private final PushTaskQueryService pushTaskQueryService;


    @PostMapping("/page")
    @TransMethodResult
    @PreAuthorize("hasPermission('tms:transferOrder:list')")
    public R<Paging<TransferOrderVO>> findPage(@RequestBody TransferOrderQuery query) {

        Paging<TransferOrder> page = transferOrderQueryService.findPage(query);
        //查询推送异常
        Map<String, List<String>> failures = pushTaskQueryService.findFailuresByBizNos(page.getRecords())
                .entrySet()
                .stream()
                .filter(Objects::nonNull)
                .map(e -> Map.entry(e.getKey(), e.getValue().stream()
                        .filter(Objects::nonNull).map(PushTask::getErrMsg)
                        .filter(StringUtils::isNotBlank).toList()))
                .collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue));
        Paging<TransferOrderVO> vos = transferOrderVoConverter.toPageVos(page);

        vos.getRecords().forEach(e ->
                Optional.ofNullable(failures.get(e.getTsNo()))
                        .ifPresent(e::setLastErrMsg)
        );
        return R.success(vos);
    }

    /**
     * 统计TS单各状态数量
     */
    @PreAuthorize("hasPermission('tms:transferOrder:list')")
    @PostMapping("/countByStatus")
    public R<Map<Integer, Object>> countByStatus() {
        return R.success(transferOrderQueryService.countByStatus());
    }

    /**
     * 创建调拨单(草稿)
     *
     * <AUTHOR>
     * @since 2025/8/23
     */
    @PostMapping("/add")
    @LogRecord(module = LogModule.TRANSFER_ODER, type = LogModule.CommonDesc.INSERT_OPERATOR, desc = LogModule.CommonDesc.INSERT_DESC)
    public R<Integer> add(@Validated @RequestBody @StoreExist @StoresExist @WarehousesExist @CountryRegionExist @ItemsExist AddCmd cmd) {
        TransferOrder command = transferOrderCmdConverter.toDomain(cmd);
        TransferOrder domain = transferOrderService.add(command);
        TransferOrderVO vo = transferOrderVoConverter.toVo(domain);
        LogRecordContextHolder.recordNewData(vo);
        return R.success(vo.getId());
    }

    /**
     * 创建调拨单并提交
     *
     * <AUTHOR>
     * @since 2025/8/23
     */
    @PostMapping("/addAndSubmit")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(module = LogModule.TRANSFER_ODER, type = LogModule.CommonDesc.INSERT_OPERATOR, desc = LogModule.CommonDesc.INSERT_DESC)
    // TODO 权限
    public R<String> addAndSubmit(@Validated @RequestBody @StoreExist @StoresExist @WarehousesExist @CountryRegionExist @ItemsExist AddCmd cmd) {
        TransferOrder command = transferOrderCmdConverter.toDomain(cmd);
        TransferOrder domain = transferOrderService.add(command);
        transferOrderService.submit(List.of(domain));
        TransferOrderVO vo = transferOrderVoConverter.toVo(domain);
        LogRecordContextHolder.recordNewData(vo);
        return R.success(domain.getInstanceId().id());
    }

    /**
     * 提交调拨单
     *
     * <AUTHOR>
     * @since 2025/8/23
     */
    @PostMapping("/submit")
    @LogRecord(module = LogModule.TRANSFER_ODER, type = LogModule.CommonDesc.SUBMIT_OPERATOR, desc = LogModule.CommonDesc.SUBMIT_DESC)
    // TODO 权限
    public R<List<String>> submit(@Validated @RequestBody @IdsExist IdsCmd cmd) {
        List<TransferOrder> domains = cmd.getTs();
        List<TransferOrderVO> oldData = transferOrderVoConverter.toVos(domains);
        List<String> instanceIds = transferOrderService.submit(domains);
        List<TransferOrderVO> newData = transferOrderVoConverter.toVos(domains);
        for (int i = 0; i < oldData.size(); i++) {
            LogRecordContextHolder.putRecordData(String.valueOf(oldData.get(i).getId()), oldData.get(i), newData.get(i));
        }
        return R.success(instanceIds);
    }

    /**
     * 编辑调拨单
     *
     * <AUTHOR>
     * @since 2025/8/23
     */
    @PostMapping("/update")
    @LogRecord(module = LogModule.TRANSFER_ODER, type = LogModule.CommonDesc.EDIT_OPERATOR, desc = LogModule.CommonDesc.EDIT_DESC)
    // TODO 权限
    public R<Integer> update(@Validated @RequestBody @StoreExist @StoresExist @WarehousesExist @CountryRegionExist @ItemsExist @IdExist UpdateCmd cmd) {
        TransferOrder command = transferOrderCmdConverter.toDomain(cmd);
        TransferOrder exist = cmd.getTransferOrder();
        if (!exist.getDataSource().equals(TransferOrderDataSource.TMS)) {
            throw new BusinessException("error.ts.update-allowed-only-manually-added");
        }
        TransferOrderVO oldData = transferOrderVoConverter.toVo(exist);
        TransferOrder domain = transferOrderService.update(command);
        TransferOrderVO newData = transferOrderVoConverter.toVo(domain);
        LogRecordContextHolder.putRecordData(String.valueOf(newData.getId()), oldData, newData);
        return R.success(newData.getId());
    }


    /**
     * 作废调拨单
     *
     * <AUTHOR>
     * @since 2025/8/23
     */
    @PostMapping("/cancel")
    @LogRecord(module = LogModule.TRANSFER_ODER, type = LogModule.CommonDesc.CANCEL_OPERATOR, desc = LogModule.CommonDesc.CANCEL_DESC)
    public R<Integer> cancel(@Validated @RequestBody @IdExist IdCmd cmd) {
        TransferOrder exist = cmd.getTransferOrder();
        TransferOrderVO oldData = transferOrderVoConverter.toVo(exist);
        TransferOrder domain = transferOrderService.cancel(exist);
        TransferOrderVO newData = transferOrderVoConverter.toVo(domain);
        LogRecordContextHolder.putRecordData(String.valueOf(newData.getId()), oldData, newData);
        return R.success(newData.getId());
    }


    /**
     * 详情
     *
     * @param cmd 参数
     * @return 详情
     */
    @PostMapping("/detail")
    @TransMethodResult
    @PreAuthorize("hasPermission('tms:transferOrder:detail')")
    public R<TransferOrderDetailVO> detail(@RequestBody TransferOrderDetailCmd cmd) {
        Assert.state(Objects.isNull(cmd.getToId()) || StringUtils.isBlank(cmd.getTsNo()),
                BizErrorCode.INVALID_REQUEST_CONTENT.getMessage());

        TransferOrderQuery query = transferOrderCmdConverter.toDetailDomain(cmd);
        TransferOrder order = transferOrderQueryService.detail(query);
        TransferOrderDetailVO vo = transferOrderVoConverter.toDetailVo(order);
        return R.success(vo);
    }


    /**
     * 上传入库单
     *
     * <AUTHOR>
     * @since 2025/8/28
     */
    @PostMapping("/inbound_order/upload")
    @LogRecord(module = LogModule.TRANSFER_ODER, type = LogModule.TransferOrder.UPLOAD_INBOUND_ORDER_NO_OPERATOR, desc = LogModule.TransferOrder.UPLOAD_INBOUND_ORDER_NO_DESC)
    public R uploadInboundOrder(@Validated @RequestBody TransferOrderFileCmd cmd) {
        TransferOrder command = transferOrderCmdConverter.toDomain(cmd);

        TransferOrder exist = transferOrderQueryService.findById(command.getId()).get();
        exist.assertStatusIn(TransferOrderStatus.WAIT_CREATE_INBOUND);
        TransferOrderVO oldData = transferOrderVoConverter.toVo(exist);

        TransferOrder newOrder = transferOrderService.uploadInboundOrder(command);
        TransferOrderVO newData = transferOrderVoConverter.toVo(newOrder);
        LogRecordContextHolder.putRecordData(String.valueOf(newData.getId()), oldData, newData);
        return R.success();
    }

}
