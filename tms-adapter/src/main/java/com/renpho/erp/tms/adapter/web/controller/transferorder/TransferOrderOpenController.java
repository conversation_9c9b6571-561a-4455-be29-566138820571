package com.renpho.erp.tms.adapter.web.controller.transferorder;

import com.renpho.erp.tms.adapter.web.controller.transferorder.cmd.WmsOutBoundStatusCmd;
import com.renpho.erp.tms.application.transferorder.job.wms.TransferOrderOutWmsTrackService;
import com.renpho.karma.dto.R;
import lombok.RequiredArgsConstructor;
import org.apache.shenyu.client.springcloud.annotation.ShenyuSpringCloudClient;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 调拨单-外部系统集成对外接口.
 *
 * <AUTHOR>
 * @since 2025/8/29
 */
@Validated
@RestController
@RequestMapping("/open/transfer/order")
@ShenyuSpringCloudClient("/open/transfer/order/**")
@RequiredArgsConstructor
public class TransferOrderOpenController {

    private final TransferOrderOutWmsTrackService transferOrderOutWmsTrackService;

    /**
     * TS-目的仓为WMS-出库单同步处理.
     *
     * @param transferOrder 同步数据
     * @return 处理结果
     */
    @PostMapping("/synOutBoundStatus")
    public R<Boolean> synOutBoundStatus(@Validated @RequestBody WmsOutBoundStatusCmd transferOrder) {
        return R.success(transferOrderOutWmsTrackService.trackOutboundStatus(transferOrder.getTsNo(), transferOrder.getOutboundNo(), transferOrder.getOutboundTime()));
    }

}
