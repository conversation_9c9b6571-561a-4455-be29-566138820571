package com.renpho.erp.tms.adapter.stream.transferorder;

import com.alibaba.fastjson.JSON;
import com.renpho.erp.bpm.api.dto.ProcessResultDto;
import com.renpho.erp.tms.adapter.stream.transferorder.handle.TransferOrderCmdHandler;
import com.renpho.erp.tms.domain.transportorder.ApprovalStatus;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.messaging.Message;

import java.util.function.Consumer;

/**
 * TS单审批回调
 *
 * <AUTHOR>
 * @since 2025/8/28
 */
@Slf4j
@Configuration
@RequiredArgsConstructor
public class TransferOrderStream {

    @Bean
    public Consumer<Message<ProcessResultDto>> transferOrderBpmConsumer(TransferOrderCmdHandler transferOrderCmdHandler) {
        return msg -> {
            ProcessResultDto payload = msg.getPayload();
            String json = JSON.toJSONString(payload);
            log.info("收到TS单审批回调消息, msg header: [{}],json：[{}] ", msg.getHeaders(), json);
            try {
                ApprovalStatus result = ApprovalStatus.fromValue(payload.getResult());
                if (ApprovalStatus.APPROVED.equals(result)) {
                    transferOrderCmdHandler.approved(payload);
                } else if (ApprovalStatus.REJECTED.equals(result) || ApprovalStatus.VOID.equals(result)) {
                    transferOrderCmdHandler.reject(payload);
                } else if (ApprovalStatus.CANCEL.equals(result)) {
                    transferOrderCmdHandler.cancel(payload);
                } else {
                    transferOrderCmdHandler.updateApprovalResult(payload);
                }
            } catch (InterruptedException e) {
                throw new RuntimeException("Ts单审批回调监听器休眠被中断: payload: " + json, e);
            }
        };
    }

}
