package com.renpho.erp.tms.adapter.web.controller.transferorder.cmd;

import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 调拨单出库-同步更新基础类.
 *
 * <AUTHOR>
 * @since 2025/8/29
 */
@Data
public class WmsOutBoundStatusCmd {

    /**
     * TS单号
     */
    @NotNull(message = "TS单号不能为空")
    private String tsNo;

    /**
     * 调拨单出库单号
     */
    @NotNull(message = "调拨单出库单号不能为空")
    private String outboundNo;

    /**
     * 调拨单出库时间
     */
    @NotNull(message = "调拨单出库时间不能为空")
    private LocalDateTime outboundTime;

}
