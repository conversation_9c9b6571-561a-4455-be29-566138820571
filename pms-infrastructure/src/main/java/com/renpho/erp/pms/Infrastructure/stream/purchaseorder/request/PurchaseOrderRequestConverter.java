package com.renpho.erp.pms.Infrastructure.stream.purchaseorder.request;

import cn.hutool.core.collection.CollUtil;
import com.renpho.erp.pms.domain.purchaseorder.PurchaseOrder;
import com.renpho.erp.pms.domain.purchaseorder.PurchaseOrderPriceInfo;
import com.renpho.erp.pms.domain.purchaserequest.PurchaseRequest;
import com.renpho.erp.pms.model.purchaseorder.model.PurchaseOrderDTO;
import com.renpho.erp.pms.model.purchaseorder.model.PurchaseOrderPriceInfoDTO;
import com.renpho.erp.pms.model.purchaseorder.model.PurchaseOrderSupplierInfoDTO;
import com.renpho.erp.pms.model.purchaserequest.model.PurchaseRequestDTO;
import com.renpho.erp.srm.client.vo.PurchaseSupplierDetailsResp;
import com.renpho.erp.srm.client.vo.PurchaseSupplierFinancialResp;
import com.renpho.erp.srm.client.vo.PurchaseSupplierSettlementResp;
import org.mapstruct.*;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;

/**
 * <AUTHOR> Zheng
 * @date 2025/5/9
 */
@Mapper(unmappedTargetPolicy = ReportingPolicy.IGNORE, componentModel = MappingConstants.ComponentModel.SPRING)
public interface PurchaseOrderRequestConverter {

    /**
     * 启用状态
     */
    Integer ACTIVE = 1;

    /**
     * 是否主数据
     */
    Boolean IS_PRIMARY = true;

    @Mapping(source = "id.id", target = "id")
    @Mapping(source = "product.id.id", target = "productId")
    @Mapping(source = "buyer.operatorId.id", target = "buyerId")
    @Mapping(source = "created.operateTime", target = "createTime")
    @Mapping(target = "supplierInfo", ignore = true)
    @Mapping(target = "request.planningsStaffId", source = "request.planningsStaff.operatorId.id")
    @Mapping(target = "request.salesStaffId", source = "request.salesStaff.operatorId.id")
    @Mapping(target = "request.salesChannelId", source = "request.salesChannelId.id")
    @Mapping(target = "request.boxQty", source = "request.item.boxQty")
    @Mapping(target = "request.boxType", source = "request.item.boxType")
    PurchaseOrderDTO toPurchaseOrderRequest(PurchaseOrder purchaseOrder);

    @Mapping(target = "planningsStaffId", source = "planningsStaff.operatorId.id")
    @Mapping(target = "salesStaffId", source = "salesStaff.operatorId.id")
    @Mapping(target = "salesChannelId", source = "salesChannelId.id")
    @Mapping(target = "boxQty", source = "item.boxQty")
    @Mapping(target = "boxType", source = "item.boxType")
    PurchaseRequestDTO toPurchaseOrderRequest(PurchaseRequest purchaseRequest);

    List<PurchaseRequestDTO> toPurchaseOrderRequest(List<PurchaseRequest> purchaseRequest);

    default PurchaseOrderSupplierInfoDTO toPurchaseOrderSupplierInfoRequest(PurchaseOrderSupplierInfoDTO request,
                                                                            PurchaseSupplierDetailsResp resp) {
        CollUtil.emptyIfNull(resp.getSettlementBOList())
                .stream()
                .filter(e -> e.getId().equals(request.getSupplerSettlementId()))
                .findFirst().ifPresent(settlement -> updateSupplierInfo(request, settlement));
        return request;
    }

    PurchaseOrderSupplierInfoDTO toPurchaseOrderSupplierInfoRequestBasic(PurchaseSupplierDetailsResp purchaseSupplierDetailsResp);

    @Mapping(source = "id", target = "supplerSettlementId")
    @Mapping(target = "id", ignore = true)
    void updateSupplierInfo(@MappingTarget PurchaseOrderSupplierInfoDTO request, PurchaseSupplierSettlementResp settlement);

    @Mapping(source = "id", target = "supplerFinancialId")
    @Mapping(target = "id", ignore = true)
    void updateFinancialInfo(@MappingTarget PurchaseOrderSupplierInfoDTO request, PurchaseSupplierFinancialResp financial);

    @Mapping(source = "id.id", target = "id")
    PurchaseOrderPriceInfoDTO toPurchaseOrderPriceInfoRequest(PurchaseOrderPriceInfo purchaseOrderPriceInfo);

    default PurchaseOrderDTO toPurchaseOrderUploadRequest(PurchaseOrder purchaseOrder) {
        PurchaseOrderDTO purchaseOrderRequest = new PurchaseOrderDTO();
        purchaseOrderRequest.setId(purchaseOrder.getId().id());
        purchaseOrderRequest.setPoNo(purchaseOrder.getPoNo());
        purchaseOrderRequest.setUploadDate(purchaseOrder.getUploadDate());
        return purchaseOrderRequest;
    }

    default List<PurchaseOrderDTO> toPurchaseOrderUploadRequest(Collection<PurchaseOrder> purchaseOrders) {
        List<PurchaseOrderDTO> purchaseOrderRequestList = new ArrayList<>(purchaseOrders.size());
        for (PurchaseOrder purchaseOrder : purchaseOrders) {
            purchaseOrderRequestList.add(toPurchaseOrderUploadRequest(purchaseOrder));
        }
        return purchaseOrderRequestList;
    }
}
