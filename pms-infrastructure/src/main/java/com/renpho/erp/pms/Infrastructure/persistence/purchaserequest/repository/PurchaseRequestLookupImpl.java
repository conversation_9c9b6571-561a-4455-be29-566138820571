package com.renpho.erp.pms.Infrastructure.persistence.purchaserequest.repository;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.renpho.erp.data.permission.service.UserDataPermissionService;
import com.renpho.erp.pms.Infrastructure.feign.processinstance.ProcessInstanceLookup;
import com.renpho.erp.pms.Infrastructure.feign.product.ProductLookup;
import com.renpho.erp.pms.Infrastructure.feign.saleschannel.SalesChannelLookup;
import com.renpho.erp.pms.Infrastructure.feign.user.OperatorLookup;
import com.renpho.erp.pms.Infrastructure.persistence.purchaserequest.mapper.PurchaseRequestMapper;
import com.renpho.erp.pms.Infrastructure.persistence.purchaserequest.po.PurchaseRequestPO;
import com.renpho.erp.pms.Infrastructure.persistence.purchaserequest.po.converter.PurchaseRequestConverter;
import com.renpho.erp.pms.domain.commom.Operator;
import com.renpho.erp.pms.domain.commom.OperatorId;
import com.renpho.erp.pms.domain.processinstance.ProcessInstance;
import com.renpho.erp.pms.domain.processinstance.ProcessInstanceId;
import com.renpho.erp.pms.domain.purchaseorder.PurchaseOrderId;
import com.renpho.erp.pms.domain.purchaserequest.*;
import com.renpho.erp.pms.domain.saleschannel.SalesChannel;
import com.renpho.erp.pms.domain.saleschannel.SalesChannelId;
import com.renpho.erp.security.util.SecurityUtils;
import com.renpho.karma.dto.PageQuery;
import com.renpho.karma.dto.Paging;
import lombok.RequiredArgsConstructor;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.ArrayUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * <AUTHOR>
 * @since 2025/3/17
 */
@Repository
@RequiredArgsConstructor
public class PurchaseRequestLookupImpl extends ServiceImpl<PurchaseRequestMapper, PurchaseRequestPO> implements PurchaseRequestLookup {

    private final PurchaseRequestItemLookup purchaseRequestItemLookup;
    private final PurchaseRequestConverter purchaseRequestConverter;
    private final SalesChannelLookup salesChannelLookup;
    private final OperatorLookup operatorLookup;
    private final ProcessInstanceLookup processInstanceLookup;
    private final ProductLookup productLookup;
    private final UserDataPermissionService userDataPermissionService;
    private final PurchaseRequestMapper purchaseRequestMapper;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Optional<PurchaseRequest> findById(PurchaseRequestId purchaseRequestId) {
        return Optional.ofNullable(purchaseRequestId)
                .map(PurchaseRequestId::id)
                .flatMap(this::getOptById)
                .map(purchaseRequestConverter::toDomain);
    }

    @Override
    public List<PurchaseRequestStatusCountView> countByStatus(Collection<PurchaseRequestStatus> statuses) {
        Set<Integer> status = CollectionUtils.emptyIfNull(statuses)
                .stream()
                .map(PurchaseRequestStatus::getValue)
                .collect(Collectors.toSet());
        PurchaseRequestQuery condition = new PurchaseRequestQuery();
        addDataPermissions(condition);
        Set<String> pskus = new HashSet<>(CollectionUtils.emptyIfNull(condition.getDataPermsPskus()));
        Set<Integer> createBys = CollectionUtils.emptyIfNull(condition.getDataPermsCreateBys()).stream()
                .filter(Objects::nonNull).map(OperatorId::id)
                .filter(Objects::nonNull).collect(Collectors.toSet());
        List<PurchaseRequestStatusCountView> counts = this.baseMapper.countGroupByStatus(status, pskus, createBys);
        int sum = CollectionUtils.emptyIfNull(counts)
                .stream()
                .mapToInt(PurchaseRequestStatusCountView::quantity)
                .filter(Objects::nonNull)
                .sum();
        counts.add(new PurchaseRequestStatusCountView(-1, sum));
        return counts;
    }

    @Override
    public Optional<PurchaseRequest> findByInstanceId(ProcessInstanceId instanceId) {
        return Optional.ofNullable(instanceId)
                .map(ProcessInstanceId::id)
                .filter(StringUtils::isNotBlank)
                .flatMap(id -> this.lambdaQuery().eq(PurchaseRequestPO::getProcessInstanceId, id).last("LIMIT 1").oneOpt())
                .map(purchaseRequestConverter::toDomain)
                .flatMap(this::findItemAssociation);
    }

    @Override
    public Optional<PurchaseRequest> findByPrNo(String prNo) {
        return Optional.ofNullable(prNo)
                .filter(StringUtils::isNotBlank)
                .flatMap(no -> this.lambdaQuery().eq(PurchaseRequestPO::getPrNo, no).last("LIMIT 1").oneOpt())
                .map(purchaseRequestConverter::toDomain);
    }

    @Override
    public List<PurchaseRequest> findByPrNos(Collection<String> prNos) {
        Set<String> nos = CollectionUtils.emptyIfNull(prNos)
                .stream()
                .filter(StringUtils::isNotBlank)
                .collect(Collectors.toSet());
        if (CollectionUtils.isEmpty(nos)) {
            return List.of();
        }

        List<PurchaseRequestPO> pos = lambdaQuery().in(PurchaseRequestPO::getPrNo, nos).list();
        return purchaseRequestConverter.toDomains(pos);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public List<PurchaseRequest> findByIds(Collection<PurchaseRequestId> purchaseRequestIds) {
        Set<Integer> ids = CollectionUtils.emptyIfNull(purchaseRequestIds)
                .stream().filter(Objects::nonNull)
                .map(PurchaseRequestId::id)
                .filter(Objects::nonNull)
                .collect(Collectors.toCollection(TreeSet::new));
        Collection<PurchaseRequestPO> domains = CollectionUtils.emptyIfNull(listByIds(ids));
        return purchaseRequestConverter.toDomains(domains);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Paging<PurchaseRequest> findPage(PurchaseRequestQuery condition, PageQuery pageQuery) {
        addDataPermissions(condition);
        pageQuery = Optional.ofNullable(pageQuery).orElse(new PageQuery());
        LambdaQueryChainWrapper<PurchaseRequestPO> wrapper = condition(condition);
        if (wrapper == null) {
            return Paging.of(pageQuery.getPageSize(), pageQuery.getPageIndex());
        }
        Page<PurchaseRequestPO> pagePos = wrapper.orderByDesc(PurchaseRequestPO::getUpdateTime)
                .page(new Page<>(pageQuery.getPageIndex(), pageQuery.getPageSize()));
        return purchaseRequestConverter.toPageDomains(pagePos);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public List<PurchaseRequest> findAll(PurchaseRequestQuery condition) {
        addDataPermissions(condition);
        LambdaQueryChainWrapper<PurchaseRequestPO> wrapper = condition(condition);
        if (wrapper == null) {
            return List.of();
        }
        List<PurchaseRequestPO> pos = wrapper.list();
        return purchaseRequestConverter.toDomains(pos);
    }

    @Override
    public List<PurchaseRequest> findSalesChannelAssociations(Collection<PurchaseRequest> domains) {
        Map<SalesChannelId, List<PurchaseRequest>> map = CollectionUtils.emptyIfNull(domains).stream()
                .filter(p -> Objects.nonNull(p.getSalesChannelId()))
                .collect(Collectors.groupingBy(PurchaseRequest::getSalesChannelId));
        List<SalesChannel> salesChannelGroupById = salesChannelLookup.findByIds(map.keySet());
        for (SalesChannel salesChannel : salesChannelGroupById) {
            for (PurchaseRequest d : map.getOrDefault(salesChannel.getId(), List.of())) {
                d.setSalesChannel(salesChannel);
            }
        }
        return new ArrayList<>(domains);
    }

    @Override
    public List<PurchaseRequest> findStoreAssociations(Collection<PurchaseRequest> domains) {
        List<PurchaseRequestItem> items = CollectionUtils.emptyIfNull(domains).stream()
                .map(PurchaseRequest::getItem)
                .filter(Objects::nonNull)
                .toList();
        purchaseRequestItemLookup.findStoreAssociations(items);
        return new ArrayList<>(domains);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Optional<PurchaseRequest> findItemAssociation(PurchaseRequest domain) {
        Optional<PurchaseRequestId> idOpt = Optional.ofNullable(domain).map(PurchaseRequest::getId);
        if (idOpt.isEmpty()) {
            return Optional.empty();
        }
        purchaseRequestItemLookup.findByPrId(idOpt.get()).ifPresent(domain::setItem);
        return Optional.of(domain);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public List<PurchaseRequest> findPurchaseOrderAssociations(Collection<PurchaseRequest> domains) {
        List<PurchaseRequestItem> items = CollectionUtils.emptyIfNull(domains)
                .stream().filter(Objects::nonNull)
                .map(PurchaseRequest::getItem)
                .filter(Objects::nonNull)
                .toList();
        if (CollectionUtils.isEmpty(items)) {
            return List.of();
        }
        purchaseRequestItemLookup.findPurchaseRequestOrdersAssociations(items);
        return new ArrayList<>(CollectionUtils.emptyIfNull(domains));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public List<PurchaseRequest> findItemAssociations(Collection<PurchaseRequest> domains) {
        Map<PurchaseRequestId, PurchaseRequest> purchaseRequestIds = CollectionUtils.emptyIfNull(domains).stream()
                .filter(Objects::nonNull)
                .filter(p -> Objects.nonNull(p.getId().id()))
                .collect(Collectors.toMap(PurchaseRequest::getId, Function.identity()));
        if (MapUtils.isEmpty(purchaseRequestIds)) {
            return List.of();
        }

        Map<PurchaseRequestId, PurchaseRequestItem> itemGroupByPrId = purchaseRequestItemLookup.findByPrIds(purchaseRequestIds.keySet());
        for (Map.Entry<PurchaseRequestId, PurchaseRequestItem> entry : itemGroupByPrId.entrySet()) {
            Optional.ofNullable(purchaseRequestIds.get(entry.getKey())).ifPresent(p -> p.setItem(entry.getValue()));
        }
        return new ArrayList<>(domains);
    }

    @Override
    public List<PurchaseRequest> findOperatorAssociations(Collection<PurchaseRequest> domains) {
        List<Operator> operators = CollectionUtils.emptyIfNull(domains).stream()
                .filter(Objects::nonNull)
                .flatMap(p -> Stream.of(Optional.ofNullable(p.getPlanningsStaff()), Optional.ofNullable(p.getSalesStaff()), Optional.ofNullable(p.getCreated()), Optional.ofNullable(p.getUpdated())))
                .filter(Optional::isPresent)
                .map(Optional::get)
                .toList();
        if (CollectionUtils.isEmpty(operators)) {
            return List.of();
        }

        operatorLookup.findAndSetByIds(operators);
        return new ArrayList<>(domains);
    }

    @Override
    public List<PurchaseRequest> findProductAssociations(Collection<PurchaseRequest> domains) {
        List<PurchaseRequestItem> items = CollectionUtils.emptyIfNull(domains).stream()
                .filter(Objects::nonNull)
                .map(PurchaseRequest::getItem)
                .filter(Objects::nonNull)
                .toList();
        if (CollectionUtils.isEmpty(items)) {
            return List.of();
        }

        purchaseRequestItemLookup.findProductAssociations(items);
        return new ArrayList<>(domains);
    }

    @Override
    public List<PurchaseRequest> findProcessInstanceAssociations(Collection<PurchaseRequest> domains) {
        Set<ProcessInstanceId> processInstanceIds = CollectionUtils.emptyIfNull(domains)
                .stream()
                .filter(Objects::nonNull)
                .map(PurchaseRequest::getProcessInstanceId)
                .collect(Collectors.toSet());
        if (CollectionUtils.isEmpty(processInstanceIds)) {
            return List.of();
        }
        Map<ProcessInstanceId, List<Operator>> processInstanceOperators = processInstanceLookup.findCurrentOperators(processInstanceIds);
        for (PurchaseRequest domain : CollectionUtils.emptyIfNull(domains)) {
            ProcessInstance processInstance = domain.getProcessInstance();
            if (processInstance != null) {
                processInstance.setCurrentOperators(processInstanceOperators.getOrDefault(processInstance.getId(), List.of()));
                Optional.ofNullable(processInstance.getId())
                        .flatMap(processInstanceLookup::findById)
                        .map(ProcessInstance::getProcessInstanceBizCode)
                        .ifPresent(processInstance::setProcessInstanceBizCode);
            }
        }
        return new ArrayList<>(CollectionUtils.emptyIfNull(domains));
    }

    private LambdaQueryChainWrapper<PurchaseRequestPO> condition(PurchaseRequestQuery condition) {
        if (condition.containsItemQuery()) {
            List<PurchaseRequestId> prIds = purchaseRequestItemLookup.findAllPrId(condition);
            if (CollectionUtils.isEmpty(prIds)) {
                return null;
            }
            Collection<PurchaseRequestId> ids = Optional.ofNullable(condition.getIds()).filter(CollectionUtils::isNotEmpty).orElseGet(ArrayList::new);
            ids.addAll(prIds);
            condition.setIds(ids);
        }

        if (CollectionUtils.isNotEmpty(condition.getDataPermsPskus())) {
            PurchaseRequestQuery dataPermCondition = new PurchaseRequestQuery();
            dataPermCondition.setPskus(condition.getDataPermsPskus());
            List<PurchaseRequestId> dataPermIds = purchaseRequestItemLookup.findAllPrId(dataPermCondition);
            if (CollectionUtils.isNotEmpty(dataPermIds)) {
                condition.setDataPermsIds(dataPermIds);
            }
        }

        Optional<Integer> hasId = Optional.ofNullable(condition.getId()).map(PurchaseRequestId::id);
        Set<Integer> hasIds = CollectionUtils.emptyIfNull(condition.getIds()).stream().map(PurchaseRequestId::id).collect(Collectors.toCollection(TreeSet::new));
        Optional<String> hasPrNo = Optional.ofNullable(condition.getPrNo()).filter(StringUtils::isNotBlank);
        Set<String> hasPrNos = CollectionUtils.emptyIfNull(condition.getPrNos()).stream().filter(StringUtils::isNotBlank).collect(Collectors.toSet());
        Optional<String> hasLikePrNo = Optional.ofNullable(condition.getLikePrNo()).filter(StringUtils::isNotBlank);
        Optional<Integer> hasStatus = Optional.ofNullable(condition.getStatus()).map(PurchaseRequestStatus::getValue);
        Set<Integer> hasStatuses = CollectionUtils.emptyIfNull(condition.getStatuses()).stream().filter(Objects::nonNull).map(PurchaseRequestStatus::getValue).collect(Collectors.toSet());
        Optional<Integer> hasSalesChannelId = Optional.ofNullable(condition.getChannelId()).map(SalesChannelId::id);
        Set<Integer> hasSalesChannelIds = CollectionUtils.emptyIfNull(condition.getChannelIds()).stream().filter(Objects::nonNull).map(SalesChannelId::id).filter(Objects::nonNull).collect(Collectors.toSet());
        Optional<Integer> hasPlanningsStaffId = Optional.ofNullable(condition.getPlanningsStaffId());
        Set<Integer> hasPlanningsStaffIds = CollectionUtils.emptyIfNull(condition.getPlanningsStaffIds()).stream().filter(Objects::nonNull).collect(Collectors.toSet());
        Optional<Integer> hasSalesStaffId = Optional.ofNullable(condition.getSalesStaffId());
        Set<Integer> hasSalesStaffIds = CollectionUtils.emptyIfNull(condition.getSalesStaffIds()).stream().filter(Objects::nonNull).collect(Collectors.toSet());
        Optional<LocalDateTime> hasCreateTime = Optional.ofNullable(condition.getCreateTime());
        Optional<LocalDateTime> hasCreateTimeStart = Optional.ofNullable(condition.getCreateTimeStart());
        Optional<LocalDateTime> hasCreateTimeEnd = Optional.ofNullable(condition.getCreateTimeEnd());
        Optional<LocalDateTime> hasUpdateTime = Optional.ofNullable(condition.getUpdateTime());
        Optional<LocalDateTime> hasUpdateTimeStart = Optional.ofNullable(condition.getUpdateTimeStart());
        Optional<LocalDateTime> hasUpdateTimeEnd = Optional.ofNullable(condition.getUpdateTimeEnd());
        Set<Integer> hasCreateBys = CollectionUtils.emptyIfNull(condition.getCreateBys()).stream().filter(Objects::nonNull).map(OperatorId::id).filter(Objects::nonNull).collect(Collectors.toSet());
        Set<Integer> hasDataPermsCreateBys = CollectionUtils.emptyIfNull(condition.getDataPermsCreateBys()).stream().filter(Objects::nonNull).map(OperatorId::id).filter(Objects::nonNull).collect(Collectors.toSet());
        Set<Integer> hasDataPermsIds = CollectionUtils.emptyIfNull(condition.getDataPermsIds()).stream().filter(Objects::nonNull).map(PurchaseRequestId::id).filter(Objects::nonNull).collect(Collectors.toSet());
        return lambdaQuery()
                .eq(hasId.isPresent(), PurchaseRequestPO::getId, hasId.orElse(null))
                .in(CollectionUtils.isNotEmpty(hasIds), PurchaseRequestPO::getId, hasIds)
                .eq(hasPrNo.isPresent(), PurchaseRequestPO::getPrNo, hasPrNo.orElse(null))
                .in(CollectionUtils.isNotEmpty(hasPrNos), PurchaseRequestPO::getPrNo, hasPrNos)
                .like(hasLikePrNo.isPresent(), PurchaseRequestPO::getPrNo, hasLikePrNo.orElse(null))
                .eq(hasStatus.isPresent(), PurchaseRequestPO::getStatus, hasStatus.orElse(null))
                .eq(CollectionUtils.isNotEmpty(hasStatuses), PurchaseRequestPO::getStatus, hasStatuses)
                .eq(hasSalesChannelId.isPresent(), PurchaseRequestPO::getSalesChannelId, hasSalesChannelId.orElse(null))
                .in(CollectionUtils.isNotEmpty(hasSalesChannelIds), PurchaseRequestPO::getSalesChannelId, hasSalesChannelIds)
                .eq(hasPlanningsStaffId.isPresent(), PurchaseRequestPO::getPlanStaffId, hasPlanningsStaffId.orElse(null))
                .in(CollectionUtils.isNotEmpty(hasPlanningsStaffIds), PurchaseRequestPO::getPlanStaffId, hasPlanningsStaffIds)
                .eq(hasSalesStaffId.isPresent(), PurchaseRequestPO::getOperationStaffId, hasSalesStaffId.orElse(null))
                .in(CollectionUtils.isNotEmpty(hasSalesStaffIds), PurchaseRequestPO::getOperationStaffId, hasSalesStaffIds)
                .eq(hasCreateTime.isPresent(), PurchaseRequestPO::getCreateTime, hasCreateTime.orElse(null))
                .ge(hasCreateTimeStart.isPresent(), PurchaseRequestPO::getCreateTime, hasCreateTimeStart.orElse(null))
                .le(hasCreateTimeEnd.isPresent(), PurchaseRequestPO::getCreateTime, hasCreateTimeEnd.orElse(null))
                .eq(hasUpdateTime.isPresent(), PurchaseRequestPO::getUpdateTime, hasUpdateTime.orElse(null))
                .ge(hasUpdateTimeStart.isPresent(), PurchaseRequestPO::getUpdateTime, hasUpdateTimeStart.orElse(null))
                .le(hasUpdateTimeEnd.isPresent(), PurchaseRequestPO::getUpdateTime, hasUpdateTimeEnd.orElse(null))
                .in(CollectionUtils.isNotEmpty(hasCreateBys), PurchaseRequestPO::getCreateBy, hasCreateBys)
                .and(CollectionUtils.isNotEmpty(hasDataPermsIds) || CollectionUtils.isNotEmpty(hasDataPermsCreateBys), w -> w.in(CollectionUtils.isNotEmpty(hasDataPermsIds), PurchaseRequestPO::getId, hasDataPermsIds).or(CollectionUtils.isNotEmpty(hasDataPermsCreateBys), wr -> wr.in(PurchaseRequestPO::getCreateBy, hasDataPermsCreateBys)));
    }

    private void addDataPermissions(PurchaseRequestQuery condition) {
        if (SecurityUtils.isAdmin()) {
            return;
        }
        // 组装通用数据权限条件
        Set<Integer> userIds = userDataPermissionService.getDataPermission();
        List<OperatorId> createBys = CollectionUtils.emptyIfNull(userIds)
                .stream()
                .filter(Objects::nonNull)
                .map(OperatorId::new)
                .toList();
        condition.setDataPermsCreateBys(createBys);
        // 组装 PR 单数据权限条件
        List<OperatorId> operatorIds = List.of(new OperatorId(SecurityUtils.getUserId()));
        Set<String> labels = Arrays.stream(ArrayUtils.nullToEmpty(SecurityUtils.getUserLabels())).collect(Collectors.toSet());
        Set<String> dataPerms = new HashSet<>();
        // 若当前用户拥有采购或计划角色标签(role_label IN (1, 4)), 则从产品管理接口中获取该用户可查询的产品 ID 集合
        if (labels.contains("1") || labels.contains("4")) {
            List<String> pskus = productLookup.findPskuByOperatorIds(operatorIds);
            dataPerms.addAll(pskus);
        }
        // 若当前用户拥有运营角色标签(role_label=7), 则从产品授权接口中获取该用户可查询的产品 ID 集合
        if (labels.contains("7")) {
            List<String> pskus = productLookup.findAuthorizedPskuByOperatorIds(operatorIds);
            dataPerms.addAll(pskus);
        }
        // 非管理员需控制可查询的 PSKU, 通过添加一个不存在的 PSKU 来控制查询可见性
        dataPerms.add("不存在的PSKU");
        condition.setDataPermsPskus(dataPerms);
    }

    @Override
    public List<PurchaseRequest> findByPoIds(Collection<PurchaseOrderId> poIds) {
        List<PurchaseRequestItem> requestItemList = purchaseRequestItemLookup.findByPoId(poIds);
        if (CollUtil.isEmpty(requestItemList)) {
            return List.of();
        }
        Map<PurchaseRequestId, PurchaseRequestItem> requestItemMap = requestItemList.stream()
                .collect(Collectors.toMap(PurchaseRequestItem::getPurchaseRequestId, Function.identity(), (k1, k2) -> k1));
        List<Integer> requestIdList = requestItemMap.keySet().stream().map(PurchaseRequestId::id).toList();
        return purchaseRequestConverter.toDomains(lambdaQuery().in(PurchaseRequestPO::getId, requestIdList).list());
    }

}
