package com.renpho.erp.tms.domain.transferorder;

import com.renpho.erp.tms.domain.inbound.WarehouseProviderType;
import com.renpho.erp.tms.domain.transportrequest.SyncApiStatus;
import org.jmolecules.ddd.types.Repository;

import java.util.List;

/**
 * 调拨单仓储接口
 *
 * <AUTHOR>
 * @since 2025/8/22
 */
public interface TransferOrderRepository extends Repository<TransferOrder, TransferOrderId> {

    /**
     * 保存调拨单
     *
     * @param transferOrder 调拨单
     * @return 调拨单ID
     */
    TransferOrder add(TransferOrder transferOrder);

    /**
     * 更新调拨单
     *
     * @param transferOrder 调拨单
     */
    TransferOrder update(TransferOrder transferOrder);

    /**
     * 批量保存调拨单
     *
     * @param transferOrders 调拨单列表
     */
    void batchSave(List<TransferOrder> transferOrders);

    void updateById(TransferOrder ts);

    /**
     * 根据TS状态查询TS单信息
     *
     * @param shipStatusList TS状态列表
     * @param warehouseType  仓库类型
     * @return TS单信息
     */
    List<TransferOrderData> selectTsListByShipStatusList(List<TransferOrderStatus> shipStatusList, WarehouseProviderType warehouseType);

    /**
     * 清空发货单ID
     *
     * @param id 调拨单ID
     */
    void clearShipmentIdById(TransferOrderId id);

    /**
     * 更新同步状态
     *
     * @param id            调拨单ID
     * @param syncApiStatus 同步状态
     */
    void updateSyncApiStatusById(TransferOrderId id, SyncApiStatus syncApiStatus);

    void updateSyncApiStatus(String bizNo, SyncApiStatus syncApiStatus);


    /**
     * 根据审批状态跟新单据
     *
     * <AUTHOR>
     * @since 2025/8/28 17:31
     */
    void updateApprovalResultByInstanceId(TransferOrderId id, TransferOrderStatus status, Integer updateBy);

    /**
     * 根据TS单号查询TS单信息
     *
     * @param tsNo TS单号
     * @return TS单信息
     */
    TransferOrder findByTsNo(String tsNo);

    TransferOrder cancel(TransferOrder command);

    /**
     * 上传入库单号，跟新入库单信息
     * <AUTHOR>
     * @date 2025/8/29 12:05
     * @param
     */
    void updateShipmentIdInfo(String shipmentId, String referenceId, List<String> fileIds, TransferOrderId id);
}
