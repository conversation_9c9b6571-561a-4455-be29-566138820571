package com.renpho.erp.tms.domain.orderfile;

import com.renpho.erp.tms.domain.file.FileId;
import com.renpho.erp.tms.domain.file.FileInfo;
import com.renpho.erp.tms.domain.operator.Operator;
import lombok.Data;
import org.jmolecules.ddd.types.AggregateRoot;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2025/6/9
 */
@Data
public class OrderFile implements AggregateRoot<OrderFile, OrderFileId> {
    private final OrderFileId id;

    /**
     * 单据ID
     */
    private Integer orderId;

    /**
     * 单据号
     */
    private String orderNo;

    /**
     * 业务类型：
     * TO-头程单；
     * TS-调拨单
     */
    private BusinessTypeEnum businessType;

    /**
     * 文件类型：
     * FREIGHT_QUOTE-货代报价单；
     * BILL_OF_LADING-提单；
     * INSURANCE_POLICY-保单；
     * POD-POD；
     * CUSTOMS_DECLARATION-报关单；
     * PACKING_LIST-箱单；INVOICE-发票；CUSTOMS_TAX_RECEIPT-海关税单；
     * CERTIFICATE_OF_ORIGIN-产地证；
     * OTHER-其他文件
     * CartonLabel箱唛文件
     */
    private FileTypeEnum fileType;

    /**
     * 文件明细
     */
    private FileInfo fileInfo;

    /**
     * 创建信息
     */
    private Operator created;

    /**
     * 更新信息
     */
    private Operator updated;

    /**
     * 海关税单号
     */
    private String customsNo;

    /**
     * 文件信息
     */
    private List<OrderFileInfo> fileInfos;
}
