package com.renpho.erp.pds.client;

import com.renpho.erp.openfeign.annotation.NoToken;
import com.renpho.erp.pds.client.request.BrandUpdateRequest;
import com.renpho.erp.pds.client.vo.PdsBrandViewQuery;
import com.renpho.erp.pds.client.vo.PdsBrandViewVo;
import com.renpho.karma.dto.R;
import com.renpho.karma.openfeign.FeignConfiguration;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

/**
 * 品牌对外接口定义
 *
 * <AUTHOR>
 * @since 2024.9.24
 */
@FeignClient(name = "erp-pds", configuration = { FeignConfiguration.class })
public interface RemoteBrandService {

	@NoToken
	@PostMapping("/brand/view/list")
	R<List<PdsBrandViewVo>> viewList(@RequestBody PdsBrandViewQuery pdsBrandViewQuery);

	/**
	 * 更新品牌最后SN码
	 * @param request 请求参数
	 * @return 结果
	 */
	@NoToken
	@PostMapping("/brand/updateLastSnCode")
	R<Boolean> updateLastSnCode(@RequestBody BrandUpdateRequest request);

}
