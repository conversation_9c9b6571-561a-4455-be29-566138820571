package com.renpho.erp.pds.client;

import com.renpho.erp.openfeign.annotation.NoToken;
import com.renpho.erp.pds.client.vo.BasicInfoViewQuery;
import com.renpho.erp.pds.client.vo.BasicInfoViewQueryVo;
import com.renpho.erp.pds.client.vo.PdsProductModelVO;
import com.renpho.karma.dto.R;
import com.renpho.karma.openfeign.FeignConfiguration;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

/**
 * 基础信息-id:name(查询接口)
 *
 * <AUTHOR>
 * @since 2024.12.06
 */
@FeignClient(name = "erp-pds", configuration = { FeignConfiguration.class })
public interface RemotePdsCommonService {

	@NoToken
	@PostMapping("/feign/views/query/info")
	R<BasicInfoViewQueryVo> viewList(@RequestBody BasicInfoViewQuery query);

	@NoToken
	@PostMapping("/pdsProductModel/getPdsProductModelList")
	R<List<PdsProductModelVO>> getPdsProductModelList(@RequestBody List<Integer> idList);

}
