package com.renpho.erp.pds.client.vo;

import lombok.Data;

/**
 * 品类表.
 *
 * <AUTHOR>
 * @since 2024.9.19
 */
@Data
public class PdsCategoryViewQuery {

    /**
     * 语言环境，1中文，0英文,  , see {@link com.renpho.erp.pds.client.common.LanguageEnum}
     */
    private Integer language;

    /**
     * 搜索的关键字，1层、2层、3层都可以
     */
    private String keyword;

    /**
     * Menu Status 选项为 Active 和 Inactive 单选，默认选中Active
     */
    private Integer status;

}
