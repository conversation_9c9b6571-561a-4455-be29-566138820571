package com.renpho.erp.pds.client.vo;

import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

/**
 * 产品管理-属性信息，详情
 *
 * <AUTHOR>
 * @since 2024.11.05
 */
@Data
public class PdsProductManagerDetailsProsVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    private Integer id;

    /**
     * 关联的产品管理主键ID
     */
    private Integer productManagerId;

    /**
     * 单品尺寸-长 (cm)/公制
     */
    private Double productLengthMetric;

    /**
     * 单品尺寸-宽 (cm)/公制
     */
    private Double productWidthMetric;

    /**
     * 单品尺寸-高 (cm)/公制
     */
    private Double productHeightMetric;

    /**
     * 单品净重 (kg)/公制
     */
    private Double productWeightMetric;

    /**
     * 单品尺寸-长 (inch)/英制
     */
    private Double productLengthImperial;

    /**
     * 单品尺寸-宽 (inch)/英制
     */
    private Double productWidthImperial;

    /**
     * 单品尺寸-高 (inch)/英制
     */
    private Double productHeightImperial;

    /**
     * 单品净重 (lb)/英制
     */
    private Double productWeightImperial;

    /**
     * 产品数量
     */
    private Integer productQuantity;

    /**
     * 产品备注
     */
    private String productRemarks;

    /**
     * 包装尺寸(即彩盒尺寸)-长 (cm)/公制
     */
    private Double packagingLengthMetric;

    /**
     * 包装尺寸(即彩盒尺寸)-宽 (cm)/公制
     */
    private Double packagingWidthMetric;

    /**
     * 包装尺寸(即彩盒尺寸)-高 (cm)/公制
     */
    private Double packagingHeightMetric;

    /**
     * 单品毛重 (kg)/公制
     */
    private Double grossWeightMetric;

    /**
     * 包装尺寸(即彩盒尺寸)-长 (inch)/英制
     */
    private Double packagingLengthImperial;

    /**
     * 包装尺寸(即彩盒尺寸)-宽 (inch)/英制
     */
    private Double packagingWidthImperial;

    /**
     * 包装尺寸(即彩盒尺寸)-高 (inch)/英制
     */
    private Double packagingHeightImperial;

    /**
     * 单品毛重 (lb)/英制
     */
    private Double grossWeightImperial;

    /**
     * 产品单位
     */
    private String productUnit;

    /**
     * 外箱列表
     */
    private List<PdsProductManagerDetailsProsBoxVo> boxList;

    public void addBox(PdsProductManagerDetailsProsBoxVo box) {
        if (boxList == null) {
            boxList = new ArrayList<>();
        }
        boxList.add(box);
    }

}
