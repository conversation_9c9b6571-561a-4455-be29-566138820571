package com.renpho.erp.pds.client;

import com.renpho.erp.openfeign.annotation.NoToken;
import com.renpho.erp.pds.client.vo.*;
import com.renpho.karma.dto.R;
import com.renpho.karma.openfeign.FeignConfiguration;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;


/**
 * 产品管理授权-授权信息查询
 *
 * <AUTHOR>
 * @since 2025.3.14
 */
@FeignClient(name = "erp-pds", configuration = { FeignConfiguration.class })
public interface RemoteProductManagerAuthorizeService {

    @NoToken
    @PostMapping("/product/manager/authorize/view/list/psku/inner")
    R<PdsProductManagerSkuQueryViewVo> viewPskuList(@RequestBody PdsProductManagerSkuQueryViewQuery query);

}