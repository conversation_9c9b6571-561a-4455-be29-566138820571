package com.renpho.erp.pds.client.vo;

import com.renpho.karma.dto.PageQuery;
import jakarta.validation.constraints.NotEmpty;
import lombok.Data;

import java.util.List;

/**
 * 产品管理-列表-前端查询,通用的查询接口,可选的入参跟可选的出参
 *
 * <AUTHOR>
 * @since 2024.11.06
 */
@Data
public class PdsProductManagerCommonQuery extends PageQuery {

    /**
     * 指定需要返回的字段列表（例如：["purchaseSku", "id","productManagerId"]）
     */
    @NotEmpty(message = "可选出参不能为空")
    private List<String> selectedFields;

    /**
     * 产品ID
     */
    private Integer id;

    /**
     * psku模糊搜索
     */
    private String keyword;

    /**
     * 采购SKU,精准匹配
     */
    private String purchaseSku;

    /**
     * 采购SKU,精准匹配
     */
    private List<String> purchaseSkuList;

    /**
     * 中文/英文名称
     */
    private String name;

    /**
     * 三级品类ID
     */
    private Integer cateThird;

    /**
     * 颜色ID
     */
    private Integer colorId;

    /**
     * 国家地区Id
     */
    private Integer countryRegionId;

    /**
     * 销售渠道ID
     */
    private Integer salesChannelId;

    /**
     * 产品类型
     */
    private Integer productType;

    /**
     * 产品审核状态
     */
    private Integer reviewStatus;

    /**
     * 商务负责人ID
     */
    private Integer businessManagerId;

    /**
     * 采购负责人ID
     */
    private Integer procurementManagerId;

    /**
     * 计划负责人ID
     */
    private Integer planningManagerId;

    /**
     * 语言环境,详情见：LanguageEnum
     */
    private Integer language;
}