package com.renpho.erp.pds.client.vo;

import lombok.Data;

import java.util.List;

/**
 * 品牌对外查询.
 *
 * <AUTHOR>
 * @since 2024.9.29
 */
@Data
public class PdsBrandViewQuery {

    /**
     * 品牌ID集合
     */
    private List<Integer> brandIds;

    /**
     * 品牌
     */
    private String brand;

    /**
     * 品牌代码
     */
    private String code;

    /**
     * Menu Status 选项为 Active 和 Inactive 单选，默认选中Active
     */
    private Integer status;

}