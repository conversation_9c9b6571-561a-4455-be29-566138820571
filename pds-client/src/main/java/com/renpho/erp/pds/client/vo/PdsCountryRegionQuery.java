package com.renpho.erp.pds.client.vo;

import com.renpho.karma.dto.PageQuery;
import lombok.Data;

import java.util.List;

/**
 * 国家区域请求基类(列表导出).
 *
 * <AUTHOR>
 * @since 2024.9.24
 */
@Data
public class PdsCountryRegionQuery extends PageQuery {

	/**
	 * 关键字: code、name、remark都能搜索
	 */
	private String keyword;

	/**
	 * 国家代码列表，精确匹配
	 */
	private List<String> codes;

	/**
	 * 国家名称列表，精确匹配
	 */
	private List<String> names;

	/**
	 * 语言环境，1中文，0英文 , see {@link com.renpho.erp.pds.client.common.LanguageEnum}
	 */
	private Integer language;

}
