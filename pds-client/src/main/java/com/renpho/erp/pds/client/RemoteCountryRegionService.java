package com.renpho.erp.pds.client;

import com.renpho.erp.openfeign.annotation.NoToken;
import com.renpho.erp.pds.client.vo.PdsCountryRegionQuery;
import com.renpho.erp.pds.client.vo.PdsCountryRegionQuerys;
import com.renpho.karma.dto.Paging;
import com.renpho.karma.dto.R;
import com.renpho.karma.openfeign.FeignConfiguration;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import com.renpho.erp.pds.client.vo.PdsCountryRegionVo;

import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

/**
 * 国家区域对外接口定义
 *
 * <AUTHOR>
 * @since 2024.9.24
 */
@FeignClient(name = "erp-pds", configuration = { FeignConfiguration.class })
public interface RemoteCountryRegionService {

	@NoToken
	@GetMapping("/country/region/detail")
	R<PdsCountryRegionVo> detail(@RequestParam Integer id);

	@NoToken
	@PostMapping("/country/region/details")
	R<List<PdsCountryRegionVo>> details(@RequestBody List<Integer> ids);

	@NoToken
	@PostMapping("/country/region/list/inner")
	R<Paging<PdsCountryRegionVo>> page(@RequestBody PdsCountryRegionQuery query);

	@NoToken
	@PostMapping("/country/region/search")
	R<List<PdsCountryRegionVo>> search(@RequestBody PdsCountryRegionQuerys query);

}
