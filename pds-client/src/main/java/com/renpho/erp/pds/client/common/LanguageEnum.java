package com.renpho.erp.pds.client.common;

import lombok.Getter;

/**
 * 语言环境.
 * <AUTHOR>
 * @since 2024.9.20
 */
@Getter
public enum LanguageEnum {

    /**
     * success
     */
    China("中文", 1),

    /**
     * error
     */
    English("英文", 0);

    /**
     * 名称
     */
    private final String name;

    /**
     * 值
     */
    private final Integer value;

    LanguageEnum(String name, Integer value) {
        this.name = name;
        this.value = value;
    }

    /**
     * 根据编码获取对应的语言环境
     * @param value 编码
     * @return 编码获取对应的语言环境
     */
    public static LanguageEnum getEnum(Integer value) {
        for (LanguageEnum e : LanguageEnum.values()) {
            if (e.value.equals(value)) {
                return e;
            }
        }
        return LanguageEnum.China;
    }

}
