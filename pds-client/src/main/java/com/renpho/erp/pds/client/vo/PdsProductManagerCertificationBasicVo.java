package com.renpho.erp.pds.client.vo;

import com.renpho.erp.pds.client.common.FileDetailVo;
import lombok.Data;

import java.util.List;

/**
 * 产品管理-认证-基础表.
 *
 * <AUTHOR>
 * @since 2024.11.18
 */
@Data
public class PdsProductManagerCertificationBasicVo {

    /**
     * 主键ID
     */
    private Integer id;

    /**
     * 关联的产品管理主键ID
     */
    private Integer productManagerId;

    /**
     * 产品UDI
     */
    private String productUdi;

    /**
     * 外箱UDI
     */
    private String cartonUdi;

    /**
     * 备注
     */
    private String remark;

    /**
     *  认证-部件表 集合
     */
    private List<PdsProductManagerCertificationComponentVo> componentList;

    /**
     *  认证-整机表 集合
     */
    private List<PdsProductManagerCertificationMachineVo> machineList;

    /**
     * 产品封面图片详情信息
     */
    private FileDetailVo productCoverImageFileInfo;

    /**
     * PSKU信息
     */
    private String pSku;

}

