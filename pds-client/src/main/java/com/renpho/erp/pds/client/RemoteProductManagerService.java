package com.renpho.erp.pds.client;


import com.renpho.erp.openfeign.annotation.NoToken;
import com.renpho.erp.pds.client.vo.*;
import com.renpho.karma.dto.R;
import com.renpho.karma.openfeign.FeignConfiguration;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import com.renpho.karma.dto.Paging;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

/**
 * 产品管理-基础信息查询
 *
 * <AUTHOR>
 * @since 2024.9.24
 */
@FeignClient(name = "erp-pds", configuration = { FeignConfiguration.class })
public interface RemoteProductManagerService {

	/**
	 * 查看产品管理-最简单的基础信息
	 * @param query 查询条件
	 * @return 产品管理-最简单的基础信息
	 */
	@NoToken
	@PostMapping("/product/manager/view/page/inner")
	R<Paging<PdsProductManagerBasicViewVo>> viewPage(@RequestBody PdsProductManagerBasicViewQuery query);

	/**
	 * 查看产品管理-基础详情信息
	 * @param query 查询条件
	 * @return 产品管理-基础详情信息
	 */
	@NoToken
	@PostMapping("/product/manager/view/list/detail/inner")
	R<List<PdsProductManagerDetailViewVo>> viewDetailList(@RequestBody PdsProductManagerDetailViewQuery query);

	/**
	 * 查看产品管理-PSKU信息
	 * @param query 查询条件
	 * @return 产品管理-PSKU信息
	 */
	@NoToken
	@PostMapping("/product/manager/view/list/psku/inner")
	R<PdsProductManagerSkuQueryViewVo> viewPskuList(@RequestBody PdsProductManagerSkuQueryViewQuery query);

	/**
	 * 产品管理通用查询接口
	 * @param query 查询条件
	 * @return 产品管理通用查询结果
	 */
	@NoToken
	@PostMapping("/product/manager/view/list/product")
	R<Paging<PdsProductManagerCommonQueryVo>> viewCommon(@RequestBody PdsProductManagerCommonQuery query);

	/**
	 * 查看认证信息 (产品UDI\外箱UDI)
	 * @param productManagerId 产品ID
	 * @return 认证信息
	 */
	@NoToken
	@GetMapping("/product/manager/certification/get/inner")
	R<PdsProductManagerCertificationBasicVo> viewCertification(@RequestParam Integer productManagerId);

}
