package com.renpho.erp.pds.client.vo;

import com.renpho.karma.dto.PageQuery;
import lombok.Data;

import java.util.List;

/**
 * 产品管理-列表-前端查询
 *
 * <AUTHOR>
 * @since 2024.11.06
 */
@Data
public class PdsProductManagerBasicViewQuery extends PageQuery {

    /**
     * 语言环境,详情见：LanguageEnum
     */
    private Integer language;

    /** 采购SKU */
    private String purchaseSku;

    /**
     * psku模糊搜索
     */
    private String keyword;

    /** 中文/英文名称 */
    private String name;

    /** 三级品类ID */
    private Integer cateThird;

    /** 颜色ID */
    private Integer colorId;

    /** 国家地区Id */
    private Integer countryRegionId;

    /** 销售渠道ID */
    private Integer salesChannelId;

    /** 产品类型 */
    private Integer productType;

    /**
     * PSKU
     */
    private List<String> purchaseSkuList;

    /**
     * 产品审核状态
     */
    private Integer reviewStatus;

    /**
     * 商务负责人ID
     */
    private Integer businessManagerId;

    /**
     * 采购负责人ID
     */
    private Integer procurementManagerId;

    /**
     * 计划负责人ID
     */
    private Integer planningManagerId;

}