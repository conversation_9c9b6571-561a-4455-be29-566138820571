package com.renpho.erp.pds.client.vo;

import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.List;
import java.util.Collection;

/**
 * 产品管理-基础信息，主数据-前端
 *
 * <AUTHOR>
 * @since 2024.11.05
 */
@Data
public class PdsProductManagerBasicViewVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    private Integer id;

    /**
     * 产品封面图片
     */
    private String productCoverImageId;

    /**
     * 产品封面图片URL
     */
    private String productCoverImageUrl;

    /**
     * 国家/地区ID
     */
    private Integer countryRegionId;

    /**
     * 产品型号ID
     */
    private Integer productModelId;

    /**
     * 产品型号
     */
    private String productModelNo;

    /**
     * 品牌ID
     */
    private Integer brandId;

    /**
     * 品牌编码
     */
    private String brandCode;

    /**
     * 品牌名称
     */
    private String brandName;


    /**
     * 该品牌已使用到的SN码
     */
    private String lastSnCode;


    /**
     * SN码生成系数
     */
    private Integer snCoefficient;


    /**
     * 品类ID，名称多语言表中查询
     */
    private Integer categoryId;

    /**
     * 品类多语言
     */
    private Collection<LanguageCategoryResp> languageCategories;

    /**
     * 颜色ID
     */
    private Integer colorId;

    /**
     * 属性编码
     */
    private String attributeEncoding;

    /**
     * 销售渠道ID
     */
    private Integer salesChannelId;

    /**
     * 版本编号
     */
    private String version;

    /**
     * PSKU
     */
    private String purchaseSku;

    /**
     * 产品类型
     */
    private Integer productType;

    /**
     * 中文名称
     */
    private String chineseName;

    /**
     * 英文名称
     */
    private String englishName;

    /**
     * 产品描述，text格式
     */
    private List<String> descriptionList;

    /**
     * 审核状态:0,草稿; 1,待审核;2,审核通过;3,审核不通过
     */
    private Integer reviewStatus;

    /**
     * 审核状态名称
     */
    private String reviewStatusName;

    /**
     * 授权状态: 0待授权、1待确认、2已授权
     */
    private Integer authorizeStatus;

    /**
     * 产品负责人ID
     */
    private Integer productManagerId;

    /**
     * 商务负责人ID
     */
    private Integer businessManagerId;

    /**
     * 采购负责人ID
     */
    private Integer procurementManagerId;

    /**
     * 计划负责人ID
     */
    private Integer planningManagerId;

    /**
     * 合规负责人ID
     */
    private Integer complianceManagerId;

    /**
     * 包材负责人ID
     */
    private Integer packingMaterialManagerId;
}
