package com.renpho.erp.pds.client;

import com.renpho.erp.openfeign.annotation.NoToken;
import com.renpho.erp.pds.client.vo.PdsCategoryNameViewQuery;
import com.renpho.erp.pds.client.vo.PdsCategoryNameViewVo;
import com.renpho.erp.pds.client.vo.PdsCategoryViewQuery;
import com.renpho.erp.pds.client.vo.PdsCategoryTreeViewVo;
import com.renpho.karma.dto.R;
import com.renpho.karma.openfeign.FeignConfiguration;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

/**
 * 品类对外接口定义
 *
 * <AUTHOR>
 * @since 2024.9.24
 */
@FeignClient(name = "erp-pds", configuration = { FeignConfiguration.class })
public interface RemoteCategroyService {

	@NoToken
	@PostMapping("/category/view/list")
	R<List<PdsCategoryTreeViewVo>> viewList(@RequestBody PdsCategoryViewQuery pdsCategoryViewQuery);

	@NoToken
	@PostMapping("/category/view/names")
	R<PdsCategoryNameViewVo> viewNames(@RequestBody PdsCategoryNameViewQuery nameViewQuery);

}
