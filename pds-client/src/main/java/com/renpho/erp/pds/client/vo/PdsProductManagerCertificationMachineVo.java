package com.renpho.erp.pds.client.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.renpho.erp.pds.client.common.FileDetailVo;
import lombok.Data;

import java.util.List;

/**
 * 产品管理-认证-整机表.
 *
 * <AUTHOR>
 * @since 2024.11.18
 */
@Data
public class PdsProductManagerCertificationMachineVo {

    /**
     * 主键ID
     */
    private Integer id;

    /**
     * 关联的产品管理主键ID
     */
    private Integer productManagerId;

    /**
     * 认证名称
     */
    private String certificationName;

    /**
     * 认证标准
     */
    private String certificationStandard;

    /**
     * 有效时间（开始）
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private java.util.Date effectiveTimeStart;

    /**
     * 有效时间（结束）
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private java.util.Date effectiveTimeEnd;

    /**
     * 附件,最多10个附件
     */
    private transient String attachmentIds;

    /**
     * 附件,最多10个附件
     */
    private List<String> attachmentIdList;

    /**
     * 附件,最多10个附件
     */
    private List<FileDetailVo> attachmentFileInfoList;

    /**
     * 备注
     */
    private String remark;
}

