package com.renpho.erp.pds.client.vo;

import lombok.Data;

/**
 * 产品管理-详情-产品属性-规格参数-规格参数值
 */
@Data
public class PdsProductManagerDetailsProsBoxVo {

    /**
     * 外箱规格ID
     */
    private Integer id;

    /**
     * 关联的产品管理属性信息ID
     */
    private Integer productManagerProsId;

    /**
     * 箱规名称
     */
    private String boxSpecificationName;

    /**
     * 装箱数量
     */
    private Integer numberOfUnitsPerBox;

    /**
     * 外箱尺寸-长 (cm)/公制
     */
    private Double boxLengthMetric;

    /**
     * 外箱尺寸-宽 (cm)/公制
     */
    private Double boxWidthMetric;

    /**
     * 外箱尺寸-高 (cm)/公制
     */
    private Double boxHeightMetric;

    /**
     * 整箱毛重 (kg)/公制
     */
    private Double grossWeightPerBoxMetric;

    /**
     * 外箱尺寸-长 (inch)/英制
     */
    private Double boxLengthImperial;

    /**
     * 外箱尺寸-宽 (inch)/英制
     */
    private Double boxWidthImperial;

    /**
     * 外箱尺寸-高 (inch)/英制
     */
    private Double boxHeightImperial;

    /**
     * 整箱毛重 (lb)/英制
     */
    private Double grossWeightPerBoxImperial;

    /**
     * 是否为生效外箱规格: 0=InActive，1=Active
     */
    private Integer active;

}
