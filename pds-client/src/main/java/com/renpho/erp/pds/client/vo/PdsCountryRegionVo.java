package com.renpho.erp.pds.client.vo;

import com.renpho.erp.pds.client.common.LanguageParent;
import lombok.Data;

import java.util.List;

/**
 * 国家区域表.
 *
 * <AUTHOR>
 * @since 2024.9.24
 */
@Data
public class PdsCountryRegionVo {

	/**
	 * 主键, 添加不需要，更新才需要
	 */
	private Integer id;

	/**
	 * 国家区域代码
	 */
	private String code;

	/**
	 * 多语言
	 */
	private List<LanguageParent> names;

	/**
	 * 解析完多语言之后的名称（已经跟请求语言环境绑定）
	 */
	private String name;

	/**
	 * 备注
	 */
	private String remark;

	/**
	 * Menu Status 选项为 Active 和 Inactive 单选，默认选中Active
	 */
	private Integer status;

}
