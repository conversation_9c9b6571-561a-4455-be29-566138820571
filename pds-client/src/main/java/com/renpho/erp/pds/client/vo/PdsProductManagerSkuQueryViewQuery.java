package com.renpho.erp.pds.client.vo;

import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.util.List;

/**
 * 产品管理-List<ID> -> List<SKU>
 * <br/> 查询产品管理，传入用户ID数组，输出匹配负责人信息的psku集合
 *
 * <AUTHOR>
 * @since 2024.11.06
 */
@Data
public class PdsProductManagerSkuQueryViewQuery {

    /**
     * 用户ID集合，集合大小是0不限制，集合大小1，查询对应产品的psku集合
     */
    @NotNull
    private List<Integer> idList;

}