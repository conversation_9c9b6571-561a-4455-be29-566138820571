package com.renpho.erp.tms.infrastructure.persistence.transferorder.repository;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.renpho.erp.tms.domain.exception.BusinessException;
import com.renpho.erp.tms.domain.inbound.WarehouseProviderType;
import com.renpho.erp.tms.domain.operator.Operator;
import com.renpho.erp.tms.domain.operator.OperatorId;
import com.renpho.erp.tms.domain.transferorder.*;
import com.renpho.erp.tms.domain.transportorder.ApprovalStatus;
import com.renpho.erp.tms.domain.transportrequest.SyncApiStatus;
import com.renpho.erp.tms.infrastructure.persistence.transferorder.mapper.TransferOrderMapper;
import com.renpho.erp.tms.infrastructure.persistence.transferorder.po.TransferOrderPO;
import com.renpho.erp.tms.infrastructure.persistence.transferorder.po.converter.TransferOrderConverter;
import com.renpho.erp.tms.infrastructure.remote.user.repository.OperatorLookup;
import lombok.RequiredArgsConstructor;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

/**
 * 调拨单仓储实现
 *
 * <AUTHOR>
 * @since 2025/8/22
 */
@Repository
@RequiredArgsConstructor
public class TransferOrderRepositoryImpl extends ServiceImpl<TransferOrderMapper, TransferOrderPO> implements TransferOrderRepository {

    private final TransferOrderConverter transferOrderConverter;
    private final OperatorLookup operatorLookup;
    private final TransferOrderItemRepository transferOrderItemRepository;
    private final TransferOrderCustomerRepository transferOrderCustomerRepository;
    private final TransferOrderLookup transferOrderLookup;
    private final TransferOrderStatusHistoryRepository transferOrderStatusHistoryRepository;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public TransferOrder add(TransferOrder command) {
        TransferOrderPO po = transferOrderConverter.toPO(command);
        save(po);
        TransferOrder domain = transferOrderConverter.toDomain(po);
        transferOrderItemRepository.add(domain);
        transferOrderCustomerRepository.add(domain);
        // TODO 插入批注
        transferOrderStatusHistoryRepository.addHistoryStatus(domain.getId().id(), domain.getTsNo(), domain.getStatus().getValue());
        return domain;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public TransferOrder update(TransferOrder command) {
        TransferOrderPO po = transferOrderConverter.toPO(command);
        updateById(po);
        TransferOrder domain = transferOrderConverter.toDomain(po);
        transferOrderItemRepository.update(domain);
        transferOrderCustomerRepository.update(domain);
        return domain;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void batchSave(List<TransferOrder> commands) {
        List<TransferOrderPO> pos = transferOrderConverter.toPOs(commands);
        saveBatch(pos);
    }

    @Override
    public void updateById(TransferOrder ts) {
        updateById(transferOrderConverter.toPO(ts));
    }

    @Override
    public List<TransferOrderData> selectTsListByShipStatusList(List<TransferOrderStatus> shipStatusList, WarehouseProviderType warehouseType) {
        List<Integer> statusList = shipStatusList.stream().map(TransferOrderStatus::getValue).toList();
        List<TransferOrderData> list = this.baseMapper.selectTsListByShipStatusList(statusList, warehouseType.name());
        if (CollectionUtils.isNotEmpty(list)) {
            return list;
        }

        return List.of();
    }

    @Override
    public void clearShipmentIdById(TransferOrderId id) {
        Integer updateBy = Optional.ofNullable(operatorLookup.findSystemUser())
                .map(Operator::getOperatorId)
                .map(OperatorId::id)
                .orElse(0);

        lambdaUpdate().eq(TransferOrderPO::getId, id)
                .set(TransferOrderPO::getShipmentId, "")
                .set(TransferOrderPO::getUpdateBy, updateBy)
                .set(TransferOrderPO::getSyncApiStatus, SyncApiStatus.SUCCESS)
                .set(TransferOrderPO::getUpdateTime, LocalDateTime.now())
                .update();
    }

    @Override
    public void updateSyncApiStatusById(TransferOrderId id, SyncApiStatus syncApiStatus) {
        Integer updateBy = Optional.ofNullable(operatorLookup.findSystemUser())
                .map(Operator::getOperatorId)
                .map(OperatorId::id)
                .orElse(0);

        lambdaUpdate().eq(TransferOrderPO::getId, id)
                .set(TransferOrderPO::getSyncApiStatus, syncApiStatus)
                .set(TransferOrderPO::getUpdateBy, updateBy)
                .set(TransferOrderPO::getUpdateTime, LocalDateTime.now())
                .update();
    }

    @Override
    public void updateSyncApiStatus(String tsNo, SyncApiStatus syncApiStatus) {

        lambdaUpdate().eq(TransferOrderPO::getTsNo, tsNo)
                .set(TransferOrderPO::getSyncApiStatus, syncApiStatus.name())
                .set(TransferOrderPO::getUpdateTime, LocalDateTime.now())
                .update();
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateApprovalResultByInstanceId(TransferOrderId id, TransferOrderStatus status, Integer updateBy) {
        lambdaUpdate()
                .eq(TransferOrderPO::getId, id.id())
                .set(TransferOrderPO::getStatus, status.getValue())
                .set(TransferOrderPO::getUpdateTime, LocalDateTime.now())
                .set(TransferOrderPO::getUpdateBy, updateBy)
                .update();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public TransferOrder cancel(TransferOrder command) {
        Integer id = command.getId().id();
        TransferOrderStatus status = TransferOrderStatus.CANCEL;
        command.setStatus(status);
        lambdaUpdate()
                .eq(TransferOrderPO::getId, id)
                .set(TransferOrderPO::getStatus, status.getValue())
                .update();
        transferOrderStatusHistoryRepository.addHistoryStatus(id, command.getTsNo(), status.getValue());
        return command;
    }

    @Override
    public TransferOrder findByTsNo(String tsNo) {
        TransferOrderPO po = lambdaQuery().eq(TransferOrderPO::getTsNo, tsNo).one();
        return transferOrderConverter.toDomain(po);
    }

    @Override
    public void updateShipmentIdInfo(String shipmentId, String referenceId, List<String> fileIds, TransferOrderId id){
        lambdaUpdate()
                .eq(TransferOrderPO::getId, id.id())
                .set(TransferOrderPO::getShipmentId, shipmentId)
                .set(TransferOrderPO::getReferenceId, referenceId)
                .set(TransferOrderPO::getCartonLabelFileIds, fileIds)
                .set(TransferOrderPO::getStatus, TransferOrderStatus.WAIT_CREATE_SHIPMENT.getValue())
                .update();
    }

}
