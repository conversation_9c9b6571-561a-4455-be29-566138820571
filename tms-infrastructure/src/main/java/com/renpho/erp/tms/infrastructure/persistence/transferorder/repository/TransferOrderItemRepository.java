package com.renpho.erp.tms.infrastructure.persistence.transferorder.repository;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.renpho.erp.tms.domain.transferorder.TransferOrder;
import com.renpho.erp.tms.domain.transferorder.TransferOrderId;
import com.renpho.erp.tms.domain.transferorder.TransferOrderItem;
import com.renpho.erp.tms.domain.transferorder.TransferOrderQuery;
import com.renpho.erp.tms.infrastructure.persistence.transferorder.mapper.TransferOrderItemMapper;
import com.renpho.erp.tms.infrastructure.persistence.transferorder.po.TransferOrderItemPO;
import com.renpho.erp.tms.infrastructure.persistence.transferorder.po.converter.TransferOrderItemConverter;
import com.renpho.erp.tms.infrastructure.persistence.transportorder.po.TransportOrderPO;
import lombok.RequiredArgsConstructor;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 调拨单查询实现
 *
 * <AUTHOR>
 * @since 2025/8/22
 */
@Repository
@RequiredArgsConstructor
public class TransferOrderItemRepository extends ServiceImpl<TransferOrderItemMapper, TransferOrderItemPO> {

    private final TransferOrderItemConverter transferOrderItemConverter;

    public List<TransferOrderItem> findByTsId(TransferOrderId tsId) {
        Optional<Integer> id = Optional.ofNullable(tsId).map(TransferOrderId::id);
        if (id.isEmpty()) {
            return List.of();
        }
        List<TransferOrderItemPO> pos = lambdaQuery().eq(TransferOrderItemPO::getTsId, id.get()).list();
        return transferOrderItemConverter.toDomains(pos);
    }

    public Map<TransferOrderId, List<TransferOrderItem>> findByTsIds(Collection<TransferOrderId> tsId) {
        Collection<Integer> ids = CollectionUtils.emptyIfNull(tsId).stream().filter(Objects::nonNull)
                .map(TransferOrderId::id).filter(Objects::nonNull).collect(Collectors.toSet());
        if (CollectionUtils.isEmpty(ids)) {
            return Map.of();
        }
        List<TransferOrderItemPO> pos = lambdaQuery().in(TransferOrderItemPO::getTsId, ids).list();
        return transferOrderItemConverter.toDomains(pos)
                .stream()
                .collect(Collectors.groupingBy(TransferOrderItem::getTsId));
    }

    public List<TransferOrderItem> findByPsku(String psku) {
        if (StringUtils.isBlank(psku)) {
            return List.of();
        }
        List<TransferOrderItemPO> pos = lambdaQuery().eq(TransferOrderItemPO::getPsku, psku).list();
        return transferOrderItemConverter.toDomains(pos);
    }

    @Transactional(rollbackFor = Exception.class)
    public void add(TransferOrder domain) {
        List<TransferOrderItemPO> pos = transferOrderItemConverter.toPOs(domain);
        saveBatch(pos);
        List<TransferOrderItem> domains = transferOrderItemConverter.toDomains(pos);
        domain.setItems(domains);
    }

    @Transactional(rollbackFor = Exception.class)
    public void update(TransferOrder domain) {
        lambdaUpdate().eq(TransferOrderItemPO::getTsId, domain.getId().id()).remove();
        List<TransferOrderItem> items = domain.getItems();
        for (TransferOrderItem item : items) {
            item.updateByTransferOrder(domain);
        }
        List<TransferOrderItemPO> pos = transferOrderItemConverter.toPOs(items);
        saveBatch(pos);
        List<TransferOrderItem> domains = transferOrderItemConverter.toDomains(pos);
        domain.setItems(domains);
    }

    @Transactional(rollbackFor = Exception.class)
    public void updateOutBoundStatus(TransferOrderItem item) {
        lambdaUpdate().eq(TransferOrderItemPO::getId, item.getId())
                .set(TransferOrderItemPO::getOutboundStatus, item.getOutboundStatus())
                .set(TransferOrderItemPO::getOutboundTime, item.getOutboundTime())
                .set(TransferOrderItemPO::getUpdateTime, LocalDateTime.now())
                .update();
    }

    public List<Integer> findByPageQuery(TransferOrderQuery condition) {
        List<TransferOrderItemPO> pos = lambdaQuery()
                .like(StringUtils.isNotBlank(condition.getPsku()), TransferOrderItemPO::getPsku, condition.getPsku())
                .like(StringUtils.isNotBlank(condition.getFnsku()), TransferOrderItemPO::getFnsku, condition.getFnsku())
                .eq(Objects.nonNull(condition.getIsBorrowed()), TransferOrderItemPO::getIsBorrowed, condition.getIsBorrowed())
                .eq(Objects.nonNull(condition.getIsRelabel()), TransferOrderItemPO::getIsRelabel, condition.getIsRelabel())
                .like(StringUtils.isNotBlank(condition.getOutboundNo()), TransferOrderItemPO::getOutboundNo, condition.getOutboundNo())
                .list();
        if (CollectionUtils.isEmpty(pos)) {
            return List.of();
        }
        return pos.stream().map(TransferOrderItemPO::getTsId).toList();
    }

    @Transactional(rollbackFor = Exception.class)
    public void batchUpdate(List<TransferOrderItem> items) {

        List<TransferOrderItemPO> pos = transferOrderItemConverter.toPOs(items);
        updateBatchById(pos);
    }

    @Transactional(rollbackFor = Exception.class)
    public void clearOutboundNoByTsId(TransferOrderId id) {
        lambdaUpdate().eq(TransferOrderItemPO::getTsId, id.id()).set(TransferOrderItemPO::getOutboundNo, null).update();
    }

    public List<TransferOrder> findByTransferOrders(Collection<TransferOrder> domains) {
        Set<Integer> ids = CollectionUtils.emptyIfNull(domains).stream()
                .filter(Objects::nonNull).map(TransferOrder::getId)
                .filter(Objects::nonNull).map(TransferOrderId::id)
                .filter(Objects::nonNull).collect(Collectors.toSet());
        List<TransferOrderItemPO> pos = lambdaQuery().in(TransferOrderItemPO::getTsId, ids).list();
        Map<TransferOrderId, List<TransferOrderItem>> items = CollectionUtils.emptyIfNull(pos)
                .stream()
                .map(transferOrderItemConverter::toDomain)
                .collect(Collectors.groupingBy(TransferOrderItem::getTsId));
        for (TransferOrder d : domains) {
            d.setItems(new ArrayList<>(items.get(d.getId())));
        }
        return new ArrayList<>(domains);
    }

}
