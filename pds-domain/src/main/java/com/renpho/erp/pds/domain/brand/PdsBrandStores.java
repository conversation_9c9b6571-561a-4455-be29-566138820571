package com.renpho.erp.pds.domain.brand;

import lombok.Data;

/**
 * 品牌店铺数据.
 *
 * <AUTHOR>
 * @since 2024.9.14
 */
@Data
public class PdsBrandStores {

	/**
	 * 主键
	 */
	private Integer id;

	/**
	 * 品牌主键ID
	 */
	private Integer brandId;

	/**
	 * 店铺主键ID
	 */
	private Integer storeId;

	/**
	 * 构建实例
	 * @param brandId brandId
	 * @param storeId storeId
	 */
	public PdsBrandStores(Integer brandId, Integer storeId) {
		this.brandId = brandId;
		this.storeId = storeId;
	}

	public PdsBrandStores() {

	}

}
