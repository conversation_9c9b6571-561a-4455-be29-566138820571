package com.renpho.erp.pds.domain.common;

import cn.hutool.core.util.StrUtil;
import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum ChannelType {
    ONLINE_PLATFORM(0, "线上平台"),
    OFFLINE_B2B(1, "线下B2B"),
    SPF_INDEPENDENT_STATION(2, "SPF独立站"),
    ;
    /**
     * 值
     */
    private Integer value;
    /**
     * 名称
     */
    private String name;

    public static ChannelType getChannelType(Integer value) {
        for (ChannelType channelType : ChannelType.values()) {
            if (channelType.getValue().equals(value)) {
                return channelType;
            }
        }
        return null;
    }

    public static ChannelType getByName(String name) {
        for (ChannelType channelType : ChannelType.values()) {
            if (StrUtil.equals(name, channelType.getName())) {
                return channelType;
            }
        }
        return null;
    }
}
