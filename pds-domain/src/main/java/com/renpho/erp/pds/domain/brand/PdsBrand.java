package com.renpho.erp.pds.domain.brand;

import cn.hutool.core.date.DateUtil;
import lombok.Data;
import lombok.RequiredArgsConstructor;
import org.jmolecules.ddd.types.AggregateRoot;
import org.jmolecules.ddd.types.Identifiable;
import org.jmolecules.ddd.types.Identifier;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

/**
 * 品牌集合根.
 *
 * <AUTHOR>
 * @since 2024.9.14
 */
@Data
public class PdsBrand implements AggregateRoot<PdsBrand, PdsBrand.PdsBrandID> {

    private PdsBrandID id;

    /**
     * 品牌
     */
    private String brand;

    /**
     * 品牌代码
     */
    private String code;

    /**
     * 注册实体
     */
    private String registeredEntity;

    /**
     * 注册时间
     */
    private LocalDateTime registeredDate;

    /**
     * 最后序列号：该品牌已使用到的SN码
     */
    private String lastSnCode;

    /**
     * 备注
     */
    private String remark;

    /**
     * 参数状态 1：Active，0：Disabled 单选框，必选，默认Active
     */
    private Integer status;

    /**
     * 品牌店铺数据
     */
    private final List<PdsBrandStores> brandStoresList = new ArrayList<PdsBrandStores>();

    /**
     * 关联ID集合，主要用于查询
     */
    private List<Integer> storeIds;

    public PdsBrand() {

    }

    /**
     * 添加品牌店铺数据
     *
     * @param brandStores 品牌店铺数据
     */
    public void addPdsBrandStores(PdsBrandStores brandStores) {
        this.brandStoresList.add(brandStores);
    }

    @RequiredArgsConstructor(staticName = "of")
    public static class PdsBrandID implements Identifiable<Integer>, Identifier {

        private final Integer id;

        @Override
        public Integer getId() {
            return id;
        }

    }


    public String initLastSnCode(){
        if(this.lastSnCode == null || "".equals(this.lastSnCode)){
            String year = String.valueOf(DateUtil.thisYear()).substring(3, 4);
            return this.code + year + "0000000";
        }
        return this.lastSnCode;
    }
}
