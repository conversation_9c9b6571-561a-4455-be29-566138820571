package com.renpho.erp.pds.domain.brand;

import java.util.List;

/**
 * 品牌操作接口.
 *
 * <AUTHOR>
 * @since 2024.9.14
 */
public interface PdsBrandRepository {

	/**
	 * 添加品牌数据
	 * @param pdsBrand 品牌数据
	 * @return 主键id
	 */
	Integer savePdsBrand(PdsBrand pdsBrand);

	/**
	 * 更新品牌数据
	 * @param pdsBrand 品牌数据
	 * @return 受影响条数
	 */
	Integer updatePdsBrand(PdsBrand pdsBrand);

	/**
	 * 添加品牌店铺数据
	 * @param pdsBrandStores 品牌店铺数据
	 * @return 受影响条数
	 */
	Integer savePdsBrandStores(PdsBrandStores pdsBrandStores);

	/**
	 * 删除品牌店铺数据
	 * @param pdsBrandStores 品牌店铺数据
	 * @return 受影响条数
	 */
	Integer removePdsBrandStores(PdsBrandStores pdsBrandStores);

	/**
	 * 根据品牌ID获取关联的店铺数据
	 * @param pdsBrandID 品牌ID
	 * @return 关联的店铺数据
	 */
	List<PdsBrandStores> findAllPdsBrandStores(Integer pdsBrandID);

	/**
	 * 更新店铺关联关系
	 * @param storeIds 新的店铺关联数据
	 * @param brandId 品牌主键
	 * @return 受影响条数
	 */
	Integer updatePdsBrandStores(List<Integer> storeIds, Integer brandId);

	boolean updateLastSnCode(PdsBrand pdsBrand);
}
