package com.renpho.erp.pds;

import com.renpho.erp.pds.client.RemoteCountryRegionService;
import com.renpho.erp.pds.client.vo.PdsCountryRegionQuery;
import com.renpho.erp.pds.client.vo.PdsCountryRegionQuerys;
import com.renpho.erp.pds.client.vo.PdsCountryRegionVo;
import com.renpho.karma.dto.Paging;
import com.renpho.karma.dto.R;
import org.assertj.core.api.Assertions;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

import java.util.List;

@SpringBootTest
class RemoteCountryRegionFeignTest {

    @Autowired
    private RemoteCountryRegionService remoteCountryRegionService;

    @Test
    void search() {
        PdsCountryRegionQuerys query = new PdsCountryRegionQuerys();
        query.setCodes(List.of("J", "I"));
        R<List<PdsCountryRegionVo>> list = remoteCountryRegionService.search(query);
        Assertions.assertThat(list)
                .isNotNull();
    }

    @Test
    void page(){
        PdsCountryRegionQuery query = new PdsCountryRegionQuery();
        query.setCodes(List.of("J", "I"));

        R<Paging<PdsCountryRegionVo>> page = remoteCountryRegionService.page(query);
        Assertions.assertThat(page).isNotNull();

    }

}
