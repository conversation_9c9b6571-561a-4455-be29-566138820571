package com.renpho.erp.pds;

import com.renpho.erp.pds.client.RemoteCategroyService;
import com.renpho.erp.pds.client.RemoteCountryRegionService;
import com.renpho.erp.pds.client.vo.PdsCategoryNameViewQuery;
import com.renpho.erp.pds.client.vo.PdsCategoryNameViewVo;
import com.renpho.erp.pds.client.vo.PdsCountryRegionQuerys;
import com.renpho.erp.pds.client.vo.PdsCountryRegionVo;
import com.renpho.erp.pds.domain.common.LanguageEnum;
import com.renpho.karma.dto.R;
import org.assertj.core.api.Assertions;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

import java.util.ArrayList;
import java.util.List;

@SpringBootTest
class RemoteCategoryFeignTest {

    @Autowired
    private RemoteCategroyService remoteCategroyService;

    @Test
    void search() {
        PdsCategoryNameViewQuery query = new PdsCategoryNameViewQuery();
        query.setLanguage(LanguageEnum.English.getValue());
        query.setCategoryId(7);

        List<Integer> categoryIds = new ArrayList<>();
        categoryIds.add(7);
        categoryIds.add(10);
        query.setCategoryIds(categoryIds);

        R<PdsCategoryNameViewVo> res = remoteCategroyService.viewNames(query);
        Assertions.assertThat(res)
                .isNotNull();
    }
}
