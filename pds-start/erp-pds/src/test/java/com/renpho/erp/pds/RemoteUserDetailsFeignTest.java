package com.renpho.erp.pds;

import com.renpho.erp.pds.domain.common.CommonConstants;
import com.renpho.erp.pds.infrastructure.feignclient.RemoteUserDetailsFeign;
import com.renpho.erp.smc.client.dto.OumLabelUserSimpleVo;
import com.renpho.erp.smc.client.dto.QueryOumLabelUserCmd;
import com.renpho.karma.dto.R;
import org.assertj.core.api.Assertions;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

import java.util.List;

@SpringBootTest
public class RemoteUserDetailsFeignTest {

    @Autowired
    RemoteUserDetailsFeign remoteUserDetails;

    /**
     * 标签：
     * 1.运营管理  6（字典）
     * 2.运营人员  7（字典）
     */
    @Test
    void test() {
        QueryOumLabelUserCmd cmd = new QueryOumLabelUserCmd();
        cmd.setUserId(6);
        cmd.setStatus(String.valueOf(CommonConstants.ACTIVE));
        cmd.setRoleLabel(CommonConstants.LABEL_PM);

        R<List<OumLabelUserSimpleVo>> datas = remoteUserDetails.getLabelSimpleList(cmd);

        Assertions.assertThat(datas)
                .isNotNull();
    }

}
