package com.renpho.erp.pds;

import com.renpho.erp.pds.client.RemoteCategroyService;
import com.renpho.erp.pds.client.RemoteProductManagerAuthorizeService;
import com.renpho.erp.pds.client.RemoteProductManagerService;
import com.renpho.erp.pds.client.vo.*;
import com.renpho.erp.pds.domain.common.LanguageEnum;
import com.renpho.karma.dto.Paging;
import com.renpho.karma.dto.R;
import jakarta.annotation.Resource;
import org.assertj.core.api.Assertions;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.ArrayList;
import java.util.List;

@SpringBootTest
class RemoteProductManagerFeignTest {

    @Resource
    private RemoteProductManagerService remoteProductManagerService;

    @Resource
    private RemoteProductManagerAuthorizeService remoteProductManagerAuthorizeService;

    @Test
    void commonSearch(){
        PdsProductManagerCommonQuery query = new PdsProductManagerCommonQuery();

        // 可选的出参，必填
        List<String> selectedFields = new ArrayList<>();
        selectedFields.add("id");
        selectedFields.add("purchaseSku");
        selectedFields.add("productManagerId");
        query.setSelectedFields(selectedFields);

        // 主要的搜索条件
        query.setId(198);

        Paging<PdsProductManagerCommonQueryVo> rPage = remoteProductManagerService.viewCommon(query).getData();
        Assertions.assertThat(rPage).isNotNull();
    }

    @Test
    void searchUDI(){
        Integer productId = 164;
        PdsProductManagerCertificationBasicVo certification = remoteProductManagerService.viewCertification(productId).getData();
        Assertions.assertThat(certification).isNotNull();
    }

    @Test
    void search() {
        PdsProductManagerDetailViewQuery query = new PdsProductManagerDetailViewQuery();
//        List<String> skuList = new ArrayList<>();
//        skuList.add("PUS-ES-28ML-BK-FF07");
//        skuList.add("PUS-ES-28ML-BK-FF08");
//        skuList.add("PUS-ES-28ML-BK-FF09");
//        query.setPSkuList(skuList);

//        List<Integer> productIdList = new ArrayList<>();
//        productIdList.add(13);
//        productIdList.add(14);
//
//        query.setProductIdList(productIdList);

        // 审核通过的
        query.setReviewStatus(2);

        R<List<PdsProductManagerDetailViewVo>> res = remoteProductManagerService.viewDetailList(query);
        Assertions.assertThat(res).isNotNull();
    }

    @Test
    void getByIdList() {
        PdsProductManagerSkuQueryViewQuery query = new PdsProductManagerSkuQueryViewQuery();
        List<Integer> idList = new ArrayList<>();
        idList.add(2);
        idList.add(3308);
        query.setIdList(idList);
        R<PdsProductManagerSkuQueryViewVo> res = remoteProductManagerService.viewPskuList(query);
        Assertions.assertThat(res).isNotNull();

        R<PdsProductManagerSkuQueryViewVo> resAuthorize = remoteProductManagerAuthorizeService.viewPskuList(query);
        Assertions.assertThat(resAuthorize).isNotNull();

        query.setIdList(new ArrayList<>());
        R<PdsProductManagerSkuQueryViewVo> resAll = remoteProductManagerService.viewPskuList(query);
        Assertions.assertThat(resAll).isNotNull();
    }

}
