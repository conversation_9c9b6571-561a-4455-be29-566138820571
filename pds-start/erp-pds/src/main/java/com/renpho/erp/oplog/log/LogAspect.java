//package com.renpho.erp.oplog.log;
//
//import java.util.Date;
//
//import com.alibaba.fastjson.JSON;
//import com.alibaba.fastjson.JSONObject;
//import com.alibaba.nacos.shaded.com.google.gson.JsonObject;
//import com.renpho.erp.oplog.log.repo.AsyncLogRepository;
//import com.renpho.erp.oplog.log.repo.SysOperLog;
//import com.renpho.erp.pds.application.utils.JsonUtils;
//import com.renpho.erp.security.service.PlatformUser;
//import com.renpho.erp.security.util.SecurityUtils;
//import com.renpho.karma.web.servlet.mvc.context.ServletUtils;
//
//import org.aspectj.lang.JoinPoint;
//import org.aspectj.lang.annotation.AfterReturning;
//import org.aspectj.lang.annotation.AfterThrowing;
//import org.aspectj.lang.annotation.Aspect;
//import org.aspectj.lang.annotation.Before;
//import org.slf4j.MDC;
//import org.springframework.beans.BeanUtils;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.core.env.Environment;
//import org.springframework.http.HttpMethod;
//import org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestWrapper;
//import org.springframework.stereotype.Component;
//
//import cn.hutool.extra.spring.SpringUtil;
//import jakarta.servlet.http.HttpServletRequest;
//import lombok.extern.slf4j.Slf4j;
//import org.springframework.web.multipart.MultipartFile;
//
///**
// * 操作日志记录处理.
// *
// * <AUTHOR>
// */
//@Aspect
//@Component
//@Slf4j
//
//public class LogAspect {
//
//    /** traceId */
//    public static final String TLOG_TRACE_KEY = "tlogTraceId";
//
//    /** 备注内容-可选 */
//    public static final String EXCHANGE = "log.direct";
//
//    /** 备注内容-可选 */
//    public static final String ROUTING_KEY = "log.save.routing.key";
//
//    /** 备注内容-可选 */
//    public static final String LOG_OLD_DATA = "log_old_data";
//
//    /** 备注内容-可选 */
//    public static final String LOG_NEW_DATA = "log_new_data";
//
//    /** 备注内容-可选 */
//    public static final String LOG_OLD_DATA_LIST = "log_old_data_list";
//
//    /** 备注内容-可选 */
//    public static final String LOG_NEW_DATA_LIST = "log_new_data_list";
//
//    /** 备注内容-可选 */
//    public static final String ORDER_NO = "order_no";
//
//    /** 备注内容-可选 */
//    public static final String ORDER_NO_LIST = "order_no_list";
//
//    /** 备注内容-可选 */
//    public static final String BUSINESS_TYPE = "business_type";
//
//    /** 备注内容-可选 */
//    public static final String REMARK = "remark";
//
//    @Autowired
//    private AsyncLogRepository logRepository;
//
//    @Autowired
//    private Environment env;
//
//    /**
//     * 处理完请求后执行
//     * @param joinPoint 切点
//     */
//    @AfterReturning(pointcut = "@annotation(controllerLog)", returning = "jsonResult")
//    public void doAfterReturning(JoinPoint joinPoint, OpLog controllerLog, Object jsonResult) {
//        Class<? extends SnapshotDatatSource> snapt = controllerLog.snaptSource();
//        if (snapt != null) {
//            SnapshotDatatSource snaptData = SpringUtil.getBean(snapt);
//            Object[] args = joinPoint.getArgs();
//            if (args.length == 0) {
//                ContextHandler.set(ContextHandler.LOG_NEW_DATA, new JSONObject());
//            }
//            else {
//                JSONObject res = null;
//                String bsId = null;
//                if (jsonResult instanceof Void) {
//                    res = snaptData.getNewData(args, null);
//                    bsId = snaptData.getBsId(args, null);
//                }
//                else {
//                    //TODO 这里做了特殊处理，返回数据不一定都是json格式的，这里做了特殊处理
//                    JSONObject result = null;
//                    try{
//                        result = (JSONObject) JSONObject.toJSON(jsonResult);
//                    }catch (Exception e){
//                        result = new JSONObject();
//                        result.put("data", String.valueOf(jsonResult));
//                    }
//                    res = snaptData.getNewData(args, result);
//                    bsId = snaptData.getBsId(args, result);
//                }
//                ContextHandler.set(ContextHandler.LOG_NEW_DATA, res);
//                ContextHandler.set(ContextHandler.BS_ID, bsId);
//            }
//        }
//        // 处理日志
//        handleLog(joinPoint, controllerLog, null, jsonResult);
//        // 清理上下文容器数据
//        ContextHandler.remove();
//    }
//
//    /**
//     * 处理请求前流程
//     * @param joinPoint 切点
//     */
//    @Before("@annotation(controllerLog)")
//    public void doBefore(JoinPoint joinPoint, OpLog controllerLog) {
//        // 清理上下文容器数据
//        ContextHandler.remove();
//        ContextHandler.set(ContextHandler.START_TIME, System.currentTimeMillis());
//        Class<? extends SnapshotDatatSource> snapt = controllerLog.snaptSource();
//        if (snapt != null) {
//            SnapshotDatatSource snaptData = SpringUtil.getBean(snapt);
//            Object[] args = joinPoint.getArgs();
//            if (args.length == 0) {
//                ContextHandler.set(ContextHandler.LOG_OLD_DATA, new JSONObject());
//            }
//            else {
//                JSONObject oldData = snaptData.getOldData(args);
//                ContextHandler.set(ContextHandler.LOG_OLD_DATA, oldData);
//            }
//        }
//    }
//
//    /**
//     * 拦截异常操作
//     * @param joinPoint 切点
//     * @param e 异常
//     */
//    @AfterThrowing(value = "@annotation(controllerLog)", throwing = "e")
//    public void doAfterThrowing(JoinPoint joinPoint, OpLog controllerLog, Exception e) {
//        // if(Exception instanceof VaildException){}
//        handleLog(joinPoint, controllerLog, e, null);
//        ContextHandler.remove();
//    }
//
//    protected void handleLog(final JoinPoint joinPoint, OpLog controllerLog, final Exception e, Object jsonResult) {
//        try {
//            int costTime = 0;
//            if(ContextHandler.get(ContextHandler.START_TIME) == null){
//                // TODO 暂时先这样处理，后面再优化
//                costTime = 100;
//            } else{
//                costTime = (int) (System.currentTimeMillis() - Long.valueOf(ContextHandler.get(ContextHandler.START_TIME).toString()));
//            }
//
//            HttpServletRequest request = ServletUtils.getRequest();
//            String ip = IpUtils.getIpAddr(request);
//            SysOperLog operLog = new SysOperLog();
//            operLog.setStatus(1);
//            if (e != null) {
//                operLog.setStatus(0);
//            }
//            String traceId = MDC.get(TLOG_TRACE_KEY);
//            // 请求的IP
//            operLog.setIp(ip);
//            PlatformUser platformUser = SecurityUtils.getUser();
//            operLog.setCreateBy(platformUser.getId());
//            operLog.setBusinessType(controllerLog.businessType().getCode());
//            operLog.setCreateTime(new Date());
//            // 执行时间
//            operLog.setExecutionTime(costTime);
//            // 设置方法名称
//            String className = joinPoint.getTarget().getClass().getName();
//            String methodName = joinPoint.getSignature().getName();
//            operLog.setClassMethod(className + "." + methodName + "()");
//            operLog.setIsDeleted(false);
//            operLog.setRemark(controllerLog.title());
//            operLog.setReqMethod(request.getMethod());
//            operLog.setReqBody(getRequestBody(joinPoint, request.getMethod()));
//            operLog.setResult(JSONObject.toJSONString(jsonResult));
//            operLog.setUri(request.getRequestURI());
//            operLog.setUpdateBy(platformUser.getId());
//            String systemModule = BeanUtils.instantiateClass(controllerLog.systemModule()).name();
//            operLog.setSystemModule(systemModule);
//            operLog.setTraceNo(traceId);
//            operLog.setBusinessModule(BeanUtils.instantiateClass(controllerLog.businessModule()).name());
//            String operationModule = controllerLog.operationModule().equals("NONE") ? systemModule + "/" + controllerLog.title()
//                    : controllerLog.operationModule();
//            operLog.setOperationModule(operationModule);
//
//            JSONObject snaptJson = new JSONObject();
//            Object old = ContextHandler.get(ContextHandler.LOG_OLD_DATA);
//            Object newd = ContextHandler.get(ContextHandler.LOG_NEW_DATA);
//            String bsId = (String) ContextHandler.get(ContextHandler.BS_ID);
//
//            operLog.setBsId(bsId);
//            snaptJson.put("oldData", old);
//            snaptJson.put("newData", newd);
//            operLog.setDataSnapt(snaptJson.toJSONString());
//
//            // 保存数据库
//            logRepository.saveSysLog(operLog);
//
//        }
//        catch (Exception exp) {
//            // 记录本地异常日志
//            log.error("==前置通知异常==");
//            log.error("异常信息:{}", exp.getMessage());
//            exp.printStackTrace();
//        }
//    }
//
//    /**
//     * 获取请求的参数，放到log中
//     * @param joinPoint 操作日志
//     * @throws Exception 异常
//     */
//    private String getRequestBody(JoinPoint joinPoint, String requestMethod) throws Exception {
//        if (HttpMethod.PUT.name().equals(requestMethod) || HttpMethod.POST.name().equals(requestMethod)) {
//            return argsArrayToString(joinPoint.getArgs());
//        }
//        return null;
//    }
//
//    private String argsArrayToString(Object[] paramsArray) {
//        try {
//            JSONObject json = new JSONObject();
//            if (paramsArray != null && paramsArray.length > 0) {
//                for (Object obj : paramsArray) {
//                    if (obj != null) {
//                        // System.out.println(obj.getClass().getPackageName());
//                        // 如果参数是 MultipartFile，跳过处理
//                        if (obj instanceof MultipartFile) {
//                            json.put(obj.getClass().getSimpleName(), "MultipartFile (file content not logged)");
//                        }
//                        // 如果参数是 MultipartFile 数组，跳过处理
//                        else if (obj instanceof MultipartFile[]) {
//                            json.put(obj.getClass().getSimpleName(), "MultipartFile[] (file content not logged)");
//                        }
//                        // 安全东西就跳过处理
//                        else if (obj instanceof SecurityContextHolderAwareRequestWrapper) {
//                            continue;
//                        }
//                        else if (obj.getClass().getPackageName().startsWith("org.springframework.jdbc.support")) {
//                            continue;
//                        }
//                        // 如果是异常
//                        else if (obj instanceof Exception) {
//                            json.put("Exception", ((Exception) obj).getMessage());
//                            continue;
//                        }
//                        else {
//                            String key = obj.getClass().getSimpleName();
//                            try {
//                                json.put(key, obj);
//                            } catch (Exception e) {
//                                // 如果序列化出错，记录异常日志（这里省略日志部分）
//                            }
//                        }
//                    }
//                }
//            }
//            return json.toJSONString();
//        } catch (Exception e) {
//            log.error("异常信息:{}", e);
//        }
//        return null;
//    }
//
//    private void saveForUpdateLog(OpLog log, final Exception e) {
//        //
//
//    }
//
//}
