//package com.renpho.erp.pds.config;
//
//import com.alibaba.fastjson.support.spring.FastJsonHttpMessageConverter;
//import org.springframework.http.MediaType;
//import org.springframework.http.converter.HttpMessageConverter;
//import org.springframework.context.annotation.Bean;
//import org.springframework.context.annotation.Configuration;
//import java.nio.charset.StandardCharsets;
//import java.util.ArrayList;
//import java.util.List;
//
///**
// * 自定义序列化工具，代替spring boot默认的序列化.(怀疑这个配置导致网关数据被拦截，发不全)
// * <AUTHOR>
// * @since 2024.9.20
// */
//@Configuration
//public class FastJsonConfig {
//
//    @Bean
//    public HttpMessageConverter<?> fastJsonHttpMessageConverter() {
//        FastJsonHttpMessageConverter fastConverter = new FastJsonHttpMessageConverter();
//        // 自定义 FastJson 配置
//        List<MediaType> supportedMediaTypes = new ArrayList<>();
//        supportedMediaTypes.add(MediaType.APPLICATION_JSON);
//        fastConverter.setSupportedMediaTypes(supportedMediaTypes);
//        fastConverter.setDefaultCharset(StandardCharsets.UTF_8);
//        return fastConverter;
//    }
//}
