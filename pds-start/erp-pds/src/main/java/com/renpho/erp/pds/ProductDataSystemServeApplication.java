package com.renpho.erp.pds;

import com.renpho.erp.security.annotation.EnablePlatformResourceServer;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.openfeign.EnableFeignClients;

import java.util.TimeZone;

/**
 * 服务启动器.
 *
 * <AUTHOR>
 */
@Slf4j
@SpringBootApplication
@EnablePlatformResourceServer
@EnableFeignClients
public class ProductDataSystemServeApplication {

	public static void main(String[] args) {
		//默认设置UTC时间（0时区）
		TimeZone.setDefault(TimeZone.getTimeZone("GMT"));

//		// 重定向 System.out 到日志
//		System.setOut(new PrintStream(System.out) {
//			@Override
//			public void println(String message) {
//				log.info(message);
//			}
//		});

		SpringApplication.run(ProductDataSystemServeApplication.class, args);
		log.info("\nERP-PDS产品管理系统启动成功！");
	}

}
