//package com.renpho.erp.pds.config;
//
//import org.springframework.context.annotation.Bean;
//import org.springframework.context.annotation.Configuration;
//import org.springframework.core.Ordered;
//import org.springframework.core.annotation.Order;
//import org.springframework.security.config.annotation.web.builders.HttpSecurity;
//import org.springframework.security.web.SecurityFilterChain;
//import org.springframework.security.web.util.matcher.AntPathRequestMatcher;
//
///**
// * 本地开发白名单配置,测试才需要.(这个配置会导致用户信息获取不到)
// *
// * <AUTHOR>
// * @since 2024.9.18
// */
//@Configuration
//public class SecurityConfig {
//
//    @Bean
//    @Order(Ordered.HIGHEST_PRECEDENCE)
//    public SecurityFilterChain authorizationServerSecurityFilterChain(HttpSecurity http) throws Exception {
//        http
//                .csrf(csrf -> csrf
//                        .ignoringRequestMatchers(new AntPathRequestMatcher("/country/region/**"), new AntPathRequestMatcher("/color/**"), new AntPathRequestMatcher("/category/**"), new AntPathRequestMatcher("/brand/**"), new AntPathRequestMatcher("/error")))
//                .authorizeHttpRequests(authorizeRequests -> {
//                    authorizeRequests
//                            .requestMatchers("/brand/**", "/country/region/**", "/color/**", "/category/**", "/error").permitAll()
//                            .anyRequest().authenticated();
//                });
//
//        return http.build();
//    }
//}
//
