package com.renpho.erp.pms.model.purchaseorder.model;

import com.alibaba.fastjson2.annotation.JSONField;
import com.renpho.erp.pms.model.purchaserequest.model.PurchaseRequestDTO;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 采购PO单完整数据类
 *
 * <AUTHOR> Zheng
 * @date 2025/5/9
 */
@Data
public class PurchaseOrderDTO implements Serializable {

    @Serial
    private static final long serialVersionUID = 5269912418427538915L;

    private Integer id;

    /**
     * 采购单号
     */
    private String poNo;

    /**
     * 供应商ID
     */
    private Integer supplierId;

    /**
     * 公司主体id
     */
    private Integer companyId;

    /**
     * 产品ID
     */
    private Integer productId;

    /**
     * PSKU
     */
    private String psku;

    /**
     * 采购数量
     */
    private Integer poQty;

    /**
     * 正品数量
     */
    private Integer normalQty;

    /**
     * 备品数量
     */
    private Integer spareQty;

    /**
     * 备品率(‰)
     */
    private Integer  spareRate;

    /**
     * 采购人员id
     */
    private Integer buyerId;

    /**
     * 下单时间
     */
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;

    /**
     * 确认交期
     */
    private LocalDate confirmedDeliveryDate;

    /**
     * 采购供应商
     */
    private PurchaseOrderSupplierInfoDTO supplierInfo;

    /**
     * 采购产品价目
     */
    private PurchaseOrderPriceInfoDTO priceInfo;

    /**
     * 采购总额
     */
    private BigDecimal purchaseAmount;

    /**
     * 上传回签合同时间
     */
    private LocalDate uploadDate;

    /**
     * PO关联的产品信息
     */
    private PurchaseOrderProductInfoDTO productInfo;

    /**
     * PD关联的PR信息
     */
    private PurchaseRequestDTO request;

    /**
     * PO关联的PR信息
     */
    private List<PurchaseRequestDTO> requestList;

}
