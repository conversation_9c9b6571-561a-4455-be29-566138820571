package com.renpho.erp.pms.application.shipplan.service;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import com.renpho.erp.data.permission.service.UserDataPermissionService;
import com.renpho.erp.fms.client.ap.purchase.ps.vo.PurchaseCancelVo;
import com.renpho.erp.ftm.client.response.FileDetailResponse;
import com.renpho.erp.log.repository.annotation.LogRecord;
import com.renpho.erp.log.repository.support.LogRecordContextHolder;
import com.renpho.erp.oms.client.skuMapping.vo.SkuMappingLineVO;
import com.renpho.erp.pms.Infrastructure.PurchaseOrderConstants;
import com.renpho.erp.pms.Infrastructure.feign.RemoteFileFeign;
import com.renpho.erp.pms.Infrastructure.feign.oms.*;
import com.renpho.erp.pms.Infrastructure.feign.fms.RemoteRequisitionPoolFeign;
import com.renpho.erp.pms.Infrastructure.feign.oms.ProductFnskuMappingLookup;
import com.renpho.erp.pms.Infrastructure.feign.qms.QualityApplyOrderLookup;
import com.renpho.erp.pms.Infrastructure.feign.qms.RemoteQualityTaskFeign;
import com.renpho.erp.pms.Infrastructure.feign.saleschannel.SalesChannelLookup;
import com.renpho.erp.pms.Infrastructure.feign.shipment.ShipmentLookup;
import com.renpho.erp.pms.Infrastructure.feign.store.StoreLookup;
import com.renpho.erp.pms.Infrastructure.feign.tms.RemoteTransportRequestFeign;
import com.renpho.erp.pms.Infrastructure.feign.user.OperatorLookup;
import com.renpho.erp.pms.Infrastructure.feign.warehouse.PlatformWarehouseLookup;
import com.renpho.erp.pms.Infrastructure.feign.warehouse.WarehouseLookup;
import com.renpho.erp.pms.Infrastructure.stream.shipmentplan.ShipmentPlanOrderProducer;
import com.renpho.erp.pms.Infrastructure.stream.shipmentplan.request.ShipmentPlanOrderRequestConverter;
import com.renpho.erp.pms.Infrastructure.util.common.CustomBarcodePDFUtil;
import com.renpho.erp.pms.application.mq.producer.QualityApplyOrderProducer;
import com.renpho.erp.pms.application.pendshipment.PdMailService;
import com.renpho.erp.pms.application.purchase.oplog.LogModule;
import com.renpho.erp.pms.application.purchase.service.PurchaseOrderService;
import com.renpho.erp.pms.application.synctask.core.SyncTaskManager;
import com.renpho.erp.pms.application.synctask.model.enums.SyncNodeEnum;
import com.renpho.erp.pms.domain.amazon.shipment.ShipmentId;
import com.renpho.erp.pms.domain.commom.Operator;
import com.renpho.erp.pms.domain.purchaseorder.*;
import com.renpho.erp.pms.domain.saleschannel.SalesChannel;
import com.renpho.erp.pms.domain.saleschannel.SalesChannelId;
import com.renpho.erp.pms.domain.shipplan.*;
import com.renpho.erp.pms.domain.shipplan.dto.*;
import com.renpho.erp.pms.domain.shipplan.enums.PdReceiptStatusEnum;
import com.renpho.erp.pms.domain.shipplan.enums.QmsQcStatusEnum;
import com.renpho.erp.pms.domain.shipplan.enums.ShipmentPlanEnum;
import com.renpho.erp.pms.domain.store.Store;
import com.renpho.erp.pms.domain.store.StoreId;
import com.renpho.erp.pms.domain.warehouse.PlatformWarehouse;
import com.renpho.erp.pms.domain.warehouse.Warehouse;
import com.renpho.erp.pms.domain.warehouse.WarehouseId;
import com.renpho.erp.pms.domain.warehouse.enums.WarehouseTypeEnum;
import com.renpho.erp.pms.exception.BizErrorCode;
import com.renpho.erp.pms.exception.BusinessException;
import com.renpho.erp.pms.exception.DataNotFoundException;
import com.renpho.erp.pms.model.common.model.enums.PmsFileBusinessType;
import com.renpho.erp.pms.model.common.model.enums.PmsOrderFileType;
import com.renpho.erp.pms.model.purchaseorder.model.PurchaseOrderDTO;
import com.renpho.erp.pms.exception.DataNotFoundException;
import com.renpho.erp.pms.model.common.model.enums.PmsFileBusinessType;
import com.renpho.erp.pms.model.common.model.enums.PmsOrderFileType;
import com.renpho.erp.pms.model.purchaseorder.enums.PurchaseOrderStatusEnum;
import com.renpho.erp.pms.model.purchaseorder.model.PurchaseOrderDTO;
import com.renpho.erp.pms.model.shipmentplan.enums.ShipmentPlanOrderStatusEnum;
import com.renpho.erp.pms.model.shipmentplan.model.ShipmentPlanOrderDTO;
import com.renpho.erp.security.util.SecurityUtils;
import com.renpho.erp.tms.client.transportrequest.response.TrStatus;
import com.renpho.erp.tms.client.transportrequest.response.TransportRequestClientVo;
import com.renpho.karma.dto.PageQuery;
import com.renpho.karma.dto.Paging;
import com.renpho.karma.dto.R;
import com.renpho.karma.exception.ApiException;
import com.renpho.karma.exception.ErrorCodeException;
import com.renpho.karma.json.JSONKit;
import com.renpho.pms.client.purchase.response.ShipmentPlanVo;
import com.renpho.pms.client.shipmentplan.request.ShipmentPlanOrderApplyRequest;
import com.renpho.pms.client.shipmentplan.request.ShipmentPlanOrderFinishQcRequest;
import com.renpho.qms.client.apply.vo.QualityDataAggVo;
import com.renpho.qms.client.apply.vo.QualityDataDetailVo;
import com.renpho.erp.pms.model.shipmentplan.enums.ShipmentPlanOrderStatusEnum;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.SerializationUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 出货计划服务接口
 *
 * <AUTHOR>
 * @date 2025-03-13
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class ShipmentPlanOrderService {

    private final ShipmentPlanOrderQueryService shipmentPlanOrderQueryService;
    private final ShipmentPlanOrderLookup shipmentPlanOrderLookup;
    private final ShipmentPlanOrderItemLookup shipmentPlanOrderItemLookup;
    private final ShipmentPlanOrderRepository shipmentPlanOrderRepository;
    private final ShipmentPlanOrderItemRepository shipmentPlanOrderItemRepository;

    private final PurchaseOrderLookup purchaseOrderLookup;
    private final PurchaseOrderSupplierInfoLookup purchaseOrderSupplierInfoLookup;
    private final PurchaseOrderProductInfoLookup purchaseOrderProductInfoLookup;
    private final PurchaseOrderRepository purchaseOrderRepository;
    private final PendShipmentPlanLookup pendShipmentPlanLookup;
    private final PendShipmentPlanRepository pendShipmentPlanRepository;

    private final PurchaseOrderFileRepository purchaseOrderFileRepository;
    private final PurchaseOrderFileLookup purchaseOrderFileLookup;

    private final SalesChannelLookup salesChannelLookup;
    private final StoreLookup storeLookup;
    private final UserDataPermissionService userDataPermissionService;
    private final OperatorLookup operatorLookup;
    private final WarehouseLookup warehouseLookup;

    private final PlatformWarehouseLookup platformWarehouseLookup;

    private final ShipmentLookup shipmentLookup;
    private final PurchaseOrderService purchaseOrderService;
    private final ShipmentPlanOrderRequestConverter shipmentPlanOrderRequestConverter;
    private final ShipmentPlanOrderProducer shipmentPlanOrderProducer;

    private final RemoteFileFeign remoteFileFeign;
    private final QualityApplyOrderLookup qualityApplyOrderLookup;
    private final ProductFnskuMappingLookup productFnskuMappingLookup;
    private final QualityApplyOrderProducer qualityApplyOrderProducer;

    private final SyncTaskManager syncTaskManager;
    private final RemoteRequisitionPoolFeign remoteRequisitionPoolFeign;
    private final RemoteTransportRequestFeign remoteTransportRequestFeign;
    private final RemoteQualityTaskFeign remoteQualityTaskFeign;

    private final PdMailService pdMailService;

    /**
     * 查询分页数据
     *
     * @param condition 查询条件
     * @param pageQuery 分页条件
     * @return 分页数据
     */
    public Paging<ShipmentPlanOrder> findPage(QueryShipPlan condition, PageQuery pageQuery) {
        Set<Integer> userIdSet = userDataPermissionService.getDataPermission();
        condition.setUserIdSet(userIdSet);
        // 查询分页数据
        Paging<ShipmentPlanOrder> paging = shipmentPlanOrderLookup.findPage(condition, pageQuery);
        if (CollectionUtil.isEmpty(paging.getRecords())) {
            return paging; // 如果记录为空，直接返回
        }

        List<ShipmentPlanOrder> records = paging.getRecords();

        // 提取相关字段并去重
        List<String> pdNos = extractDistinctField(records, ShipmentPlanOrder::getPdNo);
        List<StoreId> storeIds = extractDistinctField(records, ShipmentPlanOrder::getStoreId);
        List<SalesChannelId> salesChannelIds = extractDistinctField(records, ShipmentPlanOrder::getSalesChannelId);
        List<PurchaseOrderId> poIds = records.stream().map(e -> new PurchaseOrderId(e.getPoId())).distinct().collect(Collectors.toList());

        // 查询关联数据
        List<ShipmentPlanOrderItem> shipmentPlanOrderItems = shipmentPlanOrderItemLookup.findByPdNoList(pdNos);
        List<WarehouseId> warehouseIds = extractDistinctField(shipmentPlanOrderItems, ShipmentPlanOrderItem::getDestWarehouseId);
        Map<WarehouseId, Warehouse> warehouseMap = warehouseLookup.getWarehouseMapByIds(warehouseIds);
        Map<String, ShipmentPlanOrderItem> shipmentPlanOrderItemMap = shipmentPlanOrderItems.stream().collect(Collectors.toMap(ShipmentPlanOrderItem::getPdNo, Function.identity()));
        Map<SalesChannelId, SalesChannel> salesChannelMap = salesChannelLookup.querySalesChannelMapByIds(salesChannelIds);
        Map<StoreId, Store> storeMap = storeLookup.queryStoreMapByIds(storeIds);
        Map<PurchaseOrderId, PurchaseOrderSupplierInfo> supplierInfoMap = purchaseOrderSupplierInfoLookup.findByPoIds(poIds);
        //文件处理
        Map<String, List<PurchaseOrderFile>> fileMap = purchaseOrderFileLookup.findAllByOrderNoList(pdNos, PmsFileBusinessType.PD_ORDER.getCode(), PmsOrderFileType.PD_ORDER_MARK.getCode());

        // 提取用户ID列表并设置操作员信息
        List<Operator> userIdList = extractUserIds(records);
        operatorLookup.findAndSetByIds(userIdList);

        // 补充每个订单的详细信息
        enrichShipmentPlanOrders(records, shipmentPlanOrderItemMap, warehouseMap, storeMap, salesChannelMap, supplierInfoMap, fileMap);
        return paging;
    }

    /**
     * 查询出货计划，待后续修改
     *
     * @param condition 查询条件
     * @param pageQuery 分页参数
     */
    public List<ShipmentPlanExport> exportShipmentPlanOrder(QueryShipPlan condition, PageQuery pageQuery) {
        Set<Integer> userIdSet = userDataPermissionService.getDataPermission();
        condition.setUserIdSet(userIdSet);
        // 查询分页数据
        Paging<ShipmentPlanOrder> paging = this.findPage(condition, pageQuery);
        List<ShipmentPlanExport> shipmentPlanExports = new ArrayList<>();
        while (CollectionUtil.isNotEmpty(paging.getRecords())) {
            buildShipmentPlanExport(shipmentPlanExports, paging.getRecords());
            pageQuery.setPageIndex(pageQuery.getPageIndex() + 1);
            paging = this.findPage(condition, pageQuery);
        }
        return shipmentPlanExports;
    }


    public List<ShipmentPlanOrder> prepareConfirmShipmentPlanOrder(List<String> pdNos) {
        if (pdNos == null || pdNos.isEmpty()) {
            return Collections.emptyList(); // 处理空输入
        }

        List<ShipmentPlanOrder> shipmentPlanOrders = shipmentPlanOrderLookup.findByPdNoList(pdNos);
        commonValidShipmentPlanOrder(pdNos, shipmentPlanOrders, List.of(ShipmentPlanOrderStatusEnum.PEND_CONFIRM.getValue()));

        // 查询关联数据
        List<ShipmentPlanOrderItem> shipmentPlanOrderItems = shipmentPlanOrderItemLookup.findByPdNoList(pdNos);
        List<WarehouseId> warehouseIds = extractDistinctField(shipmentPlanOrderItems, ShipmentPlanOrderItem::getDestWarehouseId);
        List<SalesChannel> salesChannels = extractDistinctField(shipmentPlanOrders, ShipmentPlanOrder::getSalesChannel);
        List<StoreId> storeIds = extractDistinctField(shipmentPlanOrders, ShipmentPlanOrder::getStoreId);
        List<SalesChannelId> salesChannelIds = extractDistinctField(shipmentPlanOrders, ShipmentPlanOrder::getSalesChannelId);
        List<String> pskuList = extractDistinctField(shipmentPlanOrders, ShipmentPlanOrder::getPsku);

        // 查询外部数据
        List<PlatformWarehouse> platformWarehouses = platformWarehouseLookup.findBySalesChannels(salesChannels);
        List<SkuMappingLineVO> skuMappingLineList = productFnskuMappingLookup.getSkuMappingLineList(pskuList, storeIds, salesChannelIds);

        // 构建映射表
        Map<WarehouseId, Warehouse> warehouseMap = warehouseLookup.getWarehouseMapByIds(warehouseIds);
        Map<String, ShipmentPlanOrderItem> shipmentPlanOrderItemMap = shipmentPlanOrderItems.stream()
                .filter(Objects::nonNull)
                .collect(Collectors.toMap(ShipmentPlanOrderItem::getPdNo, Function.identity(), (existing, replacement) -> existing));
        Map<SalesChannelId, SalesChannel> salesChannelMap = salesChannelLookup.querySalesChannelMapByIds(salesChannelIds);
        Map<StoreId, Store> storeMap = storeLookup.queryStoreMapByIds(storeIds);

        // 补充订单信息
        enrichShipmentPlanOrders(shipmentPlanOrders, shipmentPlanOrderItemMap, warehouseMap, storeMap, salesChannelMap, Map.of(), Map.of());

        // 填充额外信息
        fullFillShipmentPlanOrder(shipmentPlanOrders, skuMappingLineList, platformWarehouses);

        return shipmentPlanOrders;
    }

    @Transactional(rollbackFor = Exception.class)
    @LogRecord(module = LogModule.SHIPMENT_PLAN_LOG_MODULE
            , type = LogModule.CommonDesc.REVIEW_OPERATOR
            , desc = LogModule.CommonDesc.REVIEW_DESC)
    public void batchReviewCmd(BatchReview batchReview) {
        if (CollectionUtils.isEmpty(batchReview.getPdNos())) {
            throw new ErrorCodeException(BizErrorCode.INVALID_REQUEST_CONTENT);
        }
        List<ShipmentPlanOrder> shipmentPlanOrders = shipmentPlanOrderLookup.findByPdNoList(batchReview.getPdNos());
        commonValidShipmentPlanOrder(batchReview.getPdNos(), shipmentPlanOrders, List.of(ShipmentPlanOrderStatusEnum.ON_REVIEW.getValue()));
        shipmentPlanOrderRepository.batchUpdateShipmentPlanOrderStatus(batchReview.getPdNos(), batchReview.getOpType());
        if (batchReview.getOpType().equals(ShipmentPlanOrderStatusEnum.REJECTED.getValue())) {
            List<PendShipmentPlan> pendShipmentPlans = bulidPendShipmentPlanList(batchReview.getPdNos(), shipmentPlanOrders);
            pendShipmentPlanRepository.updateBatchPendQty(pendShipmentPlans);
        }
        generateShipmentLog(batchReview.getPdNos(), shipmentPlanOrders);
    }

    @Transactional(rollbackFor = Exception.class)
    @LogRecord(module = LogModule.SHIPMENT_PLAN_LOG_MODULE
            , type = LogModule.CommonDesc.WITHDRAW_OPERATOR
            , desc = LogModule.CommonDesc.WITHDRAW_DESC)
    public void withdrawShippingPlan(BatchReview batchReview) {
        if (CollectionUtils.isEmpty(batchReview.getPdNos())) {
            throw new ErrorCodeException(BizErrorCode.INVALID_REQUEST_CONTENT);
        }
        List<ShipmentPlanOrder> shipmentPlanOrders = shipmentPlanOrderLookup.findByPdNoList(batchReview.getPdNos());
        commonValidShipmentPlanOrder(batchReview.getPdNos(), shipmentPlanOrders, List.of(ShipmentPlanOrderStatusEnum.PEND_CONFIRM.getValue()));
        if (batchReview.getOpType().equals(ShipmentPlanOrderStatusEnum.VOID.getValue())) {
            List<PendShipmentPlan> pendShipmentPlans = bulidPendShipmentPlanList(batchReview.getPdNos(), shipmentPlanOrders);
            pendShipmentPlanRepository.updateBatchPendQty(pendShipmentPlans);
        }
        shipmentPlanOrderRepository.batchUpdateShipmentPlanOrderStatus(batchReview.getPdNos(), batchReview.getOpType());
        generateShipmentLog(batchReview.getPdNos(), shipmentPlanOrders);
    }

    @Transactional(rollbackFor = Exception.class)
    @LogRecord(module = LogModule.SHIPMENT_PLAN_LOG_MODULE
            , type = LogModule.ShipmentPlanOperatorLogModule.MARK_FILE_SYNCED_OPERATOR
            , desc = LogModule.ShipmentPlanOperatorLogModule.MARK_FILE_SYNCED_DESC)
    public void updateFileSyncMark(EditShipPlanFile editShipPlanFile) {
        if (CollectionUtils.isEmpty(editShipPlanFile.getPdNos())) {
            throw new ErrorCodeException(BizErrorCode.INVALID_REQUEST_CONTENT);
        }
        List<ShipmentPlanOrder> shipmentPlanOrders = shipmentPlanOrderLookup.findByPdNoList(editShipPlanFile.getPdNos());
        commonValidShipmentPlanOrder(editShipPlanFile.getPdNos(), shipmentPlanOrders, List.of(ShipmentPlanOrderStatusEnum.CONFIRMED.getValue(),
                ShipmentPlanOrderStatusEnum.CANCELED.getValue(), ShipmentPlanOrderStatusEnum.VOID.getValue()));
        shipmentPlanOrderRepository.updateFileSyncMark(editShipPlanFile);
        generateShipmentLog(editShipPlanFile.getPdNos(), shipmentPlanOrders);
    }

    /**
     * @param purchaseSku productSku
     * @param storeId     店铺ID
     * @param platformId  平台ID
     * @return FnskuMappingLineVO 映射关系
     */
    public Collection<Attr> getSkuMappingLineList(String purchaseSku, Integer storeId, Integer platformId) {
        List<SkuMappingLineVO> list = productFnskuMappingLookup.getSkuMappingLineList(List.of(purchaseSku),
                List.of(new StoreId(storeId)), List.of(new SalesChannelId(platformId)));

        if (CollectionUtils.isEmpty(list)) {
            return List.of();
        }
        return list.stream()
                .map(e -> new Attr(e.getFnsku(), e.getFnsku()))
                .toList();
    }

    /**
     * 根据PD获取 PD 关联的质检数据
     * @param pdNo 入参
     * @return ShipmentPlanOrder 质检数据
     */
   public ShipmentPlanOrder findQcDataByPdNo(String pdNo){
       Optional<ShipmentPlanOrder> pdOrder = shipmentPlanOrderLookup.findByPdNo(pdNo);
       ShipmentPlanOrder shipmentPlanOrder = pdOrder.orElseThrow(DataNotFoundException::new);

       List<ShipmentPlanOrderItem> pdItems = shipmentPlanOrderItemLookup.findByPdNoList(List.of(pdNo));
       if (CollectionUtils.isEmpty(pdItems)) {
           throw new DataNotFoundException();
       }else{
           shipmentPlanOrder.setLastDeliveryDate(pdItems.get(0).getLastDeliveryDate());
           shipmentPlanOrder.setPlanDate(pdItems.get(0).getExpectedPdDate());
       }
       R<QualityDataAggVo> qcDataByPdNo = remoteQualityTaskFeign.findQcDataByPdNo(pdNo);
       if(!qcDataByPdNo.isSuccess()){
           throw new BusinessException("REMOTE_CALL_QMS_ERROR",qcDataByPdNo.getMessage());
       }
       QualityDataAggVo data = qcDataByPdNo.getData();
       if(data != null) {
           List<QualityData> qualityDataList = new ArrayList<>();
           List<QualityDataDetailVo> refQcTaskList = data.getRefQcTaskList();
           if (CollectionUtils.isNotEmpty(refQcTaskList)) {
               Map<String, FileDetailResponse> fileInfoMap = remoteFileFeign.getFileResponseMap(refQcTaskList.stream().
                       map(QualityDataDetailVo::getReportFileId).filter(StringUtils::isNotBlank).collect(Collectors.toList()));
               refQcTaskList.forEach(detail -> {
                   QualityData qualityData = new QualityData();
                   qualityData.setQcId(detail.getQcId());
                   qualityData.setQcNo(detail.getQcNo());
                   qualityData.setSqId(detail.getSqId());
                   qualityData.setSqNo(detail.getSqNo());
                   qualityData.setQcResult(detail.getQcResult());
                   qualityData.setReportFileId(detail.getReportFileId());
                   if (fileInfoMap.containsKey(detail.getReportFileId())) {
                       FileDetailResponse detailResponse = fileInfoMap.get(detail.getReportFileId());
                       qualityData.setOriginalFilename(detailResponse.getOriginalFilename());
                       qualityData.setFileUrl(detailResponse.getUrl());
                       qualityData.setExt(detailResponse.getExt());
                   }
                   qualityDataList.add(qualityData);
               });
           }
           shipmentPlanOrder.setQualityDataList(qualityDataList);
       }
       return shipmentPlanOrder;
   }



    @Transactional(rollbackFor = Exception.class)
    @LogRecord(module = LogModule.SHIPMENT_PLAN_LOG_MODULE
            , type = LogModule.CommonDesc.CONFIRM_OPERATOR
            , desc = LogModule.CommonDesc.CONFIRM_DESC)
    public void batchConfirmCmd(BatchConfirm batchConfirm) {
        long time1 = System.currentTimeMillis();
        List<String> pdNos = batchConfirm.getConfirmList().stream().map(BatchConfirm.Confirm::getPdNo).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(pdNos)) {
            throw new ErrorCodeException(BizErrorCode.INVALID_REQUEST_CONTENT);
        }
        List<ShipmentPlanOrder> shipmentPlanOrders = shipmentPlanOrderLookup.findByPdNoList(pdNos);
        commonValidShipmentPlanOrder(pdNos, shipmentPlanOrders, List.of(ShipmentPlanOrderStatusEnum.PEND_CONFIRM.getValue()));

        List<ShipmentPlanOrder> oldShipmentPlanOrders = shipmentPlanOrders.stream().map(SerializationUtils::clone)
                .collect(Collectors.toList());

        List<PurchaseOrderId> poIds = shipmentPlanOrders.stream().map(e -> new PurchaseOrderId(e.getPoId())).distinct().collect(Collectors.toList());
        List<SalesChannelId> salesChannelIds = extractDistinctField(shipmentPlanOrders, ShipmentPlanOrder::getSalesChannelId);
        List<ShipmentPlanOrderItem> shipmentPlanOrderItems = shipmentPlanOrderItemLookup.findByPdNoList(pdNos);
        List<ShipmentPlanOrderItem> oldShipmentPlanOrderItems = shipmentPlanOrderItems.stream().map(SerializationUtils::clone)
                .toList();
        Map<String, ShipmentPlanOrderItem> oldShipmentPlanOrderItemMap = oldShipmentPlanOrderItems.stream()
                .filter(Objects::nonNull)
                .collect(Collectors.toMap(ShipmentPlanOrderItem::getPdNo, Function.identity(), (existing, replacement) -> existing));

        Map<String, ShipmentPlanOrderItem> shipmentPlanOrderItemMap = shipmentPlanOrderItems.stream()
                .filter(Objects::nonNull)
                .collect(Collectors.toMap(ShipmentPlanOrderItem::getPdNo, Function.identity(), (existing, replacement) -> existing));

        Map<SalesChannelId, SalesChannel> salesChannelMap = salesChannelLookup.querySalesChannelMapByIds(salesChannelIds);
        Map<PurchaseOrderId, PurchaseOrderSupplierInfo> supplierInfoMap = purchaseOrderSupplierInfoLookup.findByPoIds(poIds);
        Map<PurchaseOrderId, PurchaseOrderProductInfo> purchaseOrderProductInfoMap = purchaseOrderProductInfoLookup.findAllByPoIds(poIds);

        Map<PurchaseOrderId, PurchaseOrder> purchaseOrderMap = purchaseOrderLookup.findByPoIds(poIds);
        //记录日志使用
        enrichShipmentPlanOrders(oldShipmentPlanOrders, oldShipmentPlanOrderItemMap, Map.of(), Map.of(), salesChannelMap, Map.of(), Map.of());
        List<PurchaseOrderFile> saveList = buildModifyData(batchConfirm, shipmentPlanOrders, shipmentPlanOrderItemMap);
        enrichShipmentPlanOrders(shipmentPlanOrders, shipmentPlanOrderItemMap, Map.of(), Map.of(), salesChannelMap, supplierInfoMap, Map.of());

        //校验shipmentId,fnSku
        long time2 = System.currentTimeMillis();
        log.info("batchConfirmCmd----time2-time1 耗时:{}ms", (time2 - time1));
        lastValidation(shipmentPlanOrders, saveList);
        long time3 = System.currentTimeMillis();
        log.info("batchConfirmCmd----lastValidation 耗时:{}ms", (time3 - time2));

        purchaseOrderFileRepository.saveBatchData(saveList);
        shipmentPlanOrderItemRepository.batchUpdateShipmentPlanOrderItem(shipmentPlanOrderItems);
        shipmentPlanOrderRepository.batchConfirmShipmentPlanOrder(shipmentPlanOrders);
        //推送QMS申请单数据
        qualityApplyOrderProducer.pushQualityApplyOrderData(qualityApplyOrderLookup.buildQmsApplyOrder(shipmentPlanOrders, purchaseOrderMap, purchaseOrderProductInfoMap), saveList, SecurityUtils.getUserId());
        Map<String, ShipmentPlanOrder> logMap = shipmentPlanOrders.stream().collect(Collectors.toMap(ShipmentPlanOrder::getPdNo, Function.identity()));
        oldShipmentPlanOrders.forEach(oldShipmentPlanOrder -> LogRecordContextHolder.putRecordData(oldShipmentPlanOrder.getId().id().toString(), oldShipmentPlanOrder, logMap.get(oldShipmentPlanOrder.getPdNo())));

        // 推送完整PD数据
        pushPdConfirm(shipmentPlanOrders);
    }

    /**
     * 推送确认的PD信息
     *
     * @param shipmentPlanOrderList 出货计划
     */
    private void pushPdConfirm(List<ShipmentPlanOrder> shipmentPlanOrderList) {
        // 获取PD关联的PO信息
        Map<PurchaseOrderId, PurchaseOrderDTO> purchaseOrderIdPurchaseOrderRequestMap = purchaseOrderService.generatePurchaseOrderRequestList(
                shipmentPlanOrderList
        );
        for (ShipmentPlanOrder shipmentPlanOrder : shipmentPlanOrderList) {
            PurchaseOrderDTO purchaseOrderDTO = purchaseOrderIdPurchaseOrderRequestMap.get(new PurchaseOrderId(shipmentPlanOrder.getPoId()));
            // 取PD唯一一个PR
            String prNo = shipmentPlanOrder.getPrNo();
            purchaseOrderDTO.setRequest(purchaseOrderDTO.getRequestList().stream().filter(e -> e.getPrNo().equals(prNo)).findFirst().orElse(null));
            ShipmentPlanOrderDTO shipmentPlanOrderDTO = shipmentPlanOrderRequestConverter.toShipmentPlanOrderRequest(shipmentPlanOrder);
            shipmentPlanOrderDTO.setPurchaseOrder(purchaseOrderDTO);
            // 计算PD总金额 = PO单价*PD数量（正品）
            shipmentPlanOrderDTO.setTotalAmount(purchaseOrderDTO.getPriceInfo().getInclusiveTaxPrice()
                    .multiply(BigDecimal.valueOf(shipmentPlanOrderDTO.getPdQty())));
            shipmentPlanOrderProducer.pdConfirm(shipmentPlanOrderDTO);
        }
    }

    private void lastValidation(List<ShipmentPlanOrder> shipmentPlanOrders, List<PurchaseOrderFile> saveList) {

        List<StoreId> storeIds = extractDistinctField(shipmentPlanOrders, ShipmentPlanOrder::getStoreId);
        List<SalesChannelId> salesChannelIds = extractDistinctField(shipmentPlanOrders, ShipmentPlanOrder::getSalesChannelId);
        List<String> pskuList = extractDistinctField(shipmentPlanOrders, ShipmentPlanOrder::getPsku);
        // 查询外部数据
        long time1 = System.currentTimeMillis();
        List<SkuMappingLineVO> skuMappingLineList = productFnskuMappingLookup.getSkuMappingLineList(pskuList, storeIds, salesChannelIds);
        long time2 = System.currentTimeMillis();
        log.info("调用oms查询fnsku映射关系----耗时:{}ms", (time2 - time1));

        Map<String, List<SkuMappingLineVO>> skuMappingLineMap = skuMappingLineList.stream()
                .collect(Collectors.groupingBy(e -> buildFnSkuKey(e.getStoreId(), e.getPsku(), e.getChannelId())));

        Map<String, List<PurchaseOrderFile>> purchaseOrderFileMap = saveList.stream()
                .collect(Collectors.groupingBy(PurchaseOrderFile::getOrderNo));

        Map<StoreId, List<ShipmentId>> shipmentIdMap = new HashMap<>();

        shipmentPlanOrders.forEach(shipmentPlanOrder -> {
            ShipmentPlanOrderItem shipmentPlanOrderItem = shipmentPlanOrder.getItem();
            String fnsku = shipmentPlanOrderItem.getFnSku();
            String pdNo = shipmentPlanOrder.getPdNo();
            StoreId storeId = shipmentPlanOrder.getStoreId();
            if (StringUtils.isNotEmpty(shipmentPlanOrder.getShipmentID()) && PurchaseOrderConstants.SALES_CHANNEL_CODE_AMAZON.equalsIgnoreCase(shipmentPlanOrder.getSalesChannel().getChannelCode())) {
                if (shipmentIdMap.containsKey(storeId)) {
                    shipmentIdMap.get(storeId).add(new ShipmentId(shipmentPlanOrder.getShipmentID()));
                } else {
                    List<ShipmentId> newList = new ArrayList<>();
                    newList.add(new ShipmentId(shipmentPlanOrder.getShipmentID()));
                    shipmentIdMap.put(storeId, newList);
                }
            }

            if (StringUtils.isEmpty(fnsku)) {
                throw new ErrorCodeException(BizErrorCode.PD_FNSKU_IS_EMPTY);
            }
            String fnSkuKey = buildFnSkuKey(shipmentPlanOrder);
            if (!skuMappingLineMap.containsKey(fnSkuKey) || !skuMappingLineMap.get(fnSkuKey).stream().map(SkuMappingLineVO::getFnsku).collect(Collectors.toSet()).contains(fnsku)) {
                throw new ErrorCodeException(BizErrorCode.PD_FNSKU_NOT_EXIST_MAPPING, fnsku, pdNo);
            }
            Integer warehouseType = shipmentPlanOrderItem.getDestWarehouseType();

            if (warehouseType.equals(WarehouseTypeEnum.PLATFORM_WAREHOUSE.getCode())) {
                if (StringUtils.isEmpty(shipmentPlanOrder.getShipmentID())) {
                    throw new ErrorCodeException(BizErrorCode.PD_SHIPMENT_ID_IS_EMPTY, pdNo);
                }
                if (StringUtils.isEmpty(shipmentPlanOrder.getReferenceID())) {
                    throw new ErrorCodeException(BizErrorCode.PD_REFERENCE_ID_IS_EMPTY, pdNo);
                }
                if (!purchaseOrderFileMap.containsKey(pdNo)) {
                    throw new ErrorCodeException(BizErrorCode.PD_MARK_IS_EMPTY, pdNo);
                }

            } else {
                if (StringUtils.isNotEmpty(shipmentPlanOrder.getShipmentID())) {
                    throw new ErrorCodeException(BizErrorCode.PD_SHIPMENT_ID_NOT_EMPTY, pdNo);
                }
            }
            shipmentPlanOrder.setFileList(purchaseOrderFileMap.get(pdNo));
        });
        long time3 = System.currentTimeMillis();
        List<ShipmentId> shipmentIds = shipmentLookup.findInvalidShipmentIds(shipmentIdMap);
        long time4 = System.currentTimeMillis();
        log.info("调用amazon查询shipmentId是否存在----耗时:{}ms", (time4 - time3));
        if (CollectionUtils.isNotEmpty(shipmentIds)) {
            throw new ErrorCodeException(BizErrorCode.PD_SHIPMENT_ID_IS_WRONG, shipmentIds.stream().map(ShipmentId::id).collect(Collectors.toList()));
        }
    }

    private List<PurchaseOrderFile> buildModifyData(BatchConfirm batchConfirm, List<ShipmentPlanOrder> shipmentPlanOrders, Map<String, ShipmentPlanOrderItem> itemMap) {

        Map<String, ShipmentPlanOrder> orderMap = shipmentPlanOrders.stream().collect(Collectors.toMap(ShipmentPlanOrder::getPdNo, Function.identity(), (existing, replacement) -> existing));
        List<PurchaseOrderFile> saveList = new ArrayList<>();
        LocalDateTime confirmTime = LocalDateTime.now();
        for (BatchConfirm.Confirm data : batchConfirm.getConfirmList()) {
            String pdNo = data.getPdNo();
            ShipmentPlanOrder shipmentPlanOrder = orderMap.get(pdNo);
            ShipmentPlanOrderItem shipmentPlanOrderItem = itemMap.get(pdNo);
            if (StringUtils.isNotEmpty(data.getFileId())) {
                PurchaseOrderFile purchaseOrderFile = new PurchaseOrderFile(null);
                purchaseOrderFile.setOrderNo(pdNo);
                purchaseOrderFile.setBusinessType(PmsFileBusinessType.PD_ORDER.getCode());
                purchaseOrderFile.setFileType(PmsOrderFileType.PD_ORDER_MARK.getCode());
                purchaseOrderFile.setOrderId(shipmentPlanOrder.getId().id());
                purchaseOrderFile.setFileId(data.getFileId());
                saveList.add(purchaseOrderFile);
            }
            shipmentPlanOrder.setShipmentID(Objects.toString(data.getShipmentId(), ""));
            shipmentPlanOrder.setReferenceID(Objects.toString(data.getReferenceId(), ""));
            shipmentPlanOrder.setConfirmTime(confirmTime);
            shipmentPlanOrder.setStatus(ShipmentPlanOrderStatusEnum.CONFIRMED);
            shipmentPlanOrder.setStoreId(new StoreId(data.getStoreId()));
            shipmentPlanOrderItem.setDestWarehouseCode(data.getDestWarehouseCode());
            shipmentPlanOrderItem.setFnSku(Objects.toString(data.getFnSku(), ""));
        }

        return saveList;
    }

    /**
     * 检查QMS的SQ是否可取消
     */
    public void validateCancelableQmsApplyOrder(String pdNo) {
        qualityApplyOrderLookup.validateCancelableQmsApplyOrder(pdNo);
    }

    /**
     * 检查FMS的请款单是否可取消
     */
    public void validateCancelableFmsApplyOrder(String pdNo) {
        PurchaseCancelVo purcahseCancelVo = remoteRequisitionPoolFeign.validateCancelableFmsApplyOrder(pdNo);
        // FMS结果处理
        fmsResultProcessing(purcahseCancelVo);
    }

    /**
     * FMS返回结果处理
     */
    private void fmsResultProcessing(PurchaseCancelVo purcahseCancelVo) {
        if (Objects.isNull(purcahseCancelVo)) {
            throw new ErrorCodeException(BizErrorCode.BUSINESS_FAILED);
        }
        Integer status = purcahseCancelVo.getStatus();
        if (NumberUtils.INTEGER_ONE.equals(status)) {
            throw new ErrorCodeException(BizErrorCode.FMS_APPLY_ORDER_NOT_CANCELABLE);
        } else if (NumberUtils.INTEGER_MINUS_ONE.equals(status)) {
            throw new ErrorCodeException(BizErrorCode.DATA_DOES_NOT_EXIST);
        }
    }

    /**
     * 检查TMS的TR是否可取消
     */
    public void validateCancelableTmsTransportRequest(List<String> pdNos) {
        List<TransportRequestClientVo> trList = remoteTransportRequestFeign.findByPdNoList(new HashSet<>(pdNos));
        boolean hasActiveTr = trList.stream()
                .anyMatch(tr -> !Objects.equals(tr.getStatus(), TrStatus.CANCEL.getValue()));
        if (hasActiveTr) {
            throw new ErrorCodeException(BizErrorCode.TMS_TRANSPORT_REQUEST_NOT_CANCELABLE);
        }
    }

    /**
     * 批量取消出库计划
     *
     * @param batchReview 批量审核入参
     */
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(module = LogModule.SHIPMENT_PLAN_LOG_MODULE
            , type = LogModule.CommonDesc.CANCEL_OPERATOR
            , desc = LogModule.CommonDesc.CANCEL_DESC)
    public void batchCancelShipmentPlan(BatchReview batchReview) {
        List<String> pdNos = batchReview.getPdNos();
        if (CollectionUtils.isEmpty(pdNos)) {
            throw new ErrorCodeException(BizErrorCode.INVALID_REQUEST_CONTENT_FOR_PLACEHOLDER);
        }
        List<ShipmentPlanOrder> shipmentPlanOrders = shipmentPlanOrderLookup.findByPdNoList(pdNos);
        commonValidShipmentPlanOrder(pdNos, shipmentPlanOrders, List.of(ShipmentPlanOrderStatusEnum.CONFIRMED.getValue()));

        List<PendShipmentPlan> pendShipmentPlans = bulidPendShipmentPlanList(pdNos, shipmentPlanOrders);
        // 验证TMS关联的TR是否可取消 （不对TMS进行取消，由TMS的相关负责人去取消）
        validateCancelableTmsTransportRequest(pdNos);
        // 取消FMS的PS（FMS取消PS前会有前置验证是否可取消）
        PurchaseCancelVo purchaseCancelVo = remoteRequisitionPoolFeign.cancelRequisitionPool(pdNos.get(0), shipmentPlanOrders.get(0).getPurchaserStaff().getOperatorId().id());
        // FMS结果处理
        fmsResultProcessing(purchaseCancelVo);

        // 取消QMS的SQ、QC （QMS取消前会有前置验证是否可取消）
        qualityApplyOrderLookup.cancelQmsApplyOrder(pdNos);
        pendShipmentPlanRepository.updateBatchPendQty(pendShipmentPlans);
        // 批量取消出库计划
        shipmentPlanOrderRepository.batchCancelShipmentPlanOrderStatus(pdNos, ShipmentPlanOrderStatusEnum.CANCELED.getValue());
        generateShipmentLog(batchReview.getPdNos(), shipmentPlanOrders);
    }

    /**
     * 推送取消的PD信息
     *
     * @param shipmentPlanOrderList 出货计划
     */
    private void pushPdCancel(List<ShipmentPlanOrder> shipmentPlanOrderList) {
        List<ShipmentPlanOrderDTO> shipmentPlanOrderDTOList = shipmentPlanOrderRequestConverter.toShipmentPlanOrderRequest(shipmentPlanOrderList);
        for (ShipmentPlanOrderDTO shipmentPlanOrderDTO : shipmentPlanOrderDTOList) {
            shipmentPlanOrderProducer.pdCancel(shipmentPlanOrderDTO);
        }
    }

    private List<PendShipmentPlan> bulidPendShipmentPlanList(List<String> pdNos, List<ShipmentPlanOrder> shipmentPlanOrders) {
        List<ShipmentPlanOrderItem> shipmentPlanOrderItems = shipmentPlanOrderItemLookup.findByPdNoList(pdNos);
        if (CollectionUtils.isEmpty(shipmentPlanOrderItems)) {
            return List.of();
        }
        Map<String, List<ShipmentPlanOrderItem>> shipmentPlanOrderItemMap = shipmentPlanOrderItems.stream().collect(Collectors.groupingBy(ShipmentPlanOrderItem::getPrNo));
        List<PendShipmentPlan> pendShipmentPlans = pendShipmentPlanLookup.findByPrNoList(shipmentPlanOrders.stream().map(ShipmentPlanOrder::getPrNo).distinct().collect(Collectors.toList()));
        List<PendShipmentPlan> pendPlans = new ArrayList<>();
        pendShipmentPlans.forEach(pendShipmentPlan -> {
            String prNo = pendShipmentPlan.getPurchaseOrderItem().getPrNo();
            if (CollectionUtils.isNotEmpty(shipmentPlanOrderItemMap.get(prNo))) {
                Integer pdQty = shipmentPlanOrderItemMap.get(prNo).stream().mapToInt(ShipmentPlanOrderItem::getPdQty).sum();
                pendShipmentPlan.setPendQty(pendShipmentPlan.getPendQty() + pdQty);
                if (pendShipmentPlan.getPurchaseRequestItem().getQuantity().equals(pendShipmentPlan.getPendQty())) {
                    pendShipmentPlan.setShipmentPlanEnum(ShipmentPlanEnum.UNPLANNED);
                } else {
                    pendShipmentPlan.setShipmentPlanEnum(ShipmentPlanEnum.PARTIAL_PLAN);
                }
                pendPlans.add(pendShipmentPlan);
            }
        });
        return pendPlans;
    }

    /**
     * 校验出库计划状态,适否存在
     *
     * @param pdNos              出库计划编号
     * @param shipmentPlanOrders 出库计划
     * @param value              状态
     */
    private void commonValidShipmentPlanOrder(List<String> pdNos, List<ShipmentPlanOrder> shipmentPlanOrders, List<Integer> value) {
        Map<String, ShipmentPlanOrder> shipmentPlanOrderMap = shipmentPlanOrders.stream().collect(Collectors.toMap(ShipmentPlanOrder::getPdNo, Function.identity()));
        pdNos.forEach(pdNo -> {
            ShipmentPlanOrder shipmentPlanOrder = shipmentPlanOrderMap.get(pdNo);
            singleValidShipmentPlanOrder(shipmentPlanOrder, value);
        });
    }

    private void singleValidShipmentPlanOrder(ShipmentPlanOrder shipmentPlanOrder, List<Integer> value) {
        if (Objects.isNull(shipmentPlanOrder)) {
            throw new ApiException(BizErrorCode.DATA_DOES_NOT_EXIST);
        }
        if (value.stream().noneMatch(v -> v.equals(shipmentPlanOrder.getStatus().getValue()))) {
            throw new ApiException(BizErrorCode.PD_STATUS_IS_INCORRECT, shipmentPlanOrder.getPdNo());
        }
    }


    /**
     * 统计出库计划状态数量
     */
    public Map<Integer, Object> countByStatus() {
        Set<Integer> userIdSet = userDataPermissionService.getDataPermission();
        Map<Integer, Object> map = shipmentPlanOrderLookup.countByStatus(userIdSet);
        Arrays.stream(ShipmentPlanOrderStatusEnum.values()).forEach(status -> {
            if (!map.containsKey(status.getValue())) {
                map.put(status.getValue(), 0);
            }
        });
        // 为所有出库计划数量之和
        map.put(PurchaseOrderConstants.ALL_STATUS_COUNT, map.values().stream().map(e -> Integer.parseInt(e.toString())).reduce(0, Integer::sum));
        return map;
    }

    @LogRecord(module = LogModule.SHIPMENT_PLAN_LOG_MODULE
            , type = LogModule.ShipmentPlanOperatorLogModule.EXPORT_BARCODE
            , desc = LogModule.ShipmentPlanOperatorLogModule.EXPORT_BARCODE_DESC)
    public void saveExportLog(Integer id) {
        LogRecordContextHolder.putRecordData(id.toString(), new Object(), new Object());
    }

    public List<String> findAllShippingMarkByOrderNo(String poNo) {
        List<PurchaseOrderFile> domains = purchaseOrderFileRepository.findByNoAndType(poNo, PmsOrderFileType.PD_ORDER_MARK);
        return domains.stream().map(PurchaseOrderFile::getFileId).toList();
    }

    private void generateShipmentLog(List<String> pdNos, List<ShipmentPlanOrder> shipmentPlanOrders) {
        List<ShipmentPlanOrder> newShipmentPlanOrders = shipmentPlanOrderLookup.findByPdNoList(pdNos);
        Map<String, ShipmentPlanOrder> logMap = newShipmentPlanOrders.stream().collect(Collectors.toMap(ShipmentPlanOrder::getPdNo, Function.identity()));
        shipmentPlanOrders.forEach(oldShipmentPlanOrder -> LogRecordContextHolder.putRecordData(oldShipmentPlanOrder.getId().id().toString(), oldShipmentPlanOrder, logMap.get(oldShipmentPlanOrder.getPdNo())));
    }

    private void fullFillShipmentPlanOrder(List<ShipmentPlanOrder> shipmentPlanOrders, List<SkuMappingLineVO> skuMappingLineList, List<PlatformWarehouse> platformWarehouses) {
        if (shipmentPlanOrders == null || skuMappingLineList == null || platformWarehouses == null) {
            return; // 防止空指针异常
        }

        // 构建映射表
        Map<String, List<SkuMappingLineVO>> skuMappingLineMap = skuMappingLineList.stream()
                .collect(Collectors.groupingBy(e -> buildFnSkuKey(e.getStoreId(), e.getPsku(), e.getChannelId())));
        Map<String, List<PlatformWarehouse>> platformWarehouseMap = platformWarehouses.stream()
                .collect(Collectors.groupingBy(e -> buildPlatformWarehouseKey(e.getSalesChannel().getId().id(), e.getSite().getCode())));

        shipmentPlanOrders.forEach(shipmentPlanOrder -> {
            String fnSkuKey = buildFnSkuKey(shipmentPlanOrder);
            String platformWarehouseKey = buildPlatformWarehouseKey(shipmentPlanOrder);

            if (fnSkuKey != null && skuMappingLineMap.containsKey(fnSkuKey)) {
                shipmentPlanOrder.setFnSkus(skuMappingLineMap.get(fnSkuKey).stream()
                        .map(e -> new Attr(e.getFnsku(), e.getFnsku())).distinct()
                        .toList());
            }

            if (shipmentPlanOrder.getItem() != null &&
                    WarehouseTypeEnum.PLATFORM_WAREHOUSE.getCode().equals(shipmentPlanOrder.getItem().getDestWarehouseType())) {
                if (platformWarehouseKey != null && platformWarehouseMap.containsKey(platformWarehouseKey)) {
                    shipmentPlanOrder.setWarehouseCodes(platformWarehouseMap.get(platformWarehouseKey).stream()
                            .map(e -> new Attr(e.getCode(), e.getCode())).distinct()
                            .toList());
                }
            } else {
                shipmentPlanOrder.setWarehouseCodes(List.of(new Attr(
                        Optional.ofNullable(shipmentPlanOrder.getItem()).map(ShipmentPlanOrderItem::getDestWarehouseCode).orElse(null),
                        Optional.ofNullable(shipmentPlanOrder.getItem()).map(ShipmentPlanOrderItem::getDestWarehouseCode).orElse(null)
                )));
            }
        });
    }

    private <T, R> List<R> extractDistinctField(List<T> list, Function<T, R> extractor) {
        if (list == null || list.isEmpty()) {
            return Collections.emptyList();
        }
        return list.stream()
                .map(extractor)
                .filter(Objects::nonNull)
                .distinct()
                .toList();
    }


    private String buildFnSkuKey(ShipmentPlanOrder shipmentPlanOrder) {
        return buildFnSkuKey(
                Optional.ofNullable(shipmentPlanOrder.getStoreId()).map(StoreId::id).orElse(null),
                shipmentPlanOrder.getPsku(),
                Optional.ofNullable(shipmentPlanOrder.getSalesChannelId()).map(SalesChannelId::id).orElse(null)
        );
    }

    private String buildFnSkuKey(Integer storeId, String pSku, Integer channelId) {
        return (storeId != null && pSku != null && Objects.nonNull(channelId)) ?
                String.join("_", storeId.toString(), pSku, channelId.toString()) : null;
    }

    private String buildPlatformWarehouseKey(ShipmentPlanOrder shipmentPlanOrder) {
        return buildPlatformWarehouseKey(
                Optional.ofNullable(shipmentPlanOrder.getSalesChannelId()).map(SalesChannelId::id).orElse(null),
                shipmentPlanOrder.getSiteCode()
        );
    }

    private String buildPlatformWarehouseKey(Integer salesChannelId, String siteCode) {
        return (Objects.nonNull(salesChannelId) && siteCode != null) ?
                String.join("_", salesChannelId.toString(), siteCode) : null;
    }

    private void enrichShipmentPlanOrders(List<ShipmentPlanOrder> records, Map<String, ShipmentPlanOrderItem> shipmentPlanOrderItemMap,
                                          Map<WarehouseId, Warehouse> warehouseMap, Map<StoreId, Store> storeMap,
                                          Map<SalesChannelId, SalesChannel> salesChannelMap,
                                          Map<PurchaseOrderId, PurchaseOrderSupplierInfo> supplierInfoMap,
                                          Map<String, List<PurchaseOrderFile>> fileMap) {
        records.forEach(shipmentPlanOrder -> {
            String pdNo = shipmentPlanOrder.getPdNo();
            ShipmentPlanOrderItem item = shipmentPlanOrderItemMap.get(pdNo);
            if (item != null) {
                if (Objects.nonNull(item.getDestWarehouseId())) {
                    item.setDestWarehouse(warehouseMap.get(item.getDestWarehouseId()));
                }
                shipmentPlanOrder.setItem(item);
            }
            shipmentPlanOrder.setStore(storeMap.get(shipmentPlanOrder.getStoreId()));
            shipmentPlanOrder.setSalesChannel(salesChannelMap.get(shipmentPlanOrder.getSalesChannelId()));
            if (supplierInfoMap.containsKey(new PurchaseOrderId(shipmentPlanOrder.getPoId()))) {
                shipmentPlanOrder.setSupplierInfo(supplierInfoMap.get(new PurchaseOrderId(shipmentPlanOrder.getPoId())));
            }
            if (fileMap.containsKey(pdNo)) {
                shipmentPlanOrder.setFileList(fileMap.get(pdNo));
            }
        });
    }


    private List<Operator> extractUserIds(List<ShipmentPlanOrder> records) {
        List<Operator> userIdList = new ArrayList<>();
        for (ShipmentPlanOrder order : records) {
            userIdList.add(order.getOperationStaff());
            userIdList.add(order.getPlanStaff());
            userIdList.add(order.getPurchaserStaff());
        }
        return userIdList;
    }

    private void buildShipmentPlanExport(List<ShipmentPlanExport> shipmentPlanExports, List<ShipmentPlanOrder> records) {
        // 遍历每个采购订单记录，构建对应的导出DTO
        records.forEach(shipmentPlanOrder -> {
            // 创建一个新的采购订单导出DTO实例
            ShipmentPlanExport exportDto = new ShipmentPlanExport();
            // 填充字段
            populateBasicFields(exportDto, shipmentPlanOrder);
            // 将构建好的DTO添加到列表中
            shipmentPlanExports.add(exportDto);
        });
    }

    private void populateBasicFields(ShipmentPlanExport exportDto, ShipmentPlanOrder shipmentPlanOrder) {

        exportDto.setPdNo(shipmentPlanOrder.getPdNo());
        exportDto.setStatusDesc(ShipmentPlanOrderStatusEnum.getEnums(shipmentPlanOrder.getStatus().getValue()).getDescCn());
        // 设置采购订单号
        exportDto.setPoNo(shipmentPlanOrder.getPoNo());
        exportDto.setPrNo(shipmentPlanOrder.getPrNo());

        exportDto.setSiteCode(shipmentPlanOrder.getSiteCode());
        exportDto.setPlatformName(shipmentPlanOrder.getSalesChannel().getChannelName());
        exportDto.setStoreName(Objects.nonNull(shipmentPlanOrder.getStore()) ? shipmentPlanOrder.getStore().getName() : "");

        // 设置采购SKU
        exportDto.setPsku(shipmentPlanOrder.getPsku());
        exportDto.setPdQty(String.valueOf(shipmentPlanOrder.getItem().getPdQty()));
        exportDto.setFnSku(shipmentPlanOrder.getItem().getFnSku());
        exportDto.setShipmentId(shipmentPlanOrder.getShipmentID());
        exportDto.setReferenceId(shipmentPlanOrder.getReferenceID());
        exportDto.setFirstLegMethod(shipmentPlanOrder.getItem().getFirstLegMethod());
        exportDto.setFirstLegType(shipmentPlanOrder.getItem().getFirstLegType());
        exportDto.setLogisticsType(shipmentPlanOrder.getItem().getLogisticsType());
        exportDto.setShipWarehouseMethod(shipmentPlanOrder.getItem().getShipWarehouseMethod());

        if (Objects.nonNull(shipmentPlanOrder.getItem().getDestWarehouse())) {
            exportDto.setWarehouseName(shipmentPlanOrder.getItem().getDestWarehouse().getName());
        }
        exportDto.setDestWarehouseCode(shipmentPlanOrder.getItem().getDestWarehouseCode());
        exportDto.setLastDeliveryDate(shipmentPlanOrder.getItem().getLastDeliveryDate() != null ? shipmentPlanOrder.getItem().getLastDeliveryDate().toString() : "");
        exportDto.setSupplier(shipmentPlanOrder.getSupplierInfo().getSupplierCode() + "(" + shipmentPlanOrder.getSupplierInfo().getSupplierShortName() + ")");
        exportDto.setExpectedPdDate(shipmentPlanOrder.getItem().getExpectedPdDate() != null ? shipmentPlanOrder.getItem().getExpectedPdDate().toString() : "");

        exportDto.setPdRemark(shipmentPlanOrder.getItem().getPdRemark());
        exportDto.setOperationStaff(shipmentPlanOrder.getOperationStaff().getOperatorName() + "(" + shipmentPlanOrder.getOperationStaff().getOperatorCode() + ")");
        exportDto.setPlanStaff(shipmentPlanOrder.getPlanStaff().getOperatorName() + "(" + shipmentPlanOrder.getPlanStaff().getOperatorCode() + ")");
        exportDto.setPurchaser(shipmentPlanOrder.getPurchaserStaff().getOperatorName() + "(" + shipmentPlanOrder.getPurchaserStaff().getOperatorCode() + ")");
        exportDto.setBarcodeSyncFlag(shipmentPlanOrder.getBarcodeSyncFlag() == 1 ? "已同步" : "未同步");

        exportDto.setCartonMarkSyncFlag(shipmentPlanOrder.getCartonMarkSyncFlag() == 1 ? "已同步" : "未同步");
        exportDto.setCreateTime(shipmentPlanOrder.getCreated().getOperateTime());
        exportDto.setReviewTime(shipmentPlanOrder.getReviewTime());
        exportDto.setConfirmTime(shipmentPlanOrder.getConfirmTime());
        exportDto.setCancelTime(shipmentPlanOrder.getCancelTime());
    }


    public ShipmentPlanVo getShipmentPlanVo(String pdNo) {
        ShipmentPlanVo shipmentPlanVo = new ShipmentPlanVo();
        ShipmentPlanOrder shipmentPlanOrder = shipmentPlanOrderLookup.findByPdNo(pdNo).orElseThrow(() ->
                new ApiException(BizErrorCode.DATA_DOES_NOT_EXIST));

        ShipmentPlanOrderItem item = shipmentPlanOrderItemLookup.findByPdNoList(Collections.singletonList(pdNo)).get(0);
        SalesChannel salesChannel = salesChannelLookup.findById(shipmentPlanOrder.getSalesChannelId()).orElseThrow(() ->
                new ApiException(BizErrorCode.DATA_DOES_NOT_EXIST));
        String fnSku = item.getFnSku();
        if (StringUtils.isNotBlank(fnSku)) {
            try {
                shipmentPlanVo.setShipmentId(shipmentPlanOrder.getShipmentID());
                shipmentPlanVo.setPoNo(shipmentPlanOrder.getPoNo());
                shipmentPlanVo.setSiteCode(shipmentPlanOrder.getSiteCode());
                shipmentPlanVo.setBarCode(fnSku);
                shipmentPlanVo.setBarCodeImage(CustomBarcodePDFUtil.pdfOSToBase64Image(CustomBarcodePDFUtil.generateFnskuPDF(70f,
                        20f,
                        salesChannel.getChannelName(),
                        fnSku,
                        shipmentPlanOrder.getId().id())));
                //向上取整
                shipmentPlanVo.setPdCartonNum((item.getPdQty() + item.getBoxPcs() - 1) / item.getBoxPcs());
                if (StringUtils.isNotBlank(shipmentPlanOrder.getCartonImageFileId())) {
                    Map<String, String> fileInfo = remoteFileFeign.getFileInfo(Collections.singletonList(shipmentPlanOrder.getCartonImageFileId()));
                    shipmentPlanVo.setCartonMarkUrl(fileInfo.get(shipmentPlanOrder.getCartonImageFileId()));
                }
            } catch (Exception e) {
                log.error("获取pd信息异常", e);
                throw new RuntimeException(e);
            }

        }
        return shipmentPlanVo;
    }

    public List<ShipmentPlanOrder> getDetail(Collection<Integer> idList, Collection<String> pdNoList) {
        List<ShipmentPlanOrder> shipmentPlanOrderList;
        if (CollUtil.isNotEmpty(idList)) {
            shipmentPlanOrderList = shipmentPlanOrderLookup.findByPdIds(idList.stream().distinct().map(ShipmentPlanId::new).toList());
        } else if (CollUtil.isNotEmpty(pdNoList)) {
            shipmentPlanOrderList = shipmentPlanOrderLookup.findByPdNoList(pdNoList);
        } else {
            throw new BusinessException("params.is.not.null");
        }
        if (CollUtil.isNotEmpty(shipmentPlanOrderList)) {
            Map<String, ShipmentPlanOrderItem> orderItemMap = shipmentPlanOrderItemLookup.findByPdNoList(
                    shipmentPlanOrderList.stream().map(ShipmentPlanOrder::getPdNo).toList()
            ).stream().collect(Collectors.toMap(ShipmentPlanOrderItem::getPdNo, Function.identity()));
            Map<Integer, PurchaseOrder> purchaseOrderMap = purchaseOrderService.getDetail(
                    shipmentPlanOrderList.stream().map(ShipmentPlanOrder::getPoId).distinct().toList(), null
            ).stream().collect(Collectors.toMap(e -> e.getId().id(), Function.identity()));
            for (ShipmentPlanOrder shipmentPlanOrder : shipmentPlanOrderList) {
                shipmentPlanOrder.setItem(orderItemMap.get(shipmentPlanOrder.getPdNo()));
                shipmentPlanOrder.setPo(purchaseOrderMap.get(shipmentPlanOrder.getPoId()));
            }
            return shipmentPlanOrderList;
        }
        return Collections.emptyList();
    }

    /**
     * 申请质检
     */
    @Transactional(rollbackFor = Exception.class)
    public void pdApplyQc(ShipmentPlanOrderApplyRequest request) {
        ShipmentPlanOrder pdOrderInfoAggOnlyMysql = shipmentPlanOrderQueryService.getPdOrderInfoAggOnlyMysql(request.getPdNo());
        ShipmentPlanOrderItem pdItem = pdOrderInfoAggOnlyMysql.getItem();
        if (pdItem.getSqId() == null && pdItem.getSqNo() == null) {
            pdItem.setQcStatus(QmsQcStatusEnum.PENDING.getCode());
            pdItem.setSqId(request.getSqId());
            pdItem.setSqNo(request.getSqNo());
            pdItem.setQcId(request.getQcId());
            pdItem.setQcNo(request.getQcNo());
            log.info("更新PD Item数据,{}", JSONKit.toJSONString(pdItem));
            shipmentPlanOrderItemRepository.batchUpdateShipmentPlanOrderItem(List.of(pdItem));

            // 第一次质检申请 同步IMS
            syncTaskManager.createTask(SyncNodeEnum.PD_SQ_APPLY.getNodeCode(), pdOrderInfoAggOnlyMysql.getPdNo());
        } else {
            pdItem.setQcStatus(QmsQcStatusEnum.PENDING.getCode());
            pdItem.setSqId(request.getSqId());
            pdItem.setSqNo(request.getSqNo());
            pdItem.setQcId(request.getQcId());
            pdItem.setQcNo(request.getQcNo());
            log.info("更新PD Item数据,{}", JSONKit.toJSONString(pdItem));
            shipmentPlanOrderItemRepository.batchUpdateShipmentPlanOrderItem(List.of(pdItem));
        }
    }

    /**
     * 完成质检ment
     */
    @Transactional(rollbackFor = Exception.class)
    public void pdFinishQc(ShipmentPlanOrderFinishQcRequest request) {
        ShipmentPlanOrder pdOrderInfoAggOnlyMysql = shipmentPlanOrderQueryService.getPdOrderInfoAggOnlyMysql(request.getPdNo());
        ShipmentPlanOrderItem pdItem = pdOrderInfoAggOnlyMysql.getItem();

        pdItem.setQcStatus(QmsQcStatusEnum.PASS.getCode());
        pdItem.setSqId(request.getSqId());
        pdItem.setSqNo(request.getSqNo());
        pdItem.setQcId(request.getQcId());
        pdItem.setQcNo(request.getQcNo());
        pdItem.setQcFailQty(request.getQcFailQty());
        pdItem.setQcPassQty(request.getQcPassQty());
        log.info("更新PD Item数据,{}", JSONKit.toJSONString(pdItem));
        shipmentPlanOrderItemRepository.batchUpdateShipmentPlanOrderItem(List.of(pdItem));

        // 质检完成 同步IMS
        syncTaskManager.createTask(SyncNodeEnum.PD_QC_FINISH.getNodeCode(), pdOrderInfoAggOnlyMysql.getPdNo());
    }

    /**
     * 记录邮件日志
     */
    @LogRecord(module = LogModule.SHIPMENT_PLAN_LOG_MODULE
            , type = LogModule.CommonDesc.SEND_MAIL
            , desc = LogModule.CommonDesc.SEND_MAIL_DESC)
    public void sendMailLog(List<ShipmentPlanOrder> shipmentPlanOrderList,Integer flag, String errorMessage){
        for (ShipmentPlanOrder shipmentPlanOrder : shipmentPlanOrderList) {

            ShipmentPlanOrder old = SerializationUtils.clone(shipmentPlanOrder);
            shipmentPlanOrder.setSendMailFlag(flag);
            shipmentPlanOrder.setMailErrorMsg(errorMessage);

            LogRecordContextHolder.putRecordData(shipmentPlanOrder.getId().id().toString(), old, shipmentPlanOrder);
        }
    }

    /**
     * 同步TR信息--更新PD Item的TR单号
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(module = LogModule.SHIPMENT_PLAN_LOG_MODULE
            , type = LogModule.CommonDesc.EDIT_OPERATOR
            , desc = LogModule.CommonDesc.EDIT_DESC)
    public void syncTrInfo(Integer trId,String trNo,String pdNo) {
        ShipmentPlanOrderItem shipmentPlanOrderItem = shipmentPlanOrderItemLookup.findByPdNoList(List.of(pdNo))
                .stream()
                .findFirst()
                .orElseThrow(() -> new ErrorCodeException(BizErrorCode.DATA_DOES_NOT_EXIST));
        ShipmentPlanOrderItem old = SerializationUtils.clone(shipmentPlanOrderItem);
        shipmentPlanOrderItem.setTrId(trId);
        shipmentPlanOrderItem.setTrNo(trNo);
        shipmentPlanOrderItemRepository.updateTrByPdNo(shipmentPlanOrderItem);
        LogRecordContextHolder.putRecordData(String.valueOf(shipmentPlanOrderItem.getPdId()), old, shipmentPlanOrderItem);
    }

    /**
     * 同步TR货交承运人状态--PD收货
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(module = LogModule.SHIPMENT_PLAN_LOG_MODULE
            , type = LogModule.CommonDesc.RECEIVE_OPERATOR
            , desc = LogModule.CommonDesc.RECEIVE_DESC)
    public void syncTrDeliveryStatus(LocalDate trDeliveryDate, String pdNo) {
        // 1. 更新PD Item的收货数量、货交承运人时间
        ShipmentPlanOrderItem shipmentPlanOrderItem = shipmentPlanOrderItemLookup.findByPdNoList(List.of(pdNo))
                .stream()
                .findFirst()
                .orElseThrow(() -> new ErrorCodeException(BizErrorCode.DATA_DOES_NOT_EXIST));
        ShipmentPlanOrderItem oldPdItem = SerializationUtils.clone(shipmentPlanOrderItem);
        shipmentPlanOrderItem.setTrDeliveryDate(trDeliveryDate);
        shipmentPlanOrderItem.setReceiveQty(shipmentPlanOrderItem.getPdQty());
        shipmentPlanOrderItemRepository.updateTrByPdNo(shipmentPlanOrderItem);

        // 2. 更新PD单收货状态
        ShipmentPlanOrder shipmentPlan = shipmentPlanOrderLookup.findByPdNo(shipmentPlanOrderItem.getPdNo())
                .orElseThrow(() -> new ApiException(BizErrorCode.DATA_DOES_NOT_EXIST));
        ShipmentPlanOrder oldPd = SerializationUtils.clone(shipmentPlan);
        shipmentPlan.setReceiptStatus(PdReceiptStatusEnum.FULL_RECEIVE.getCode());
        shipmentPlan.setReceiptDate(trDeliveryDate);
        shipmentPlanOrderRepository.updateReceiptStatus(shipmentPlan);

        // 3. 更新关联PO单状态
        PurchaseOrderId poId = new PurchaseOrderId(shipmentPlan.getPoId());
        List<ShipmentPlanOrder> pdOrders = shipmentPlanOrderLookup.findByPoId(poId);
        List<String> pdNos = pdOrders.stream().map(ShipmentPlanOrder::getPdNo).toList();
        // 计算总收货数量
        int totalReceiveQty = shipmentPlanOrderItemLookup.findByPdNoList(pdNos)
                .stream()
                .mapToInt(item -> Optional.ofNullable(item.getReceiveQty()).orElse(0))
                .sum();
        // 更新PO状态
        purchaseOrderLookup.findById(poId).ifPresent(po -> {
            PurchaseOrder oldPo = SerializationUtils.clone(po);
            po.setStatus(totalReceiveQty >= po.getPoQty() ?
                    PurchaseOrderStatusEnum.COMPLETED : PurchaseOrderStatusEnum.PART_RECEIVE);
            purchaseOrderRepository.updateById(po);
            // 记录日志
            oldPd.setPo(oldPo);
            shipmentPlan.setPo(po);
        });
        // 4. 记录日志
        oldPd.setItem(oldPdItem);
        shipmentPlan.setItem(shipmentPlanOrderItem);
        LogRecordContextHolder.putRecordData(shipmentPlan.getId().id().toString(), oldPd, shipmentPlan);
    }


    /**
     * 同步TR信息--更新PD的 shipment 和 包材文件
     */
    @Transactional(rollbackFor = Exception.class)
    public void syncTrShipmentIdAndBoxMarkFile(String pdNo,String shipmentId,String boxMarkFileId){
        // 查询PD 单
        Optional<ShipmentPlanOrder> byPdNo = shipmentPlanOrderLookup.findByPdNo(pdNo);
        byPdNo.ifPresent(pd -> {
            pd.setShipmentID(shipmentId);

            if (boxMarkFileId != null) {
                PurchaseOrderFile purchaseOrderFile = new PurchaseOrderFile(null);
                purchaseOrderFile.setOrderNo(pdNo);
                purchaseOrderFile.setBusinessType(PmsFileBusinessType.PD_ORDER.getCode());
                purchaseOrderFile.setFileType(PmsOrderFileType.PD_ORDER_MARK.getCode());
                purchaseOrderFile.setOrderId(pd.getId().id());
                purchaseOrderFile.setFileId(boxMarkFileId);

                purchaseOrderFile.setUpdateBy(0);
                purchaseOrderFile.setCreateBy(0);

                purchaseOrderFileRepository.saveData(purchaseOrderFile);
            }

            shipmentPlanOrderRepository.updateShipmentId(pdNo,shipmentId);

             // 异步推送 邮件
            pdMailService.pdSendMail(List.of(pdNo) , false);

        });
    }

    public List<ShipmentPlanOrder> getBaseInfo(Collection<Integer> idList, Collection<String> pdNoList) {
        List<ShipmentPlanOrder> shipmentPlanOrderList;
        if (CollUtil.isNotEmpty(idList)) {
            shipmentPlanOrderList = shipmentPlanOrderLookup.findByPdIds(idList.stream().distinct().map(ShipmentPlanId::new).toList());
        } else if (CollUtil.isNotEmpty(pdNoList)) {
            shipmentPlanOrderList = shipmentPlanOrderLookup.findByPdNoList(pdNoList);
        } else {
            throw new BusinessException("params.is.not.null");
        }
        return shipmentPlanOrderList;
    }
}
