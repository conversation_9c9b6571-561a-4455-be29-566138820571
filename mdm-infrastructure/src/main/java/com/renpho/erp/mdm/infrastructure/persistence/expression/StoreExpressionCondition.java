package com.renpho.erp.mdm.infrastructure.persistence.expression;

import com.renpho.erp.data.permission.expression.ExpressionBuilder;
import com.renpho.erp.data.permission.expression.ExpressionCondition;
import com.renpho.erp.mdm.infrastructure.manager.StorePermissionManager;
import com.renpho.erp.security.constant.RoleLabelEnum;
import com.renpho.erp.security.util.SecurityUtils;
import lombok.RequiredArgsConstructor;
import net.sf.jsqlparser.expression.Expression;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.Objects;
import java.util.Set;

/**
 * 店铺数据权限表达式
 * <AUTHOR>
 */
@Component
@RequiredArgsConstructor
public class StoreExpressionCondition implements ExpressionCondition {

    private final StorePermissionManager storePermissionManager;

    @Override
    public Expression getExpression() {
        // 角色标签
        String[] roleLabels = SecurityUtils.getUserLabels();
        // 不存在运营标签（运营管理、运营人员），拥有所有店铺数据权限
        if (Objects.isNull(roleLabels) || Arrays.stream(roleLabels).noneMatch(RoleLabelEnum::isOperations)) {
            return null;
        }
        // 获取用户的店铺数据权限
        Set<Integer> storeIds = storePermissionManager.findPermission(SecurityUtils.getUserId());
        // 没有店铺数据权限
        if (CollectionUtils.isEmpty(storeIds)) {
            // 构建不成立的表达式（1 = 0）
            return ExpressionBuilder.buildImpossibleExpression();
        }
        // 店铺数据权限过滤
        return ExpressionBuilder.buildInExpression("id", storeIds);
    }

}
