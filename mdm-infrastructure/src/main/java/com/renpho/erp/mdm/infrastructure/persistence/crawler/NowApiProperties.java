package com.renpho.erp.mdm.infrastructure.persistence.crawler;

import lombok.Data;
import lombok.Getter;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Component;
@RefreshScope
@Getter
@Data
@Component
@ConfigurationProperties(prefix = "renpho.karma")
public class NowApiProperties {
    private NowApi nowApi;


    @Data
    public static class NowApi {
        private String appKey;
        private String sign;
    }

}