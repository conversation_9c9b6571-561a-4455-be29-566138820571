package com.renpho.erp.mdm.infrastructure.persistence.repository.store;

import com.baomidou.mybatisplus.core.enums.SqlMethod;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.renpho.erp.mdm.domain.payment.Payment;
import com.renpho.erp.mdm.domain.payment.PaymentId;
import com.renpho.erp.mdm.domain.store.StoreId;
import com.renpho.erp.mdm.domain.store.StorePayment;
import com.renpho.erp.mdm.domain.store.StorePaymentRepository;
import com.renpho.erp.mdm.infrastructure.persistence.mapper.store.StorePaymentMapper;
import com.renpho.erp.mdm.infrastructure.persistence.po.store.StorePaymentPo;
import com.renpho.erp.mdm.infrastructure.persistence.po.store.converter.StorePaymentConverter;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Repository;

import java.util.Collection;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2024/9/19
 */
@Repository
public class StorePaymentRepositoryImpl extends ServiceImpl<StorePaymentMapper, StorePaymentPo>
		implements StorePaymentRepository {

	private final StorePaymentConverter storePaymentConverter;

	@Override
	public Map<StoreId, List<StorePayment>> findByStoreIds(Collection<StoreId> storeIds) {
		Map<StoreId, List<StorePaymentPo>> companyGroupByStoreId = CollectionUtils
				.emptyIfNull(lambdaQuery()
						.in(StorePaymentPo::getStoreId, storeIds.stream().map(StoreId::getId).toList()).list())
				.stream().collect(Collectors.groupingBy(storeCompanyPo -> new StoreId(storeCompanyPo.getStoreId())));
		List<PaymentId> paymentIds = companyGroupByStoreId.values().stream().flatMap(Collection::stream)
				.map(StorePaymentPo::getPaymentId).distinct().map(PaymentId::new).toList();
		// TODO 根据 companyId 查询站点实体
		Map<Integer, Payment> paymentGroupById = paymentIds.stream().map(Payment::new)
				.collect(Collectors.toMap(company -> company.getId().getId(), Function.identity()));

		Map<StoreId, List<StorePayment>> map = new HashMap<>(companyGroupByStoreId.size());
		for (Map.Entry<StoreId, List<StorePaymentPo>> entry : companyGroupByStoreId.entrySet()) {
			List<StorePayment> storeCompanies = CollectionUtils.emptyIfNull(entry.getValue()).stream()
					.map(po -> storePaymentConverter.toDomain(po, paymentGroupById.get(po.getPaymentId()))).toList();
			map.put(entry.getKey(), storeCompanies);
		}
		return map;
	}

	@Override
	public void saveBatch(StoreId id, Collection<StorePayment> payments) {
		if (CollectionUtils.isNotEmpty(payments)) {
			lambdaUpdate().eq(StorePaymentPo::getStoreId, id.getId()).in(StorePaymentPo::getPaymentId,
					payments.stream().map(payment -> payment.getPayment().getId().getId()).toList()).remove();
			List<StorePaymentPo> pos = payments.stream()
					.map(storePayment -> storePaymentConverter.toPo(storePayment, id)).toList();
			String sqlStatement = getSqlStatement(SqlMethod.INSERT_ONE);
			executeBatch(pos, DEFAULT_BATCH_SIZE, (sqlSession, entity) -> sqlSession.insert(sqlStatement, entity));
		}
	}

	@Override
	public List<StorePayment> findByStoreId(StoreId storeId) {
		List<StorePaymentPo> pos = lambdaQuery().eq(StorePaymentPo::getStoreId, storeId.getId()).list();
		if (CollectionUtils.isEmpty(pos)) {
			return List.of();
		}
		List<PaymentId> list = pos.stream().map(po -> new PaymentId(po.getPaymentId())).toList();
		// TODO 根据 paymentId 查询站点实体
		Map<PaymentId, Payment> companyById = list.stream().map(Payment::new)
				.collect(Collectors.toMap(Payment::getId, Function.identity()));
		return pos.stream()
				.map(po -> storePaymentConverter.toDomain(po, companyById.get(new PaymentId(po.getPaymentId()))))
				.toList();
	}

	public StorePaymentRepositoryImpl(StorePaymentConverter storePaymentConverter) {
		this.storePaymentConverter = storePaymentConverter;
	}

}
