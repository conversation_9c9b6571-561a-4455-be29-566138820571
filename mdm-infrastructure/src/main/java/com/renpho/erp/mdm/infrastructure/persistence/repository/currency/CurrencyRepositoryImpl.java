package com.renpho.erp.mdm.infrastructure.persistence.repository.currency;

import java.util.*;
import java.util.stream.Collectors;

import org.apache.commons.collections4.CollectionUtils;
import org.springframework.cloud.stream.function.StreamBridge;
import org.springframework.messaging.support.MessageBuilder;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.support.TransactionSynchronization;
import org.springframework.transaction.support.TransactionSynchronizationManager;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.renpho.erp.mdm.domain.currency.Currency;
import com.renpho.erp.mdm.domain.currency.CurrencyRepository;
import com.renpho.erp.mdm.domain.exception.DataDuplicateException;
import com.renpho.erp.mdm.infrastructure.feignclient.oum.RemoteUserDetailsFeign;
import com.renpho.erp.mdm.infrastructure.persistence.mapper.currency.CurrencyLanguageMapper;
import com.renpho.erp.mdm.infrastructure.persistence.mapper.currency.CurrencyMapper;
import com.renpho.erp.mdm.infrastructure.persistence.ns.CurrencyNsResponse;
import com.renpho.erp.mdm.infrastructure.persistence.ns.NsApiEndpointsConfig;
import com.renpho.erp.mdm.infrastructure.persistence.po.currency.CurrencyLanguagePO;
import com.renpho.erp.mdm.infrastructure.persistence.po.currency.CurrencyPO;
import com.renpho.erp.mdm.infrastructure.persistence.po.transformer.CurrencyTransformer;
import com.renpho.erp.mdm.infrastructure.persistence.repository.costcenter.NsUtils;
import com.renpho.erp.mdm.infrastructure.persistence.repository.rate.CurrencyNsRequest;
import com.renpho.erp.security.util.SecurityUtils;
import com.renpho.karma.cloud.mybatisplus.po.CreationPO;
import com.renpho.karma.i18n.I18nMessageKit;

import cn.hutool.core.util.StrUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * 货币仓储实现.
 *
 * <AUTHOR>
 */
@Slf4j
@Repository
@RequiredArgsConstructor
public class CurrencyRepositoryImpl extends ServiceImpl<CurrencyMapper, CurrencyPO> implements CurrencyRepository {
	private final StreamBridge streamBridgeTemplate;
	private final CurrencyMapper currencyMapper;
	private final CurrencyLanguageMapper currencyLanguageMapper;
	private final RemoteUserDetailsFeign remoteUserDetailsFeign;

	@Override
	public void syncNsById(Integer id) {
		CurrencyPO currencyPO = currencyMapper
			.selectOne(Wrappers.<CurrencyPO> lambdaQuery().select(CurrencyPO::getCode).eq(CreationPO::getId, id).last("limit 1"));
		if (currencyPO == null) {
			return;
		}
		String code = currencyPO.getCode();
		if (code == null) {
			return;
		}
		Map<String, String> dataMap = getCurrencyMap(Collections.singletonList(code));
		CurrencyRepositoryImpl.log.info("货币币种同步 ns 消息: [{}]", id);
		syncNs(id, dataMap);
	}

	@Transactional(rollbackFor = Exception.class)
	@Override
	public Integer save(Currency currency) {
		CurrencyPO currencyPO = CurrencyTransformer.INSTANCE.domain2PO(currency);
		checkCode(currencyPO);
		currencyPO.setUpdateName(remoteUserDetailsFeign.getUserDetails(SecurityUtils.getUser().getName()).getUserInfo().getName());
		currencyPO.setStatus(1);
		currencyMapper.insert(currencyPO);
		List<CurrencyLanguagePO> languages = currencyPO.getLanguages();

		if (CollectionUtils.isNotEmpty(languages)) {
			for (CurrencyLanguagePO language : languages) {
				language.setId(null);
				language.setCurrencyId(currencyPO.getId());
				currencyLanguageMapper.insert(language);
			}
		}

		afterCommitPush(currencyPO);
		return currencyPO.getId();
	}

	private void afterCommitPush(CurrencyPO currencyPO) {
		if (currencyPO == null) {
			return;
		}
		Integer id = currencyPO.getId();
		if (id == null) {
			return;
		}
		TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronization() {
			@Override
			public void afterCommit() {
				if (NsApiEndpointsConfig.getNsApiEndpointsConfig()
					.getNsApiEndPoints()
					.get(NsApiEndpointsConfig.getCurrencyProfile())
					.isUseMq()) {
					boolean send = streamBridgeTemplate.send("produceCurrencySyncNS-out-0", MessageBuilder.withPayload(id).build());
					System.out.println(send);
				}
				else {
					syncNsById(id);
				}

			}
		});
	}

	/**
	 * 导入 存在相同货币,覆盖数据
	 */
	@Transactional(rollbackFor = Exception.class)
	@Override
	public Currency importSave(Currency currency) {
		CurrencyPO currencyPO = CurrencyTransformer.INSTANCE.domain2PO(currency);
		currencyPO.setStatus(1);
		String name = remoteUserDetailsFeign.getUserDetails(SecurityUtils.getUser().getName()).getUserInfo().getName();
		currencyPO.setUpdateName(name);
		CurrencyPO dbCurrencyPO = currencyMapper
			.selectOne(Wrappers.<CurrencyPO> lambdaQuery().eq(CurrencyPO::getCode, currencyPO.getCode()).last("limit 1"));

		Integer currencyId;
		if (Objects.isNull(dbCurrencyPO)) {
			currencyMapper.insert(currencyPO);
			currencyId = currencyPO.getId();
		}
		else {
			dbCurrencyPO.setSymbol(currencyPO.getSymbol());
			dbCurrencyPO.setNsId(currencyPO.getNsId());
			dbCurrencyPO.setLanguages(currencyPO.getLanguages());
			dbCurrencyPO.setStatus(currencyPO.getStatus());
			dbCurrencyPO.setUpdateName(name);
			currencyMapper.updateById(dbCurrencyPO);
			currencyId = dbCurrencyPO.getId();
		}
		// 先删除原来的
		currencyLanguageMapper.delete(Wrappers.<CurrencyLanguagePO> lambdaQuery().eq(CurrencyLanguagePO::getCurrencyId, currencyId));
		CurrencyLanguagePO cnCurrencyLanguagePO = new CurrencyLanguagePO();
		cnCurrencyLanguagePO.setCurrencyId(currencyId);
		cnCurrencyLanguagePO.setLanguage("zh-CN");
		cnCurrencyLanguagePO.setName(currency.getCurrencyCnName());
		currencyLanguageMapper.insert(cnCurrencyLanguagePO);

		CurrencyLanguagePO enCurrencyLanguagePO = new CurrencyLanguagePO();
		enCurrencyLanguagePO.setCurrencyId(currencyId);
		enCurrencyLanguagePO.setLanguage("en-US");
		enCurrencyLanguagePO.setName(currency.getCurrencyEnName());
		currencyLanguageMapper.insert(enCurrencyLanguagePO);
		currency.setId(Currency.CurrencyID.of(currencyId));
		afterCommitPush(currencyPO);
		return currency;
	}

	/**
	 * 检查code
	 */
	private void checkCode(CurrencyPO currencyPO) {
		List<Object> exit = currencyMapper.selectObjs(Wrappers.<CurrencyPO> lambdaQuery()
			.select(CurrencyPO::getId)
			.ne(Objects.nonNull(currencyPO.getId()), CreationPO::getId, currencyPO.getId())
			.eq(CurrencyPO::getCode, currencyPO.getCode())
			.last("limit 1"));
		if (CollectionUtils.isNotEmpty(exit)) {
			String message = I18nMessageKit.getMessage("currency_code");
			throw new DataDuplicateException(message);
		}
	}

	@Transactional(rollbackFor = Exception.class)
	@Override
	public void update(Currency currency) {
		CurrencyPO currencyPO = CurrencyTransformer.INSTANCE.domain2PO(currency);
		checkCode(currencyPO);
		currencyMapper.updateById(currencyPO);

		// 先删除原来的
		currencyLanguageMapper
			.delete(Wrappers.<CurrencyLanguagePO> lambdaQuery().eq(CurrencyLanguagePO::getCurrencyId, currencyPO.getId()));
		List<CurrencyLanguagePO> languages = currencyPO.getLanguages();
		// 后保存新的
		for (CurrencyLanguagePO language : languages) {
			language.setId(null);
			language.setCurrencyId(currencyPO.getId());
			currencyLanguageMapper.insert(language);
		}
		afterCommitPush(currencyPO);

	}

	@Override
	public void changeStatus(Currency currency) {
		CurrencyPO currencyPO = CurrencyTransformer.INSTANCE.domain2PO(currency);
		currencyMapper.update(null,
				Wrappers.<CurrencyPO> lambdaUpdate()
					.set(CurrencyPO::getStatus, currencyPO.getStatus())
					.eq(CurrencyPO::getId, currencyPO.getId()));
	}

	@Override
	public Optional<Currency> findById(Currency.CurrencyID currencyID) {
		CurrencyPO currencyPO = currencyMapper.selectById(currencyID.getId());
		Currency currency = CurrencyTransformer.INSTANCE.po2Domain(currencyPO);
		return Optional.ofNullable(currency);
	}

	@Override
	public void syncNs(Integer id, Map<String, String> dataMap) {
		CurrencyPO currencyPO = currencyMapper.selectById(id);
		if (currencyPO == null) {
			return;
		}
		String code = currencyPO.getCode();
		String nsId = dataMap.get(code);
		if (StrUtil.isNotBlank(nsId)) {
			// 改状态，设置id
			currencyMapper.update(Wrappers.<CurrencyPO> lambdaUpdate()
				.set(CurrencyPO::getNsStatus, 2)
				.set(CurrencyPO::getNsId, nsId)
				.eq(CreationPO::getId, id));
		}
		else {
			currencyMapper.update(Wrappers.<CurrencyPO> lambdaUpdate().set(CurrencyPO::getNsStatus, 3).eq(CreationPO::getId, id));
		}

	}

	@Override
	public void syncNsJob() {
		List<Integer> idList = currencyMapper
			.selectList(Wrappers.<CurrencyPO> lambdaQuery().select(CurrencyPO::getId).isNull(CurrencyPO::getNsId))
			.stream()
			.map(CurrencyPO::getId)
			.distinct()
			.toList();
		if (CollectionUtils.isEmpty(idList)) {
			return;
		}
		Map<String, String> dataMap = getCurrencyMap(Collections.emptyList());
		for (Integer id : idList) {
			try {
				syncNs(id, dataMap);
			}
			catch (Exception e) {
				log.error(e.getMessage(), e);
			}
		}
	}

	@Override
	public Map<String, String> getCurrencyMap(List<String> nameList) {
		CurrencyNsRequest currencyNsRequest = new CurrencyNsRequest();
		currencyNsRequest.setNameList(nameList);
		CurrencyNsResponse currencyNsResponse = NsUtils.sendRequestAndGetResult(NsApiEndpointsConfig.getCurrencyProfile(),
				currencyNsRequest, CurrencyNsResponse.class);
		List<CurrencyNsResponse.ResultBean> resultList = Optional.ofNullable(currencyNsResponse.getResult())
			.orElse(Collections.emptyList());
		return resultList.stream()
			.collect(Collectors.toMap(CurrencyNsResponse.ResultBean::getName, CurrencyNsResponse.ResultBean::getNsId, (v1, v2) -> v2));
	}

}
