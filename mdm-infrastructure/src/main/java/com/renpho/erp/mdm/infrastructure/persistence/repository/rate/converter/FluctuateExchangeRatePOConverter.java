package com.renpho.erp.mdm.infrastructure.persistence.repository.rate.converter;

import com.renpho.erp.mdm.domain.rate.FluctuateExchangeRate;
import com.renpho.erp.mdm.infrastructure.persistence.po.rate.FluctuateExchangeRatePO;
import org.mapstruct.Mapper;
import org.mapstruct.MappingConstants;
import org.mapstruct.ReportingPolicy;

import java.util.List;

@Mapper(unmappedTargetPolicy = ReportingPolicy.IGNORE,
        componentModel = MappingConstants.ComponentModel.SPRING)
public interface FluctuateExchangeRatePOConverter {

    FluctuateExchangeRatePO toPO(FluctuateExchangeRate domain);

    List<FluctuateExchangeRatePO> toPOs(List<FluctuateExchangeRate> domains);

    FluctuateExchangeRate toDomain(FluctuateExchangeRatePO po);

    List<FluctuateExchangeRate> toDomains(List<FluctuateExchangeRatePO> pos);
}
