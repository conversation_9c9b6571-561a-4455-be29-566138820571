package com.renpho.erp.mdm.infrastructure.persistence.repository.saleschannel;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.renpho.erp.mdm.domain.saleschannel.*;
import com.renpho.erp.mdm.infrastructure.persistence.mapper.saleschannel.SalesChannelMapper;
import com.renpho.erp.mdm.infrastructure.persistence.po.saleschannel.SalesChannelPo;
import com.renpho.erp.mdm.infrastructure.persistence.po.saleschannel.converter.SalesChannelConverter;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

/**
 * 销售渠道 Repository.
 *
 * <AUTHOR>
 * @since 2024/9/14
 */
@Repository
public class SalesChannelRepositoryImpl extends ServiceImpl<SalesChannelMapper, SalesChannelPo>
		implements SalesChannelRepository {

	private final SalesChannelLookup salesChannelLookup;

	private final SalesChannelSiteRepository saleschannelsiteRepository;

	private final SalesChannelConverter salesChannelConverter;

	@Override
	@Transactional(rollbackFor = Exception.class)
	public SalesChannel add(SalesChannel root) {
		root.assertNameNoDuplicate(salesChannelLookup);
		root.assertCodeNoDuplicate(salesChannelLookup);
		SalesChannelPo po = salesChannelConverter.toPo(root);
		save(po);
		saleschannelsiteRepository.saveBatch(new SalesChannelId(po.getId()), root.getAvailableSites());
		return salesChannelLookup.findById(new SalesChannelId(po.getId())).orElse(null);
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public SalesChannel update(SalesChannel root) {
		root.assertNameNoDuplicate(salesChannelLookup);
		root.assertCodeNoDuplicate(salesChannelLookup);
		SalesChannelPo po = salesChannelConverter.toPo(root);
		updateById(po);
		saleschannelsiteRepository.saveBatch(root.getId(), root.getAvailableSites());
		return salesChannelLookup.findById(new SalesChannelId(po.getId())).orElse(null);
	}

	public SalesChannelRepositoryImpl(SalesChannelLookup salesChannelLookup,
			SalesChannelSiteRepository saleschannelsiteRepository, SalesChannelConverter salesChannelConverter) {
		this.salesChannelLookup = salesChannelLookup;
		this.saleschannelsiteRepository = saleschannelsiteRepository;
		this.salesChannelConverter = salesChannelConverter;
	}

}
