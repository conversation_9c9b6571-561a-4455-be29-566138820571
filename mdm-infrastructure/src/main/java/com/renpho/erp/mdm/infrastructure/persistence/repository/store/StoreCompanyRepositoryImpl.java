package com.renpho.erp.mdm.infrastructure.persistence.repository.store;

import com.baomidou.mybatisplus.core.enums.SqlMethod;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.renpho.erp.mdm.domain.company.*;
import com.renpho.erp.mdm.domain.store.StoreCompany;
import com.renpho.erp.mdm.domain.store.StoreCompanyRepository;
import com.renpho.erp.mdm.domain.store.StoreId;
import com.renpho.erp.mdm.infrastructure.persistence.mapper.store.StoreCompanyMapper;
import com.renpho.erp.mdm.infrastructure.persistence.po.store.StoreCompanyPo;
import com.renpho.erp.mdm.infrastructure.persistence.po.store.converter.StoreCompanyConverter;
import lombok.RequiredArgsConstructor;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.ListUtils;
import org.apache.commons.collections4.MapUtils;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2024/9/19
 */
@Repository
@RequiredArgsConstructor
public class StoreCompanyRepositoryImpl extends ServiceImpl<StoreCompanyMapper, StoreCompanyPo>
		implements StoreCompanyRepository {

	private final CompanyLookup companyLookup;

	private final CompanyTaxLookup companyTaxLookup;

	private final StoreCompanyConverter storeCompanyConverter;

	@Override
	@Transactional(rollbackFor = Exception.class)
	public List<StoreCompany> findByStoreId(StoreId storeId) {
		List<StoreCompanyPo> pos = lambdaQuery().eq(StoreCompanyPo::getStoreId, storeId.getId()).list();
		if (CollectionUtils.isEmpty(pos)) {
			return List.of();
		}
		List<CompanyId> companyIds = pos.stream().map(StoreCompanyPo::getCompanyId).distinct().sorted()
				.map(CompanyId::new).toList();
		Map<CompanyId, Company> companyById = ListUtils.emptyIfNull(companyLookup.findByIds(companyIds)).stream()
				.collect(Collectors.toMap(Company::getId, Function.identity()));
		List<StoreCompany> list = new ArrayList<>(pos.size());
		for (StoreCompanyPo po : pos) {
			CompanyId companyId = new CompanyId(po.getCompanyId());
			Company company = companyById.get(companyId);
			StoreCompany domain = storeCompanyConverter.toDomain(po, company);
			Optional.ofNullable(domain.getSelectedTax())
					.map(CompanyTax::getId)
					.flatMap(companyTaxLookup::findById)
					.ifPresent(domain::setSelectedTax);
			list.add(domain);
		}
		return list;
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public Map<StoreId, List<StoreCompany>> findByStoreIds(Collection<StoreId> storeIds) {
		Map<StoreId, List<StoreCompanyPo>> companyGroupByStoreId = CollectionUtils
				.emptyIfNull(lambdaQuery()
						.in(StoreCompanyPo::getStoreId, storeIds.stream().map(StoreId::getId).toList()).list())
				.stream().collect(Collectors.groupingBy(storeCompanyPo -> new StoreId(storeCompanyPo.getStoreId())));
		List<CompanyId> companyIds = companyGroupByStoreId.values().stream().flatMap(Collection::stream)
				.map(StoreCompanyPo::getCompanyId).distinct().sorted().map(CompanyId::new).toList();
		Map<CompanyId, Company> companyById = ListUtils.emptyIfNull(companyLookup.findByIds(companyIds)).stream()
				.collect(Collectors.toMap(Company::getId, Function.identity()));

		Map<StoreId, List<StoreCompany>> map = new HashMap<>(companyGroupByStoreId.size());
		for (Map.Entry<StoreId, List<StoreCompanyPo>> entry : companyGroupByStoreId.entrySet()) {
			List<StoreCompany> storeCompanies = new ArrayList<>();
			for (StoreCompanyPo po : CollectionUtils.emptyIfNull(entry.getValue())) {
				CompanyId companyId = new CompanyId(po.getCompanyId());
				Company company = companyById.get(companyId);
				StoreCompany domain = storeCompanyConverter.toDomain(po, company);
				Optional.ofNullable(domain.getSelectedTax())
						.map(CompanyTax::getId)
						.flatMap(companyTaxLookup::findById)
						.ifPresent(domain::setSelectedTax);
				storeCompanies.add(domain);
			}
			map.put(entry.getKey(), storeCompanies);
		}
		return map;
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public Map<StoreId, List<StoreCompany>> findByCompanyId(CompanyId companyId) {
		Optional<Integer> optional = Optional.ofNullable(companyId).map(CompanyId::getId);
		if (optional.isEmpty()) {
			return Map.of();
		}
		Optional<Company> company = companyLookup.findById(companyId);
		if (company.isEmpty()) {
			return Map.of();
		}
		List<StoreCompanyPo> pos = lambdaQuery().eq(StoreCompanyPo::getCompanyId, optional.get()).list();
		Map<Integer, List<StoreCompanyPo>> map = CollectionUtils.emptyIfNull(pos)
				.stream()
				.collect(Collectors.groupingBy(StoreCompanyPo::getStoreId));
		if (map.isEmpty()) {
			return Map.of();
		}
		Map<StoreId, List<StoreCompany>> results = new HashMap<>(map.size());
		for (Map.Entry<Integer, List<StoreCompanyPo>> entry : map.entrySet()) {
			List<StoreCompanyPo> storeCompanyPos = ListUtils.emptyIfNull(entry.getValue());
			if (CollectionUtils.isEmpty(storeCompanyPos)) {
				continue;
			}
			List<StoreCompany> value = new ArrayList<>(storeCompanyPos.size());
			Map<CompanyTaxId, List<StoreCompany>> groupingByTaxId = new HashMap<>();
			for (StoreCompanyPo p : storeCompanyPos) {
				StoreCompany domain = storeCompanyConverter.toDomain(p, company.get());
				value.add(domain);
				if (Optional.ofNullable(domain.getSelectedTax()).map(CompanyTax::getId).map(CompanyTaxId::id).isPresent()) {
					groupingByTaxId.computeIfAbsent(domain.getSelectedTax().getId(), k -> new ArrayList<>()).add(domain);
				}
			}
			if (MapUtils.isNotEmpty(groupingByTaxId)) {
				List<CompanyTax> taxes = companyTaxLookup.findByIds(groupingByTaxId.keySet());
				if (CollectionUtils.isNotEmpty(taxes)) {
					for (CompanyTax tax : taxes) {
						List<StoreCompany> storeCompanies = groupingByTaxId.get(tax.getId());
						if (CollectionUtils.isNotEmpty(storeCompanies)) {
							storeCompanies.forEach(c -> c.setSelectedTax(tax));
						}
					}
				}
			}
			results.put(new StoreId(entry.getKey()), value);
		}
		return results;
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public void saveBatch(StoreId id, Collection<StoreCompany> companies) {
		lambdaUpdate().eq(StoreCompanyPo::getStoreId, id.getId()).remove();
		List<StoreCompanyPo> pos = companies.stream()
				.map(storeCompany -> storeCompanyConverter.toPo(storeCompany, id))
				.toList();
		String sqlStatement = getSqlStatement(SqlMethod.INSERT_ONE);
		executeBatch(pos, DEFAULT_BATCH_SIZE, (sqlSession, entity) -> sqlSession.insert(sqlStatement, entity));
	}

}
