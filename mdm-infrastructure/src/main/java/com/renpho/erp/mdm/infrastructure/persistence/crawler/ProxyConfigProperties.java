
package com.renpho.erp.mdm.infrastructure.persistence.crawler;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Component;

import java.util.List;

@RefreshScope
@Data
@Component
@ConfigurationProperties(prefix = "renpho.karma")
public class ProxyConfigProperties {

    private ProxyConfig proxy;

    @Data
    public static class ProxyConfig {
        private boolean enabled;
        private List<ProxyInfo> pool;
    }

    @Data
    public static class ProxyInfo {
        private String ip;
        private int port;
    }
}