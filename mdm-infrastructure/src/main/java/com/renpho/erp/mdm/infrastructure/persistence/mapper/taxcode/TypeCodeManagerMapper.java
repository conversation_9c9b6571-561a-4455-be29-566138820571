package com.renpho.erp.mdm.infrastructure.persistence.mapper.taxcode;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.renpho.erp.mdm.domain.tax.TypeCodePageQuery;
import com.renpho.erp.mdm.domain.tax.dto.TaxCodeGroupDto;
import com.renpho.erp.mdm.infrastructure.persistence.po.taxcode.TypeCodeManagerPo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface TypeCodeManagerMapper extends BaseMapper<TypeCodeManagerPo> {
    /**
     * 查询列表数据
     *
     * @param page
     * @param typeCodePageQuery
     * @return
     */
    IPage<TypeCodeManagerPo> selectPage(Page<TypeCodeManagerPo> page, @Param("query") TypeCodePageQuery typeCodePageQuery);

    /**
     * 根据税码统计数量
     *
     * @param idList
     */
    List<TaxCodeGroupDto> findCountByIdList(@Param("idList") List<Integer> idList);
}