package com.renpho.erp.mdm.infrastructure.persistence.repository.company;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.renpho.erp.mdm.domain.company.*;
import com.renpho.erp.mdm.domain.exception.DataNotFoundException;
import com.renpho.erp.mdm.infrastructure.persistence.mapper.company.CompanyMapper;
import com.renpho.erp.mdm.infrastructure.persistence.po.company.CompanyPo;
import com.renpho.erp.mdm.infrastructure.persistence.po.company.converter.CompanyConverter;
import com.renpho.erp.security.util.SecurityUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Optional;

/**
 * 公司信息 Repository
 *
 * <AUTHOR>
 * @since 2024/9/20
 */
@Service
public class CompanyRepositoryImpl extends ServiceImpl<CompanyMapper, CompanyPo> implements CompanyRepository {

	private final CompanyLookup companyLookup;
	private final CompanyTaxLookup companyTaxLookup;
	private final CompanyTaxRepository companyTaxRepository;
	private final CompanyConverter companyConverter;

	@Override
	@Transactional(rollbackFor = Exception.class)
	public Company add(Company company) {
		company.assertNamePattern();
		company.assertNameNotDuplicate(companyLookup);
		company.assertParentExist(companyLookup);
		company.assertTax(companyTaxLookup);
		CompanyPo po = companyConverter.toPo(company).formatCompanyName();
		save(po);
		Company domain = companyConverter.toDomain(po.parseCompanyName());
		companyTaxRepository.batchSaveOrUpdate(domain, company.getTaxes());
		return domain;
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public Company update(Company company) {
		assertExist(company);
		company.assertNamePattern();
		company.assertNameNotDuplicate(companyLookup);
		company.assertParentExist(companyLookup);
		company.assertTax(companyTaxLookup);
		CompanyPo po = companyConverter.toPo(company).formatCompanyName();
		updateById(po);
		Company domain = companyConverter.toDomain(po.parseCompanyName());
		companyTaxRepository.batchSaveOrUpdate(domain, company.getTaxes());
		return domain;
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public Company switchStatus(Company company) {
		assertExist(company);
		Integer id = company.getId().getId();
		lambdaUpdate().eq(CompanyPo::getId, id)
				.set(CompanyPo::getStatus, company.getStatus().getStatus()).update();
		return companyLookup.findById(company.getId()).orElseThrow(DataNotFoundException::new);
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public Company updateNsId(Company company) {
		assertExist(company);
		Optional<Integer> updated = Optional.ofNullable(SecurityUtils.getUserId());
		lambdaUpdate()
				.set(CompanyPo::getNsSyncStatus, company.getNsSyncStatus().getValue())
				.set(company.getNsId() != null, CompanyPo::getNsId, company.getNsId())
				.set(updated.isPresent(), CompanyPo::getUpdateBy, updated.orElse(null))
				.eq(CompanyPo::getId, company.getId().getId())
				.update();
		return companyLookup.findById(company.getId()).orElse(new Company(new CompanyId(null)));
	}

	private void assertExist(Company company) {
		if (getById(company.getId().getId()) == null) {
			throw new DataNotFoundException();
		}
	}

	public CompanyRepositoryImpl(CompanyConverter companyConverter,
								 CompanyLookup companyLookup,
								 CompanyTaxLookup companyTaxLookup,
								 CompanyTaxRepository companyTaxRepository) {
		this.companyConverter = companyConverter;
		this.companyLookup = companyLookup;
		this.companyTaxLookup = companyTaxLookup;
		this.companyTaxRepository = companyTaxRepository;
	}

}
