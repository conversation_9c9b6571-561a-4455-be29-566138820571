package com.renpho.erp.mdm.infrastructure.persistence.repository.nsapidefinition;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.renpho.erp.mdm.domain.netsuite.NsApiDefinition;
import com.renpho.erp.mdm.domain.netsuite.NsApiDefinitionId;
import com.renpho.erp.mdm.domain.netsuite.NsApiDefinitionLookup;
import com.renpho.erp.mdm.infrastructure.persistence.mapper.nsapidefinition.NsApiDefinitionMapper;
import com.renpho.erp.mdm.infrastructure.persistence.po.nsapidefinition.NsApiDefinitionPo;
import com.renpho.erp.mdm.infrastructure.persistence.po.nsapidefinition.converter.NsApiDefinitionConverter;
import org.springframework.stereotype.Repository;

import java.util.Optional;

/**
 * NS接口配置 Repository.
 *
 * <AUTHOR>
 * @since 2024/9/29
 */
@Repository
public class NsApiDefinitionLookupImpl extends ServiceImpl<NsApiDefinitionMapper, NsApiDefinitionPo>
		implements NsApiDefinitionLookup {

	private final NsApiDefinitionConverter nsApiDefinitionConverter;

	@Override
	public Optional<NsApiDefinition> findById(NsApiDefinitionId nsApiDefinitionId) {
		return getOptById(nsApiDefinitionId.name()).map(nsApiDefinitionConverter::toDomain);
	}

	public NsApiDefinitionLookupImpl(NsApiDefinitionConverter nsApiDefinitionConverter) {
		this.nsApiDefinitionConverter = nsApiDefinitionConverter;
	}

}
