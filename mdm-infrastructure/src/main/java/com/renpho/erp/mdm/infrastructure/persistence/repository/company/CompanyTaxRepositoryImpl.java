package com.renpho.erp.mdm.infrastructure.persistence.repository.company;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.renpho.erp.mdm.domain.company.*;
import com.renpho.erp.mdm.infrastructure.persistence.mapper.company.CompanyTaxMapper;
import com.renpho.erp.mdm.infrastructure.persistence.po.company.CompanyTaxPo;
import com.renpho.erp.mdm.infrastructure.persistence.po.company.converter.CompanyTaxConverter;
import lombok.RequiredArgsConstructor;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2024/12/10
 */
@Repository
@RequiredArgsConstructor
public class CompanyTaxRepositoryImpl extends ServiceImpl<CompanyTaxMapper, CompanyTaxPo> implements CompanyTaxRepository {

    private final CompanyTaxConverter companyTaxConverter;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void batchSaveOrUpdate(Company company, Collection<CompanyTax> taxes) {
        Integer cid = Optional.ofNullable(company).map(Company::getId).map(CompanyId::getId).orElse(null);
        if (cid == null) {
            return;
        }
        if (CollectionUtils.isEmpty(taxes)) {
            lambdaUpdate().eq(CompanyTaxPo::getCompanyId, cid).remove();
            return;
        }
        Map<Boolean, List<CompanyTax>> map = CollectionUtils.emptyIfNull(taxes)
                .stream()
                .filter(Objects::nonNull)
                .collect(Collectors.groupingBy(t -> Optional.ofNullable(t.getId()).map(CompanyTaxId::id).isPresent()));
        if (MapUtils.isEmpty(map)) {
            return;
        }
        List<CompanyTax> domains = new LinkedList<>();
        for (Map.Entry<Boolean, List<CompanyTax>> entry : map.entrySet()) {
            List<CompanyTaxPo> pos = companyTaxConverter.toPos(entry.getValue());
            pos.forEach(po -> po.setCompanyId(cid));
            if (entry.getKey()) {
                updateBatchById(pos);
            } else {
                saveBatch(pos);
            }
            domains.addAll(companyTaxConverter.toDomains(pos));
        }
        company.setTaxes(domains);
    }
}
