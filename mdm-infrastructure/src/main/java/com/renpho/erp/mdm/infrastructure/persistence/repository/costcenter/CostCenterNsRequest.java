package com.renpho.erp.mdm.infrastructure.persistence.repository.costcenter;

import com.renpho.erp.mdm.infrastructure.persistence.repository.rate.NsRequestBase;

import lombok.Data;
import lombok.EqualsAndHashCode;

@EqualsAndHashCode(callSuper = true)
@Data
public class CostCenterNsRequest extends NsRequestBase {

	private String name; // 名称
	private String code; // 代码
	private String nameRoute; // 11/11
	private String codeRoute; // 11/22
	private Integer id; // ID
}
