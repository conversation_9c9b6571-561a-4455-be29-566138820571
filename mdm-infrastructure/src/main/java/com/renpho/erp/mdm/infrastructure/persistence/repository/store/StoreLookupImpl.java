package com.renpho.erp.mdm.infrastructure.persistence.repository.store;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.renpho.erp.mdm.domain.common.ActiveStatus;
import com.renpho.erp.mdm.domain.company.Company;
import com.renpho.erp.mdm.domain.company.CompanyId;
import com.renpho.erp.mdm.domain.saleschannel.*;
import com.renpho.erp.mdm.domain.store.*;
import com.renpho.erp.mdm.infrastructure.feignclient.oum.OumUserInfoConverter;
import com.renpho.erp.mdm.infrastructure.feignclient.oum.RemoteUserDetailsFeign;
import com.renpho.erp.mdm.infrastructure.feignclient.pds.RemoteCountryRegionFeign;
import com.renpho.erp.mdm.infrastructure.persistence.mapper.store.StoreMapper;
import com.renpho.erp.mdm.infrastructure.persistence.po.saleschannel.converter.SalesChannelSiteConverter;
import com.renpho.erp.mdm.infrastructure.persistence.po.store.StorePo;
import com.renpho.erp.mdm.infrastructure.persistence.po.store.converter.StoreConverter;
import com.renpho.erp.pds.client.vo.PdsCountryRegionVo;
import com.renpho.erp.smc.client.dto.OumUserInfoRes;
import com.renpho.karma.dto.PageQuery;
import com.renpho.karma.dto.Paging;
import lombok.RequiredArgsConstructor;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2024/9/24
 */
@Repository
@RequiredArgsConstructor
public class StoreLookupImpl extends ServiceImpl<StoreMapper, StorePo> implements StoreLookup {

	private final StoreCompanyRepository storeCompanyRepository;

	private final StorePaymentRepository storePaymentRepository;

	private final SalesChannelLookup salesChannelLookup;

	private final RemoteUserDetailsFeign remoteUserDetailsFeign;

	private final RemoteCountryRegionFeign remoteCountryRegionFeign;

	private final StoreConverter storeConverter;

	private final OumUserInfoConverter oumUserInfoConverter;

	private final SalesChannelSiteConverter salesChannelSiteConverter;

	private final StoreMapper storeMapper;

	@Override
	@Transactional(rollbackFor = Exception.class)
	public Paging<Store> findPage(Store root, PageQuery pageQuery) {
        Optional<Store> optional = Optional.ofNullable(root);
		Optional<CompanyId> companyId = optional.map(Store::getActiveCompany).map(Company::getId).filter(cid -> Objects.nonNull(cid.getId()));
        Set<Integer> ids = new TreeSet<>();
        if (companyId.isPresent()) {
            Map<StoreId, List<StoreCompany>> storeCompanies = storeCompanyRepository.findByCompanyId(companyId.get());
            if (MapUtils.isEmpty(storeCompanies)) {
                return Paging.of(pageQuery.getPageSize(), pageQuery.getPageIndex());
            }
            storeCompanies.keySet().stream().map(StoreId::getId).forEach(ids::add);
        }
        Optional<Integer> salesChannelId = optional.map(Store::getSalesChannel).map(SalesChannel::getId).map(SalesChannelId::getId);
        Optional<String> storeName = optional.map(Store::getStoreName).filter(StringUtils::isNotBlank);
        Optional<Integer> siteId = optional.map(Store::getSite).map(SalesChannelSite::getId).map(SiteId::getId);
        Optional<Integer> status = optional.map(Store::getStatus).map(ActiveStatus::getStatus);

		LambdaQueryWrapper<StorePo> qw = new LambdaQueryWrapper<StorePo>()
				.in(CollectionUtils.isNotEmpty(ids), StorePo::getId, ids)
				.eq(siteId.isPresent(), StorePo::getSiteId, siteId.orElse(null))
				.eq(status.isPresent(), StorePo::getStatus, status.orElse(null))
				.like(storeName.isPresent(), StorePo::getStoreName, storeName.orElse(null))
				.eq(salesChannelId.isPresent(), StorePo::getSalesChannelId, salesChannelId.orElse(null))
				.orderByDesc(StorePo::getUpdateTime);
		// 分页查询许可的店铺（数据权限）
		Page<StorePo> page = storeMapper.selectPermissionPage(Page.of(pageQuery.getPageIndex(), pageQuery.getPageSize()), qw);

		Paging<Store> paging = Paging.of((int) page.getSize(), (int) page.getCurrent());
		List<StorePo> records = page.getRecords();

		List<Store> stores = associations(records);
		if (CollectionUtils.isEmpty(stores)) {
			paging.setRecords(List.of());
			return paging;
		}
		paging.setRecords(stores);
		paging.setTotalCount((int) page.getTotal());
		return paging;
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public List<Store> findByIds(Collection<StoreId> ids) {
		if (CollectionUtils.isEmpty(ids)) {
			return List.of();
		}
		List<StorePo> pos = lambdaQuery().in(StorePo::getId, ids.stream().map(StoreId::getId).toList()).list();
		return associations(pos);
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public List<Store> findByNames(Collection<String> names) {
		if (CollectionUtils.isEmpty(names)) {
			return List.of();
		}
		List<StorePo> pos = lambdaQuery().in(StorePo::getStoreName, names).list();
		return associations(pos);
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public long countName(StoreId id, String name) {
		Optional<Integer> byId = Optional.ofNullable(id).map(StoreId::getId);
		return lambdaQuery()
				.ne(byId.isPresent(), StorePo::getId, byId.orElse(null))
				.eq(StorePo::getStoreName, name)
				.count();
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public List<Store> findAll(Store root) {
		Optional<Store> hasCondition = Optional.ofNullable(root);
		Optional<CompanyId> companyId = hasCondition.map(Store::getActiveCompany)
				.map(Company::getId)
				.filter(cid -> Objects.nonNull(cid.getId()));
		Set<Integer> ids = new TreeSet<>();
		if (companyId.isPresent()) {
			Map<StoreId, List<StoreCompany>> storeCompanies = storeCompanyRepository.findByCompanyId(companyId.get());
			if (MapUtils.isEmpty(storeCompanies)) {
				return List.of();
			}
			storeCompanies.keySet().stream().map(StoreId::getId).forEach(ids::add);
		}
		Optional<Integer> bySiteId = hasCondition.map(Store::getSite).map(SalesChannelSite::getId).map(SiteId::getId);

		Optional<String> byName = hasCondition.map(Store::getStoreName).filter(StringUtils::isNotBlank);
		Optional<Integer> byChannel = hasCondition.map(Store::getSalesChannel).map(SalesChannel::getId).map(SalesChannelId::getId);
		List<StorePo> pos = lambdaQuery()
				.in(CollectionUtils.isNotEmpty(ids), StorePo::getId, ids)
				.eq(bySiteId.isPresent(), StorePo::getSiteId, bySiteId.orElse(null))
				.like(byName.isPresent(), StorePo::getStoreName, byName.orElse(null))
				.eq(byChannel.isPresent(), StorePo::getSalesChannelId, byChannel.orElse(null))
				.list();
		return associations(pos);
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public List<Store> findByAmazonSellerIdAndSiteCode(Store condition) {
		List<PdsCountryRegionVo> sites = remoteCountryRegionFeign.findByCodes(List.of(condition.getSite().getSiteCode()));
		Set<Integer> siteIds = CollectionUtils.emptyIfNull(sites)
				.stream()
				.map(PdsCountryRegionVo::getId)
				.filter(Objects::nonNull)
				.collect(Collectors.toSet());
		if (CollectionUtils.isEmpty(siteIds)) {
			return List.of();
		}
		List<StorePo> pos = lambdaQuery()
				.in(StorePo::getSiteId, siteIds)
				.apply("JSON_SEARCH(developer_info -> '$.amzSellerId', 'ONE', {0}) IS NOT NULL", condition.getDeveloperInfo().getAmzSellerId())
				.list();
		return associations(pos);
	}

	@Override
	public List<Store> findAll(StoreQuery query) {
		Optional<StoreQuery> optional = Optional.ofNullable(query);
		if (optional.filter(StoreQuery::hasSalesChannelQuery).isPresent()) {
			List<SalesChannel> salesChannels = salesChannelLookup.findAll(query.getSalesChannelQuery());
			if (CollectionUtils.isEmpty(salesChannels)) {
				return List.of();
			}
			query.setChannelIds(salesChannels.stream().map(SalesChannel::getId).toList());
		}
		Optional<String> hasSiteCode = optional.map(StoreQuery::getSiteCode).filter(StringUtils::isNotBlank);
		if (hasSiteCode.isPresent()) {
			List<PdsCountryRegionVo> result = remoteCountryRegionFeign.findByCodes(List.of(hasSiteCode.get()));
			if (CollectionUtils.isEmpty(result)) {
				return List.of();
			}
			query.setSiteIds(CollectionUtils.intersection(result.stream().map(PdsCountryRegionVo::getId).map(SiteId::new).toList(), CollectionUtils.emptyIfNull(query.getSiteIds())));
		}
		Set<String> hasSiteCodes = optional.map(StoreQuery::getSiteCodes).stream().flatMap(Collection::stream).filter(StringUtils::isNotBlank).collect(Collectors.toSet());
		if (CollectionUtils.isNotEmpty(hasSiteCodes)) {
			List<PdsCountryRegionVo> result = remoteCountryRegionFeign.findByCodes(hasSiteCodes);
			if (CollectionUtils.isEmpty(result)) {
				return List.of();
			}
			if (optional.isPresent()) {
				query.setSiteIds(CollectionUtils.intersection(result.stream().map(PdsCountryRegionVo::getId).map(SiteId::new).toList(), CollectionUtils.emptyIfNull(query.getSiteIds())));
			}
		}
		Optional<Integer> hasStoreId = optional.map(StoreQuery::getStoreId).map(StoreId::getId);
		Set<Integer> hasStoreIds = optional.map(StoreQuery::getStoreIds).stream().flatMap(Collection::stream).filter(Objects::nonNull).map(StoreId::getId).filter(Objects::nonNull).collect(Collectors.toSet());
		Set<Integer> hasChannelIds = optional.map(StoreQuery::getChannelIds).stream().flatMap(Collection::stream).filter(Objects::nonNull).map(SalesChannelId::getId).filter(Objects::nonNull).collect(Collectors.toSet());
		Optional<Integer> hasSiteId = optional.map(StoreQuery::getSiteId).map(SiteId::getId);
		Set<Integer> hasSiteIds = optional.map(StoreQuery::getSiteIds).stream().flatMap(Collection::stream).filter(Objects::nonNull).map(SiteId::getId).filter(Objects::nonNull).collect(Collectors.toSet());
		Optional<Integer> hasStoreStatus = optional.map(StoreQuery::getStoreStatus).map(ActiveStatus::getStatus);

		List<StorePo> pos = lambdaQuery().eq(hasStoreId.isPresent(), StorePo::getId, hasStoreId.orElse(null))
				.eq(hasSiteId.isPresent(), StorePo::getSiteId, hasSiteId.orElse(null))
				.eq(hasStoreStatus.isPresent(), StorePo::getStatus, hasStoreStatus.orElse(null))
				.in(CollectionUtils.isNotEmpty(hasStoreIds), StorePo::getId, hasStoreIds)
				.in(CollectionUtils.isNotEmpty(hasChannelIds), StorePo::getSalesChannelId, hasChannelIds)
				.in(CollectionUtils.isNotEmpty(hasSiteIds), StorePo::getSiteId, hasSiteIds)
				.list();

		return associations(pos);
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public Optional<Store> findById(StoreId id) {
		return getOptById(id.getId())
				.map(this::toDomain)
				.stream()
				.peek(d -> salesChannelLookup.findById(d.getSalesChannel().getId()).ifPresent(d::setSalesChannel))
				.peek(d -> d.setCompanies(storeCompanyRepository.findByStoreId((d.getId()))))
				.peek(d -> d.setPayments(storePaymentRepository.findByStoreId(d.getId())))
				.peek(Store::findActiveCompany)
				.peek(d -> Optional.ofNullable(d.getSite())
						.map(SalesChannelSite::getId)
						.map(SiteId::getId)
						.flatMap(remoteCountryRegionFeign::findById)
						.map(salesChannelSiteConverter::toDomain)
						.ifPresent(d::setSite))
				.findFirst();
	}

	private Store toDomain(StorePo s) {
		Store domain = storeConverter.toDomain(s);
		Map<Integer, OumUserInfoRes> userMap = remoteUserDetailsFeign.findByUserIds(Set.of(s.getStoreManagerId()));
		// 店铺负责人信息
		Optional.ofNullable(s.getStoreManagerId()).ifPresent(managerId -> {
			OumUserInfoRes storeManager = userMap.get(managerId);
			Optional.ofNullable(storeManager).ifPresent(m -> {
				domain.setStoreManagerName(m.getName());
				domain.setStoreManagerCode(m.getCode());
				domain.setStoreManagerStatus(m.getStatus());
			});
		});
		return domain;
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public Map<SalesChannelId, List<Store>> findBySalesChannelIds(Collection<SalesChannelId> ids) {
		if (CollectionUtils.isEmpty(ids)) {
			return Map.of();
		}
		List<StorePo> pos = lambdaQuery()
				.in(StorePo::getSalesChannelId, ids.stream().map(SalesChannelId::getId).toList())
				.list();

		List<Store> stores = associations(pos);
		if (CollectionUtils.isEmpty(stores)) {
			return Map.of();
		}
		return stores.stream().collect(Collectors.groupingBy(store -> store.getSalesChannel().getId()));
	}

	private List<Store> associations(Collection<StorePo> pos) {
		if (CollectionUtils.isEmpty(pos)) {
			return List.of();
		}

		List<Store> stores = new ArrayList<>(pos.size());
		List<StoreId> storeIds = new ArrayList<>(pos.size());
		Set<SalesChannelId> salesChannelIds = new TreeSet<>();
		Set<Integer> userIds = new TreeSet<>();
		for (StorePo record : pos) {
			Store domain = storeConverter.toDomain(record);
			stores.add(domain);
			storeIds.add(domain.getId());
			salesChannelIds.add(new SalesChannelId(record.getSalesChannelId()));
			userIds.add(record.getCreateBy());
			userIds.add(record.getUpdateBy());
			// 店铺负责人
			userIds.add(record.getStoreManagerId());
		}

		Map<StoreId, List<StoreCompany>> companies = storeCompanyRepository.findByStoreIds(storeIds);
		Map<StoreId, List<StorePayment>> payments = storePaymentRepository.findByStoreIds(storeIds);
		Map<Integer, OumUserInfoRes> users = remoteUserDetailsFeign.findByUserIds(userIds);
		Map<SalesChannelId, SalesChannel> salesChannels = salesChannelLookup.findByIds(salesChannelIds);
		for (Store store : stores) {
			store.setCompanies(companies.get(store.getId()));
			store.findActiveCompany();
			store.setPayments(payments.get(store.getId()));
			SalesChannel salesChannel = salesChannels.get(store.getSalesChannel().getId());
			store.setSalesChannel(salesChannel);
			Optional.ofNullable(salesChannel)
					.flatMap(channel -> channel.findInAvailableSites(store.getSite()))
					.ifPresent(store::setSite);
			oumUserInfoConverter.fillCreated(users.get(store.getCreated().getCreateBy()), store.getCreated());
			oumUserInfoConverter.fillUpdated(users.get(store.getUpdated().getUpdateBy()), store.getUpdated());
			// 店铺负责人信息
			Optional<OumUserInfoRes> u = Optional.ofNullable(store.getStoreManagerId()).map(users::get);
			if (u.isPresent()) {
				store.setStoreManagerName(u.get().getName());
				store.setStoreManagerCode(u.get().getCode());
				store.setStoreManagerStatus(u.get().getStatus());
			}
		}
		return stores;
	}

}
