package com.renpho.erp.mdm.infrastructure.persistence.repository.tax;


import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.renpho.erp.mdm.domain.tax.TaxTypeLookup;
import com.renpho.erp.mdm.domain.tax.TaxTypeManagerId;
import com.renpho.erp.mdm.domain.tax.TypeManager;
import com.renpho.erp.mdm.infrastructure.persistence.mapper.taxcode.TypeManagerMapper;
import com.renpho.erp.mdm.infrastructure.persistence.po.taxcode.TypeManagerPo;
import com.renpho.erp.mdm.infrastructure.persistence.po.taxcode.converter.TypeManagerConverter;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

/**
 * @author: Wyatt
 * @description: 查询税种
 * @date: 2024/11/20 15:33
 */
@Repository
public class TaxTypeLookupImpl extends ServiceImpl<TypeManagerMapper, TypeManagerPo> implements TaxTypeLookup {


    private final TypeManagerConverter typeManagerConverter;

    public TaxTypeLookupImpl(TypeManagerConverter typeManagerConverter) {
        this.typeManagerConverter = typeManagerConverter;
    }

    @Override
    public long countName(TaxTypeManagerId id, String name) {
        Optional<Integer> byId = Optional.ofNullable(id).map(TaxTypeManagerId::getId);
        return this.lambdaQuery()
                .ne(byId.isPresent(), TypeManagerPo::getId, byId.orElse(null))
                .eq(TypeManagerPo::getName, name)
                .eq(TypeManagerPo::getIsDeleted, NumberUtils.INTEGER_ZERO)
                .count();
    }


    @Override
    public List<TypeManager> findByIds(List<Integer> idList) {
        return this.lambdaQuery()
                .in(TypeManagerPo::getId, idList)
                .list()
                .stream().map(typeManagerConverter::toDomain).toList();
    }

    @Override
    public Optional<TypeManager> findById(TaxTypeManagerId taxTypeManagerId) {
        return getOptById(taxTypeManagerId.getId()).map(typeManagerConverter::toDomain);
    }
}
