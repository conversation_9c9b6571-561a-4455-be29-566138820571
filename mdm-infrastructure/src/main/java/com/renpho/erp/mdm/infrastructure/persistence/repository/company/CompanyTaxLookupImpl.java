package com.renpho.erp.mdm.infrastructure.persistence.repository.company;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.renpho.erp.mdm.domain.company.*;
import com.renpho.erp.mdm.infrastructure.persistence.mapper.company.CompanyTaxMapper;
import com.renpho.erp.mdm.infrastructure.persistence.po.company.CompanyTaxPo;
import com.renpho.erp.mdm.infrastructure.persistence.po.company.converter.CompanyTaxConverter;
import lombok.RequiredArgsConstructor;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2024/12/10
 */
@Repository
@RequiredArgsConstructor
public class CompanyTaxLookupImpl extends ServiceImpl<CompanyTaxMapper, CompanyTaxPo> implements CompanyTaxLookup {

    private final CompanyTaxConverter companyTaxConverter;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Optional<CompanyTax> findById(CompanyTaxId companyTaxId) {
        return Optional.ofNullable(companyTaxId)
                .map(CompanyTaxId::id)
                .flatMap(this::getOptById)
                .map(companyTaxConverter::toDomain);
    }

    @Override
    public List<CompanyTax> findByIds(Collection<CompanyTaxId> companyTaxIds) {
        Set<Integer> ids = CollectionUtils.emptyIfNull(companyTaxIds)
                .stream()
                .filter(Objects::nonNull)
                .map(CompanyTaxId::id)
                .filter(Objects::nonNull)
                .collect(Collectors.toCollection(TreeSet::new));
        if (CollectionUtils.isEmpty(ids)) {
            return List.of();
        }
        List<CompanyTaxPo> pos = listByIds(ids);
        return companyTaxConverter.toDomains(pos);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public List<Company> findByCompanyIds(Collection<CompanyId> companyIds) {
        Set<Integer> ids = CollectionUtils.emptyIfNull(companyIds)
                .stream()
                .filter(Objects::nonNull)
                .map(CompanyId::getId)
                .filter(Objects::nonNull)
                .collect(Collectors.toCollection(TreeSet::new));
        if (CollectionUtils.isEmpty(ids)) {
            return List.of();
        }
        List<CompanyTaxPo> pos = lambdaQuery().in(CompanyTaxPo::getCompanyId, ids).list();
        if (CollectionUtils.isEmpty(pos)) {
            return List.of();
        }
        Map<Integer, List<CompanyTaxPo>> map = pos.stream().collect(Collectors.groupingBy(CompanyTaxPo::getCompanyId));
        List<Company> results = new LinkedList<>();
        for (Map.Entry<Integer, List<CompanyTaxPo>> entry : map.entrySet()) {
            CompanyId companyId = new CompanyId(entry.getKey());
            Company company = new Company(companyId);
            company.setTaxes(companyTaxConverter.toDomains(entry.getValue()));
            results.add(company);
        }
        return results;
    }
}
