package com.renpho.erp.mdm.infrastructure.persistence.repository.costcenter;

import org.springframework.context.annotation.Configuration;

import com.renpho.erp.mdm.domain.costprice.CostCenterRepository;
import com.xxl.job.core.handler.annotation.XxlJob;

import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Configuration
public class CostCenterJob {
	@Resource
	private CostCenterRepository costCenterRepository;

	@XxlJob("CostCenterPushNsJob")
	public void job() {
		costCenterRepository.syncNsJob();
	}

}
