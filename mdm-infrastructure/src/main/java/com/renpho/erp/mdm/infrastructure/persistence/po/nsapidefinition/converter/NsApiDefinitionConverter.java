package com.renpho.erp.mdm.infrastructure.persistence.po.nsapidefinition.converter;

import cn.hutool.core.util.StrUtil;
import com.renpho.erp.mdm.domain.netsuite.NsApiDefinition;
import com.renpho.erp.mdm.domain.netsuite.NsApiDefinitionId;
import com.renpho.erp.mdm.infrastructure.persistence.po.nsapidefinition.NsApiDefinitionPo;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingConstants;
import org.mapstruct.ReportingPolicy;

import java.util.Optional;

@Mapper(unmappedTargetPolicy = ReportingPolicy.IGNORE, componentModel = MappingConstants.ComponentModel.SPRING)
public interface NsApiDefinitionConverter {

	@Mapping(target = "id", expression = "java(map(po))")
	NsApiDefinition toDomain(NsApiDefinitionPo po);

	@Mapping(target = "id", expression = "java(map(domain))")
	NsApiDefinitionPo toPo(NsApiDefinition domain);

	default String map(NsApiDefinition domain) {
		return Optional.ofNullable(domain).map(NsApiDefinition::getId).map(NsApiDefinitionId::name).orElse(null);
	}

	default NsApiDefinitionId map(NsApiDefinitionPo po) {
		return Optional.ofNullable(po).map(NsApiDefinitionPo::getId).filter(StrUtil::isNotBlank).map(NsApiDefinitionId::valueOf).orElse(null);
	}
}