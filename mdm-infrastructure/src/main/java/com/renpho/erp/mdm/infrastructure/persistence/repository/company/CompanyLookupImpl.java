package com.renpho.erp.mdm.infrastructure.persistence.repository.company;

import cn.hutool.core.util.BooleanUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.core.util.BooleanUtil;
import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.renpho.erp.mdm.domain.common.ActiveStatus;
import com.renpho.erp.mdm.domain.common.LanguageName;
import com.renpho.erp.mdm.domain.company.Company;
import com.renpho.erp.mdm.domain.company.CompanyId;
import com.renpho.erp.mdm.domain.company.CompanyLookup;
import com.renpho.erp.mdm.domain.company.CompanyTaxLookup;
import com.renpho.erp.mdm.domain.currency.CurrencyRepository;
import com.renpho.erp.mdm.domain.netsuite.NsSyncStatus;
import com.renpho.erp.mdm.infrastructure.feignclient.oum.OumUserInfoConverter;
import com.renpho.erp.mdm.infrastructure.feignclient.oum.RemoteUserDetailsFeign;
import com.renpho.erp.mdm.infrastructure.persistence.mapper.company.CompanyMapper;
import com.renpho.erp.mdm.infrastructure.persistence.po.company.CompanyPo;
import com.renpho.erp.mdm.infrastructure.persistence.po.company.converter.CompanyConverter;
import com.renpho.erp.smc.client.dto.OumUserInfoRes;
import lombok.RequiredArgsConstructor;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.ListUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Repository;

import java.util.*;

/**
 * <AUTHOR>
 * @since 2024/9/20
 */
@Repository
@RequiredArgsConstructor
public class CompanyLookupImpl extends ServiceImpl<CompanyMapper, CompanyPo> implements CompanyLookup {

	private final CompanyTaxLookup companyTaxLookup;

	private final CurrencyRepository currencyRepository;

	private final RemoteUserDetailsFeign remoteUserDetailsFeign;

	private final CompanyConverter companyConverter;

	private final OumUserInfoConverter oumUserInfoConverter;

	@Override
	public List<Company> findAll(Company company, Collection<Integer> excludeIds) {
		List<CompanyPo> pos = findAllCondition(company, excludeIds).list();

		if (CollectionUtils.isEmpty(pos)) {
			return List.of();
		}
		return associations(pos);
	}

	@Override
	public List<Company> findAllWithoutAssociations(Company company, Collection<Integer> excludeIds) {
		List<CompanyPo> pos = findAllCondition(company, excludeIds).list();
		return ListUtils.emptyIfNull(pos).stream().map(companyConverter::toDomain).toList();
	}

	private LambdaQueryChainWrapper<CompanyPo> findAllCondition(Company company, Collection<Integer> excludeIds) {
		Optional<Company> optional = Optional.ofNullable(company);
		Optional<String> name = optional.map(Company::getCompanyName)
				.map(CollectionUtils::emptyIfNull)
				.stream()
				.flatMap(Collection::stream)
				.map(LanguageName::getName)
				.filter(StringUtils::isNotBlank)
				.map("%%%s%%"::formatted)
				.findFirst();
		Optional<Integer> hasNsSyncStatus = optional.map(Company::getNsSyncStatus).map(NsSyncStatus::getValue);
		excludeIds = CollectionUtils.emptyIfNull(excludeIds);
		Optional<Integer> hasStatus = optional.map(Company::getStatus).map(ActiveStatus::getStatus);
        return lambdaQuery()
				.notIn(CollectionUtils.isNotEmpty(excludeIds), CompanyPo::getId, excludeIds)
				.eq(hasStatus.isPresent(), CompanyPo::getStatus, hasStatus.orElse(null))
				.eq(hasNsSyncStatus.isPresent(), CompanyPo::getNsSyncStatus, hasNsSyncStatus.orElse(null))
                .apply(name.isPresent(), "JSON_SEARCH(company_name, 'ONE', {0}) IS NOT NULL", name.orElse(null))
				;
	}

	@Override
	public List<Company> findByIds(Collection<CompanyId> id) {
		if (CollectionUtils.isEmpty(id)) {
			return List.of();
		}
		List<Integer> ids = id.stream().map(CompanyId::getId).toList();
		List<CompanyPo> pos = ListUtils.emptyIfNull(listByIds(ids));
		return associations(pos);
	}

	@Override
	public List<Company> findByNames(Collection<Company> companies) {
		List<Integer> ids = new ArrayList<>(companies.size());
		Set<String> names = new HashSet<>();
		for (Company company : CollectionUtils.emptyIfNull(companies)) {
			Optional.ofNullable(company.getId())
					.map(CompanyId::getId)
					.ifPresent(ids::add);
			CollectionUtils.emptyIfNull(company.getCompanyName())
					.stream()
					.map(LanguageName::getName)
					.filter(StringUtils::isNotBlank)
					.forEach(names::add);
		}
		List<CompanyPo> pos = lambdaQuery()
				.in(CollectionUtils.isNotEmpty(ids), CompanyPo::getId, ids)
				.and(CollectionUtils.isNotEmpty(names), w -> names.forEach(n -> w.or(wr -> wr.apply("JSON_SEARCH(company_name, 'ONE', {0}) IS NOT NULL", n))))
				.list();
		return associations(pos);
	}

	@Override
	public List<Company> findByShortNames(Collection<Company> companies) {
		List<Integer> ids = new ArrayList<>(companies.size());
		Set<String> shortNames = new HashSet<>();
		for (Company company : CollectionUtils.emptyIfNull(companies)) {
			Optional.ofNullable(company.getId())
					.map(CompanyId::getId)
					.ifPresent(ids::add);
			Optional.ofNullable(company.getShortName()).ifPresent(shortNames::add);
		}
		List<CompanyPo> pos = lambdaQuery()
				.in(CollectionUtils.isNotEmpty(ids), CompanyPo::getId, ids)
				.in(CollectionUtils.isNotEmpty(shortNames), CompanyPo::getShortName, shortNames)
				.list();
		return associations(pos);
	}

	@Override
	public List<Company> query(Long id, String name, Integer status, Boolean hasB2bAccount) {
        List<CompanyPo> companyPoList = lambdaQuery()
                .eq(id != null, CompanyPo::getId, id)
				.apply(StrUtil.isNotBlank(name), "company_name->'$[*].name' like '%%%s%%'".formatted(name) )
				.eq(status != null, CompanyPo::getStatus, status)
                .isNotNull(BooleanUtil.isTrue(hasB2bAccount), CompanyPo::getB2BFmsFundAccountId)
                .isNull(BooleanUtil.isFalse(hasB2bAccount), CompanyPo::getB2BFmsFundAccountId)
			.list();
        return associations(companyPoList);
	}

	@Override
	public long countNames(CompanyId id, String name) {
		Optional<Integer> companyId = Optional.ofNullable(id).map(CompanyId::getId);
		return lambdaQuery()
				.ne(companyId.isPresent(), CompanyPo::getId, companyId.orElse(null))
				.apply("JSON_CONTAINS(company_name -> '$[*].name', {0}) > 0", "\"%s\"".formatted(name))
				.count();
	}

	@Override
	public long countShortNames(CompanyId id, String shortName) {
		Optional<Integer> companyId = Optional.ofNullable(id).map(CompanyId::getId);
		return lambdaQuery()
				.ne(companyId.isPresent(), CompanyPo::getId, companyId.orElse(null))
				.eq(CompanyPo::getShortName, shortName)
				.count();
	}

	private List<Company> associations(List<CompanyPo> pos) {
		List<Company> companies = new ArrayList<>(pos.size());
		Set<Integer> userIds = new HashSet<>();
		Map<CompanyId, Company> companyIds = new HashMap<>();
		for (CompanyPo po : pos) {
			Company domain = companyConverter.toDomain(po.parseCompanyName());
			companies.add(domain);
			// TODO 批量查找 id
			currencyRepository.findById(domain.getCurrency().getId()).ifPresent(domain::setCurrency);
			currencyRepository.findById(domain.getRegisteredCountryCurrency().getId()).ifPresent(domain::setRegisteredCountryCurrency);
			userIds.add(po.getCreateBy());
			userIds.add(po.getUpdateBy());
			companyIds.put(domain.getId(), domain);
		}
		// 填充公司税务信息
		List<Company> taxes = companyTaxLookup.findByCompanyIds(companyIds.keySet());
		for (Company t : CollectionUtils.emptyIfNull(taxes)) {
			Optional.ofNullable(companyIds.get(t.getId())).ifPresent(c -> c.setTaxes(t.getTaxes()));
		}

		Map<Integer, OumUserInfoRes> users = remoteUserDetailsFeign.findByUserIds(userIds);
		for (Company company : companies) {
			oumUserInfoConverter.fillCreated(users.get(company.getCreated().getCreateBy()), company.getCreated());
			oumUserInfoConverter.fillUpdated(users.get(company.getUpdated().getUpdateBy()), company.getUpdated());
		}
		return companies;
	}

	public Optional<Company> findById(CompanyId id) {
		return getOptById(id.getId())
				.map(CompanyPo::parseCompanyName)
				.map(companyConverter::toDomain)
				.stream()
				.peek(company -> getOptById(company.getParent().getId().getId())
						.map(CompanyPo::parseCompanyName)
						.map(companyConverter::toDomain)
						.ifPresent(company::setParent))
				.peek(company -> currencyRepository
						.findById(company.getCurrency().getId())
						.ifPresent(company::setCurrency))
				.peek(company -> currencyRepository
						.findById(company.getRegisteredCountryCurrency().getId())
						.ifPresent(company::setRegisteredCountryCurrency))
				.peek(company -> companyTaxLookup.findByCompanyIds(List.of(company.getId()))
						.stream()
						.filter(c -> Objects.equals(c.getId(), company.getId()))
						.findFirst()
						.map(Company::getTaxes)
						.ifPresent(company::setTaxes))
				.findFirst();
	}

}
