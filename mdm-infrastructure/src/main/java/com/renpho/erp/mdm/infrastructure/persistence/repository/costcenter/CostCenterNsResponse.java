package com.renpho.erp.mdm.infrastructure.persistence.repository.costcenter;

import java.io.Serializable;
import java.util.List;

import lombok.Data;

@Data
public class CostCenterNsResponse {

	/**
	 * result : {} data : [{"nsCostCenterId":216}] code : 200 message : 操作成功 success :
	 * true
	 */

	private ResultBean result;
	private String code;
	private String message;
	private boolean success;
	private List<DataBean> data;

	public static class ResultBean implements Serializable {
	}

	@Data
	public static class DataBean implements Serializable {
		/**
		 * nsCostCenterId : 216
		 */
		private int nsCostCenterId;
	}
}
