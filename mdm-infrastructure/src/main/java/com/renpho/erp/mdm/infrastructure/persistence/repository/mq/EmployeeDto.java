
package com.renpho.erp.mdm.infrastructure.persistence.repository.mq;

import lombok.Data;

/**
 * 员工数据传输对象
 */
@Data
public class EmployeeDto {

	/**
	 * 用户名称 示例值: 伊丽莎白
	 */
	private String name;

	/**
	 * 职位名称 示例值: 行政
	 */
	private String positionName;

	/**
	 * 主管nsId 示例值: 1231
	 */
	private String reportNsUserId;

	/**
	 * 手机号 示例值: 13711111111
	 */
	private String phone;

	/**
	 * 邮箱 示例值: <EMAIL>
	 */
	private String email;

	/**
	 * 工号 示例值: FT01000001
	 */
	private String code;

	/**
	 * 称呼 示例值: 女士
	 */
	private String salutation;

	/**
	 * oum用户id 示例值: 123
	 */
	private String userId;

	/**
	 * ns用户id（编辑时必填，新增不填） 示例值: 1515
	 */
	private String nsEmployeeId;

	/**
	 * token建议放参数里 示例值: 101
	 */
	private String token;

	/**
	 * 公司nsid 示例值: 37
	 */
	private Integer nsSubsidiaryId;

	private String departmentName;

	private String primaryKey;

}