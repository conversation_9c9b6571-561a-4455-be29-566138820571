package com.renpho.erp.mdm.infrastructure.persistence.repository.tax;


import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.renpho.erp.mdm.domain.exception.DataNotFoundException;
import com.renpho.erp.mdm.domain.exception.TaxCodeCheckException;
import com.renpho.erp.mdm.domain.tax.*;
import com.renpho.erp.mdm.domain.tax.dto.TaxCodeGroupDto;
import com.renpho.erp.mdm.infrastructure.persistence.mapper.taxcode.TypeManagerMapper;
import com.renpho.erp.mdm.infrastructure.persistence.po.taxcode.TypeManagerPo;
import com.renpho.erp.mdm.infrastructure.persistence.po.taxcode.converter.TypeManagerConverter;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.stereotype.Repository;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @author: Wyatt
 * @description: 税款实体
 * @date: 2024/11/20 12:10
 */
@Repository
public class TaxTypeRepositoryImpl extends ServiceImpl<TypeManagerMapper, TypeManagerPo> implements TaxTypeRepository {


    private final TypeManagerConverter typeManagerConverter;
    private final TaxTypeLookup taxTypeLookup;
    private final TaxCodeLookup taxCodeLookup;

    public TaxTypeRepositoryImpl(TypeManagerConverter typeManagerConverter, TaxTypeLookup taxTypeLookup, TaxCodeLookup taxCodeLookup) {
        this.typeManagerConverter = typeManagerConverter;
        this.taxTypeLookup = taxTypeLookup;
        this.taxCodeLookup = taxCodeLookup;
    }

    @Override
    public List<TypeManager> findAll(TypeManager typeManager) {
        List<TypeManagerPo> dbData = this.lambdaQuery()
                .eq(typeManager.getIsDeleted() != null, TypeManagerPo::getIsDeleted, typeManager.getIsDeleted())
                .list();
        if (CollectionUtils.isEmpty(dbData)){
            return Collections.emptyList();
        }
        List<Integer> idList = dbData.stream().map(TypeManagerPo::getId).toList();
        List<TaxCodeGroupDto> groupDtoList = taxCodeLookup.findCountByIdList(idList);
        Map<Integer, Long> map = groupDtoList.stream().collect(Collectors.toMap(TaxCodeGroupDto::getTypeId, TaxCodeGroupDto::getCountNumber));
        return dbData.stream().map(data -> {
            TypeManager domain = typeManagerConverter.toDomain(data);
            if (null != map){
                Long count = map.get(data.getId());
                if (null != count && count > 0) {
                    domain.setBusinessTypeUse(NumberUtils.INTEGER_ONE);
                } else {
                    domain.setBusinessTypeUse(NumberUtils.INTEGER_ZERO);
                }
            }else {
                domain.setBusinessTypeUse(NumberUtils.INTEGER_ZERO);
            }
            return domain;
        }).collect(Collectors.toList());
    }

    @Override
    public TypeManager add(TypeManager typeManager) {
        typeManager.assertCheckTypeName(taxTypeLookup);
        TypeManagerPo po = typeManagerConverter.toPo(typeManager);
        this.save(po);
        TaxTypeManagerId taxTypeManagerId = new TaxTypeManagerId(po.getId());
        return TypeManager.builder().id(taxTypeManagerId).build();
    }

    @Override
    public TypeManager update(TypeManager typeManager) {
        if (getById(typeManager.getId()) == null) {
            throw new DataNotFoundException();
        }
        typeManager.assertCheckTypeName(taxTypeLookup);
        this.lambdaUpdate()
                .set(TypeManagerPo::getName, typeManager.getTypeName())
                .eq(TypeManagerPo::getId, typeManager.getId().getId())
                .update();
        TaxTypeManagerId taxTypeManagerId = new TaxTypeManagerId(typeManager.getId().getId());
        return TypeManager.builder().id(taxTypeManagerId).build();
    }

    @Override
    public void delete(Integer id) {
        TypeManagerPo typeManagerPo = getById(id);
        if (typeManagerPo == null) {
            throw new DataNotFoundException();
        }
        TypeCodeManger typeCodeManger = new TypeCodeManger();
        TypeManager typeManager = typeManagerConverter.toDomain(typeManagerPo);
        typeCodeManger.setTypeManager(typeManager);
        TypeCodeManger data = taxCodeLookup.findData(typeCodeManger);
        if (data != null){
            throw new TaxCodeCheckException();
        }
        this.lambdaUpdate()
                .set(TypeManagerPo::getIsDeleted, NumberUtils.INTEGER_ONE)
                .eq(TypeManagerPo::getId, id)
                .update();
    }

    @Override
    public TypeManager findById(Integer id) {
        TypeManagerPo typeManagerPo = getById(id);
        return typeManagerConverter.toDomain(typeManagerPo);
    }
}
