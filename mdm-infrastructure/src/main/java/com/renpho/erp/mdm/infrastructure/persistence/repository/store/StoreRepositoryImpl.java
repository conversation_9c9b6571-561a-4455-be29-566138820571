package com.renpho.erp.mdm.infrastructure.persistence.repository.store;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.renpho.erp.ims.client.feign.warehouse.vo.WarehouseVo;
import com.renpho.erp.mdm.domain.exception.DataNotFoundException;
import com.renpho.erp.mdm.domain.exception.ParamIllegalException;
import com.renpho.erp.mdm.domain.saleschannel.SalesChannelSiteRepository;
import com.renpho.erp.mdm.domain.store.*;
import com.renpho.erp.mdm.infrastructure.feignclient.ims.RemoteWarehouseFeign;
import com.renpho.erp.mdm.infrastructure.persistence.mapper.store.StoreMapper;
import com.renpho.erp.mdm.infrastructure.persistence.mapper.store.StorePermissionMapper;
import com.renpho.erp.mdm.infrastructure.persistence.po.store.StorePermissionPo;
import com.renpho.erp.mdm.infrastructure.persistence.po.store.StorePo;
import com.renpho.erp.mdm.infrastructure.persistence.po.store.converter.StoreConverter;
import lombok.RequiredArgsConstructor;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 店铺信息 Repository.
 *
 * <AUTHOR>
 * @since 2024/9/14
 */
@Repository
@RequiredArgsConstructor
public class StoreRepositoryImpl extends ServiceImpl<StoreMapper, StorePo> implements StoreRepository {

	private final StoreLookup storeLookup;

	private final SalesChannelSiteRepository salesChannelSiteRepository;

	private final StoreCompanyRepository storeCompanyRepository;

	private final StorePaymentRepository storePaymentRepository;

	private final StoreConverter storeConverter;

	private final RemoteWarehouseFeign remoteWarehouseFeign;

	private final StorePermissionMapper storePermissionMapper;

	private final StorePermissionService storePermissionService;

	@Override
	@Transactional
	public Store add(Store root) {
		root.assertNameNotDuplicate(storeLookup);
		root.assertOnlyOneActiveCompany();
        root.assertSiteBelongsToChannel(salesChannelSiteRepository);
		Map<Integer, Integer> warehouseVoMap = assertWarehouse(root);
		assertWarehouseVoMap(root,warehouseVoMap);
		StorePo po = storeConverter.toPo(root);
		save(po);
		StoreId id = new StoreId(po.getId());
		storeCompanyRepository.saveBatch(id, root.getCompanies());
		storePaymentRepository.saveBatch(id, root.getPayments());
		// 自动把店铺负责人添加到【店铺用户权限列表】
		this.saveStorePermission(root, po.getId());
		return storeLookup.findById(id).orElseThrow(DataNotFoundException::new);
	}

	/**
	 * 新增/修改负责人时，自动把其添加到【店铺用户权限列表】
	 * @param store 店铺信息
	 * @param storeId 店铺id
	 */
	private void saveStorePermission(Store store, Integer storeId) {
		// 店铺负责人id
		Integer storeManagerId = store.getStoreManagerId();
		// 店铺负责人是否已存在
		StorePermissionPo storePermission = storePermissionMapper.getByStoreIdAndUserId(storeId, storeManagerId);
		if (Objects.nonNull(storePermission)) {
			store.setStoreManagerExist(true);
			return;
		}
		store.setStoreManagerExist(false);
		// 构建店铺权限
		storePermission = this.buildPermission(storeManagerId, storeId);
		storePermissionMapper.insert(storePermission);
	}

	@Override
	@Transactional
	public Store update(Store root) {
		Integer exist = root.getId().getId();
		if (!lambdaQuery().eq(StorePo::getId, exist).exists()) {
			throw new DataNotFoundException();
		}

		root.assertNameNotDuplicate(storeLookup);
		root.assertOnlyOneActiveCompany();
        root.assertSiteBelongsToChannel(salesChannelSiteRepository);
		Map<Integer, Integer> warehouseVoMap = assertWarehouse(root);
		assertWarehouseVoMap(root,warehouseVoMap);
		StorePo po = storeConverter.toPo(root);
		po.setStoreName(null);
		po.setSalesChannelId(null);
		lambdaUpdate()
				.set(StorePo::getSiteId, po.getSiteId())
				.set(StorePo::getIsTaxWithheldByPlatform, po.getIsTaxWithheldByPlatform())
				.set(StorePo::getOpenTime, po.getOpenTime())
				.set(StorePo::getCloseTime, po.getCloseTime())
				.set(StorePo::getNsStoreId, po.getNsStoreId())
				.set(StorePo::getNsStoreName, po.getNsStoreName())
				.set(StorePo::getNsCustomerId, po.getNsCustomerId())
				.set(StorePo::getNsCustomerName, po.getNsCustomerName())
				.eq(StorePo::getId, po.getId())
				.update(po);
		updateById(po);
		// 自动把店铺负责人添加到【店铺用户权限列表】
		this.saveStorePermission(root, po.getId());
		StoreId id = new StoreId(po.getId());
		storeCompanyRepository.saveBatch(id, root.getCompanies());
		storePaymentRepository.saveBatch(id, root.getPayments());
		return storeLookup.findById(id).orElseThrow(DataNotFoundException::new);
	}

	private Map<Integer, Integer> assertWarehouse(Store root) {
		List<Integer> idList = new ArrayList<>();
		if (StringUtils.isNotBlank(root.getChannelWarehouse())) {
			List<Integer> cascadeList = JSONObject.parseArray(root.getChannelWarehouse(), Integer.class);
			idList.add(cascadeList.get(cascadeList.size() - 1));
		}
		if (StringUtils.isNotBlank(root.getAwdWarehouse())) {
			List<Integer> cascadeList = JSONObject.parseArray(root.getAwdWarehouse(), Integer.class);
			idList.add(cascadeList.get(cascadeList.size() - 1));
		}
		if (StringUtils.isNotBlank(root.getOverseaWarehouse())) {
			List<Integer> cascadeList = JSONObject.parseArray(root.getOverseaWarehouse(), Integer.class);
			idList.add(cascadeList.get(cascadeList.size() - 1));
		}
		List<WarehouseVo> warehouseVos = remoteWarehouseFeign.queryWarehouseByCodeList(idList);
		return warehouseVos.stream().collect(Collectors.toMap(WarehouseVo::getId, WarehouseVo::getId, (o, n) -> n));
	}

	public void assertWarehouseVoMap(Store root, Map<Integer, Integer> warehouseVoMap) {
		if (null != root.getChannelWarehouse()) {
			List<Integer> cascadeList = JSONObject.parseArray(root.getChannelWarehouse(), Integer.class);
			Optional.ofNullable(warehouseVoMap.get(cascadeList.get(cascadeList.size() - 1))).orElseThrow(() -> new ParamIllegalException("warehouse.not_found"));
		}
		if (null != root.getAwdWarehouse()) {
			List<Integer> cascadeList = JSONObject.parseArray(root.getAwdWarehouse(), Integer.class);
			Optional.ofNullable(warehouseVoMap.get(cascadeList.get(cascadeList.size() - 1))).orElseThrow(() -> new ParamIllegalException("warehouse.not_found"));
		}
		if (null != root.getOverseaWarehouse()) {
			List<Integer> cascadeList = JSONObject.parseArray(root.getOverseaWarehouse(), Integer.class);
			Optional.ofNullable(warehouseVoMap.get(cascadeList.get(cascadeList.size() - 1))).orElseThrow(() -> new ParamIllegalException("warehouse.not_found"));
		}
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public Map<Integer, Set<Integer>> addPermission(Set<Integer> storeIds, Set<Integer> userIds) {
		// 已存在的店铺授权用户列表
		List<StorePermissionPo> oldPermissionList = storePermissionMapper.getByStoreIdsAndUserIds(storeIds, userIds);
		// 已存在的店铺授权用户Map
		Map<String, StorePermissionPo> permissionMap = oldPermissionList.stream().collect(Collectors.toMap(p -> p.getStoreId() + "-" + p.getUserId(), Function.identity(), (x1, x2) -> x1));
		// 店铺权限列表
		List<StorePermissionPo> permissionList = new ArrayList<>();
		storeIds.forEach(storeId -> userIds.forEach(userId -> {
			// 不存在时，才处理
            if (Objects.isNull(permissionMap.get(storeId + "-" + userId))) {
				// 构建店铺权限
				StorePermissionPo permission = this.buildPermission(userId, storeId);
				permissionList.add(permission);
            }
        }));
		// 批量入库
		storePermissionService.saveBatch(permissionList);
		// 用户店铺权限Map，key为用户id，value为店铺id集合
		return permissionList.stream().collect(Collectors.groupingBy(StorePermissionPo::getUserId,
				Collectors.mapping(StorePermissionPo::getStoreId, Collectors.toSet())));
	}

	/**
	 * 构建店铺权限
	 * @param userId 用户id
	 * @param storeId 店铺id
	 * @return StorePermissionPo
	 */
	private @NotNull StorePermissionPo buildPermission(Integer userId, Integer storeId) {
		StorePermissionPo permission = new StorePermissionPo();
		permission.setStoreId(storeId);
		permission.setUserId(userId);
		return permission;
	}

	@Override
	public Map<Integer, Set<Integer>> removePermission(Set<Integer> storeIds, Set<Integer> userIds) {
		// 已存在的店铺权限列表
		List<StorePermissionPo> permissionList = storePermissionMapper.getByStoreIdsAndUserIds(storeIds, userIds);
		// 店铺权限主键id集
		Set<Integer> ids = permissionList.stream().map(StorePermissionPo::getId).collect(Collectors.toSet());
		// 删除
		if (CollectionUtils.isNotEmpty(ids)) {
			storePermissionMapper.deleteBatchIds(ids);
		}
		// 已删除的用户店铺权限Map，key为用户id，value为店铺id集合
		return permissionList.stream().collect(Collectors.groupingBy(StorePermissionPo::getUserId,
				Collectors.mapping(StorePermissionPo::getStoreId, Collectors.toSet())));
	}
}
