package com.renpho.erp.mdm.infrastructure.persistence.repository.tax;


import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.renpho.erp.mdm.domain.exception.BusinessException;
import com.renpho.erp.mdm.domain.exception.DataNotFoundException;
import com.renpho.erp.mdm.domain.tax.TaxCodeLookup;
import com.renpho.erp.mdm.domain.tax.TaxTypeCodeRepository;
import com.renpho.erp.mdm.domain.tax.TypeCodeId;
import com.renpho.erp.mdm.domain.tax.TypeCodeManger;
import com.renpho.erp.mdm.infrastructure.persistence.mapper.taxcode.TypeCodeManagerMapper;
import com.renpho.erp.mdm.infrastructure.persistence.po.taxcode.TypeCodeManagerPo;
import com.renpho.erp.mdm.infrastructure.persistence.po.taxcode.converter.TypeManagerConverter;
import com.renpho.karma.i18n.I18nMessageKit;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.stereotype.Repository;

/**
 * @author: Wyatt
 * @description: 税码信息
 * @date: 2024/11/21 14:44
 */
@Repository
public class TaxTypeCodeRepositoryImpl extends ServiceImpl<TypeCodeManagerMapper, TypeCodeManagerPo> implements TaxTypeCodeRepository {


    private TaxCodeLookup taxCodeLookup;
    private TypeManagerConverter typeManagerConverter;

    public TaxTypeCodeRepositoryImpl(TaxCodeLookup taxCodeLookup, TypeManagerConverter typeManagerConverter) {
        this.taxCodeLookup = taxCodeLookup;
        this.typeManagerConverter = typeManagerConverter;
    }

    @Override
    public TypeCodeManger add(TypeCodeManger typeCodeManger) {
		typeCodeManger.assertNameNotDuplicate(taxCodeLookup);
        typeCodeManger.assertCheckNotDuplicate(taxCodeLookup);
        TypeCodeManagerPo typeCodeManagerPo = typeManagerConverter.codeAddToPo(typeCodeManger);
        this.save(typeCodeManagerPo);
        return TypeCodeManger.builder().id(new TypeCodeId(typeCodeManagerPo.getId())).build();
    }

    @Override
    public TypeCodeManger update(TypeCodeManger typeCodeManger) {
        TypeCodeManagerPo dbData = getById(typeCodeManger.getId());
        if (null == dbData) {
            throw new DataNotFoundException();
        }
        boolean isUse = dbData.getIsUse().equals(NumberUtils.INTEGER_ONE);
        if (isUse){
            throw new BusinessException(I18nMessageKit.getMessage("tax.code.use.update"));
        }
        typeCodeManger.assertNameNotDuplicate(taxCodeLookup);
        typeCodeManger.assertCheckNotDuplicate(taxCodeLookup);

        TypeCodeManagerPo typeCodeManagerPo = typeManagerConverter.codeAddToPo(typeCodeManger);
        this.updateById(typeCodeManagerPo);
        return TypeCodeManger.builder().id(new TypeCodeId(typeCodeManagerPo.getId())).build();
    }
}
