package com.renpho.erp.mdm.infrastructure.persistence.repository.rate;

import lombok.Data;
import lombok.EqualsAndHashCode;


@EqualsAndHashCode(callSuper = true)
@Data
class MonthExchangeRateNsRequest extends NsRequestBase  {

    /**
     * token : 107
     * originalCurrency : AUD
     * targetCurrency : USD
     * exchangeRate : 1.5153
     * effectiveDate : 2024-10-01
     */

    private String originalCurrency;
    private String targetCurrency;
    private double exchangeRate;
    private String effectiveDate;

}
