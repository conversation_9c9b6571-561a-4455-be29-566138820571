package com.renpho.erp.mdm.infrastructure.persistence.repository.rate;

import com.renpho.erp.mdm.domain.rate.MonthExchangeRateRepository;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.messaging.Message;

import java.util.function.Consumer;

@Slf4j
@Configuration
public class MonthExchangeRateConsumer {
    @Resource
    private MonthExchangeRateRepository monthExchangeRateRepository;
    @Bean
    public Consumer<Message<Integer>> consumeMonthExchangeRateSyncNs() {
        return msg -> {
            MonthExchangeRateConsumer.log.info("收到月度汇率同步 ns 消息, msg header: [{}]", msg.getHeaders());
            Integer id = msg.getPayload();
            monthExchangeRateRepository.syncNs(id);
            MonthExchangeRateConsumer.log.info("月度汇率同步 ns 消息: [{}]", id);
        };
    }


}
