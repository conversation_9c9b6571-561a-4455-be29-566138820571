package com.renpho.erp.mdm.infrastructure.persistence.crawler;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.renpho.erp.mdm.infrastructure.dingtalk.DingTalkUtil;
import com.renpho.erp.mdm.infrastructure.persistence.crawler.optlog.MdmSystemModule;
import com.renpho.erp.mdm.infrastructure.persistence.crawler.optlog.MonthExchangeRateAddSnapSource;
import com.renpho.erp.mdm.infrastructure.persistence.crawler.optlog.MonthExchangeRateBusinessModule;
import com.renpho.erp.mdm.infrastructure.persistence.mapper.currency.CurrencyMapper;
import com.renpho.erp.mdm.infrastructure.persistence.mapper.rate.FluctuateExchangeRateMapper;
import com.renpho.erp.mdm.infrastructure.persistence.mapper.rate.MonthExchangeRateMapper;
import com.renpho.erp.mdm.infrastructure.persistence.po.currency.CurrencyPO;
import com.renpho.erp.mdm.infrastructure.persistence.po.rate.FluctuateExchangeRatePO;
import com.renpho.erp.mdm.infrastructure.persistence.po.rate.MonthExchangeRatePO;
import com.renpho.erp.oplog.log.OpLog;
import com.renpho.erp.oplog.log.enums.BusinessType;
import com.taobao.api.ApiException;
import com.xxl.job.core.handler.annotation.XxlJob;
import jakarta.annotation.Resource;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.http.HttpEntity;
import org.apache.http.HttpStatus;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.util.EntityUtils;
import org.jetbrains.annotations.NotNull;
import org.jsoup.Jsoup;
import org.jsoup.nodes.Document;
import org.jsoup.nodes.Element;
import org.jsoup.select.Elements;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.time.temporal.TemporalAdjusters;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * 销售渠道. 月度汇率用中间汇率:月度抓取用中间价： 浮动汇率使用买入汇率:浮动汇率你直接用原来的那套代码,走老系统接口
 *
 * <AUTHOR>
 * @since 2024/9/13
 */
@Slf4j
@Component
public class ExchangeRateCrawler {

    public static final String XE_URL = "https://www.xe.com/api/protected/charting-rates/?fromCurrency={}&toCurrency={}&crypto=true";
    public static final String API_URL = "https://sapi.k780.com/?app=finance.rate&scur={}&tcur={}&appkey={}&sign={}&format=json";
    private static final Pattern PATTERN = Pattern.compile("\\d+\\.\\d+");
    public static final String DING_DING_ID = "95e209610edccb4bf0ac86b39d00f305335e8768fb67bb00e979340219d7c935";
    @Resource
    private ExchangeRateCrawler exchangeRateCrawler;
    @Resource
    private NowApiProperties nowApiProperties;
    @Resource
    private FluctuateExchangeRateMapper fluctuateExchangeRateMapper;
    @Resource
    private MonthExchangeRateMapper monthExchangeRateMapper;
    @Resource
    private CurrencyMapper currencyMapper;
    @Resource
    private CrawlerHttpClientUtil clientUtil;

    @Resource
    private DingTalkUtil dingTalkUtil;

    private static @NotNull MonthExchangeRatePO getMonthExchangeRatePO(MonthExchangeRatePO po, BigDecimal rate, Result result) {
        MonthExchangeRatePO monthExchangeRatePO = new MonthExchangeRatePO();
        monthExchangeRatePO.setOriginalCurrencyId(po.getOriginalCurrencyId());
        monthExchangeRatePO.setTargetCurrencyId(po.getTargetCurrencyId());
        monthExchangeRatePO.setExchangeRate(rate);
        //todo 月度汇率时间
        monthExchangeRatePO.setEffectiveDateTime(LocalDateTime.now());
        monthExchangeRatePO.setCreateBy(0);
        monthExchangeRatePO.setCreateTime(result.now());
        monthExchangeRatePO.setUpdateBy(0);
        monthExchangeRatePO.setUpdateTime(result.now());
        return monthExchangeRatePO;
    }

    private static @NotNull FluctuateExchangeRatePO getFluctuateExchangeRatePO(FluctuateExchangeRatePO po, LocalDateTime now, BigDecimal rate) {
        FluctuateExchangeRatePO fluctuateExchangeRatePO1 = new FluctuateExchangeRatePO();
        fluctuateExchangeRatePO1.setOriginalCurrencyId(po.getOriginalCurrencyId());
        fluctuateExchangeRatePO1.setTargetCurrencyId(po.getTargetCurrencyId());
        fluctuateExchangeRatePO1.setExchangeRate(po.getExchangeRate());
        fluctuateExchangeRatePO1.setCreateBy(0);
        fluctuateExchangeRatePO1.setCreateTime(now);
        fluctuateExchangeRatePO1.setExchangeRate(rate);
        fluctuateExchangeRatePO1.setUpdateBy(0);
        fluctuateExchangeRatePO1.setUpdateTime(now);
        return fluctuateExchangeRatePO1;
    }

    private static void printMessage(String targetCurrencyName, String originalCurrencyName) {
        log.error("获取月度汇率异常：targetCurrencyName = {} , originalCurrencyName = {}  ", targetCurrencyName, originalCurrencyName);
    }

    private static BigDecimal getSearchEnHtml(CrawlerHttpClientUtil clientUtil, String sCurrency, String tCurrency, String day) {
        if (StrUtil.hasBlank(sCurrency, tCurrency, day)) {
            return null;
        }
        // https://chl.cn/huilv/huansuan-ajax.asp?1*cny*usd*2024-09-26
        String url = StrUtil.format("https://chl.cn/huilv/huansuan-ajax.asp?{}*{}*{}*{}", 1, sCurrency.toLowerCase(), tCurrency.toLowerCase(), day);
        String html = "";
        HttpPost httpPost = new HttpPost(url);
        CrawlerHttpClientUtil.setHead(httpPost);
        clientUtil.setProxy(httpPost);

        try (CloseableHttpResponse response = clientUtil.getHttpClient().execute(httpPost)) {
            // 判断响应状态为200，进行处理
            if (response.getStatusLine().getStatusCode() == HttpStatus.SC_OK) {
                HttpEntity httpEntity = response.getEntity();
                html = EntityUtils.toString(httpEntity, "utf-8");
            } else {
                log.error(EntityUtils.toString(response.getEntity(), "utf-8"));
                log.error("请求异常, sCurrency = " + sCurrency + ", tCurrency = " + tCurrency + ", day = " + day);
            }
        } catch (IOException e) {
            log.error("请求异常, sCurrency = " + sCurrency + ", tCurrency = " + tCurrency + ", day = " + day, e);
        }
        if (StrUtil.isBlank(html)) {
            return null;
        }

        Document doc = Jsoup.parse(html);
        if (Objects.isNull(doc)) {
            return null;
        }
        // 查找包含 <em> 标签的元素
        Elements emElements = doc.select("em");
        if (Objects.isNull(emElements)) {
            return null;
        }
        String rateStr = "";
        for (Element em : emElements) {
            String text = em.text();
            Matcher m = PATTERN.matcher(text);
            if (m.matches()) {
                rateStr = text;
                break;
            }
        }
        if (StrUtil.isEmpty(rateStr)) {
            log.error("爬取异常, sCurrency = " + sCurrency + ", tCurrency = " + tCurrency + ", day = " + day);
            log.error("异常爬取页面 {}", html);
            return null;
        }
        return new BigDecimal(rateStr);

    }

    // 获取今天的零点时间戳
    private static long getTodayMidnightTimestamp() {
        Calendar calendar = Calendar.getInstance();
        calendar.set(Calendar.HOUR_OF_DAY, 0);
        calendar.set(Calendar.MINUTE, 0);
        calendar.set(Calendar.SECOND, 0);
        calendar.set(Calendar.MILLISECOND, 0);
        return calendar.getTimeInMillis();
    }

    private static void setXeHeader(HttpPost httpPost) {
        httpPost.setHeader("Connection", "keep-alive");
        httpPost.setHeader("sec-ch-ua", "\";Not A Brand\";v=\"99\", \"Chromium\";v=\"94\"");
        httpPost.setHeader("Authorization", "Basic bG9kZXN0YXI6cHVnc25heA==");
        httpPost.setHeader("sec-ch-ua-mobile", "?0");
        httpPost.setHeader("User-Agent", "Mozilla/5.0 (Windows NT 10.0; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/94.0.4606.71 Safari/537.36 SE 2.X MetaSr 1.0");
        httpPost.setHeader("sec-ch-ua-platform", "\"Windows\"");
        httpPost.setHeader("Accept", "*/*");
        httpPost.setHeader("Sec-Fetch-Site", "same-origin");
        httpPost.setHeader("Sec-Fetch-Mode", "cors");
        httpPost.setHeader("Sec-Fetch-Dest", "empty");
        httpPost.setHeader("Referer", "https://www.xe.com/currencycharts/?from=USD&to=CNY&view=1M");
        httpPost.setHeader("Accept-Language", "zh-CN,zh;q=0.9");
        httpPost.setHeader("Cookie", "_gcl_au=1.1.1230220573.1728719858; _ga=GA1.1.925631060.1728719860; IR_gbd=xe.com; _fbp=fb.1.1728719861720.909994977462253605; _y2=1%3AeyJjIjp7fX0%3D%3AMTc0OTg2MjMwNA%3D%3D%3A2; __hs_cookie_cat_pref=1:true_2:true_3:true; hubspotutk=92f0947f728ec9e1badde2e6f2d5c0a9; __hssrc=1; __hstc=123240596.92f0947f728ec9e1badde2e6f2d5c0a9.1728719865239.1728719865239.1728885759506.2; __gads=ID=1ecbe7fe696bbc65:T=1728886433:RT=1728886433:S=ALNI_Mao9z2Gofb7CxdeyRDSdBiOEfzU_g; __gpi=UID=00000f4239ef666d:T=1728886433:RT=1728886433:S=ALNI_MYWsrJkygrrLFqrb_R7lBPTC1sp3A; __eoi=ID=500eaedc3b089423:T=1728886433:RT=1728886433:S=AA-Afjbi89kuSs5ALjK9pqyZL3Zy; amp_470887=SDO6LDJKWIIY-dkMgjz9Jk...1ia4pu26u.1ia4qolsp.g.5.l; IR_12610=1728886626525%7C0%7C1728886626525%7C%7C; _uetsid=e916ce5089f111ef80639364b8073557; _uetvid=a8135bb0886f11ef8d068158aa2703a8; __hssc=123240596.4.1728885759506; _ga_KRKJ3PLCP1=GS1.1.1728885754.1.1.1728886650.60.0.0; _yi=1%3AeyJsaSI6bnVsbCwic2UiOnsiYyI6MiwiZWMiOjM3LCJsYSI6MTcyODg4NjY1MTIzOSwicCI6Miwic2MiOjg5Mn0sInUiOnsiaWQiOiIyMmU4NDM1Yi0zMjQ1LTQwNDAtYjcyNS0yYzlkNTAyMDkzYTgiLCJmbCI6IjAifX0%3D%3ALTE4MDY5MDc0ODg%3D%3A2");
        httpPost.setHeader("If-None-Match", "\"gkbtffxh42nm0\"");
    }

    private static BigDecimal getZHSearchEnHtml(CrawlerHttpClientUtil clientUtil, String sCurrency, String tCurrency, String day) {
        if (StrUtil.hasBlank(sCurrency, tCurrency, day)) {
            return null;
        }
        // https://chl.cn/huilv/huansuan-ajax.asp?1*cny*usd*2024-09-26
        String url = StrUtil.format("https://chl.cn/huilv/huansuan-ajax.asp?{}*{}*{}*{}", 1, sCurrency.toLowerCase(), tCurrency.toLowerCase(), day);
        String html = "";
        HttpPost httpPost = new HttpPost(url);
        CrawlerHttpClientUtil.setHead(httpPost);
        clientUtil.setProxy(httpPost);

        try (CloseableHttpResponse response = clientUtil.getHttpClient().execute(httpPost)) {
            // 判断响应状态为200，进行处理
            if (response.getStatusLine().getStatusCode() == HttpStatus.SC_OK) {
                HttpEntity httpEntity = response.getEntity();
                html = EntityUtils.toString(httpEntity, "utf-8");
            } else {
                log.error(EntityUtils.toString(response.getEntity(), "utf-8"));
                log.error("请求异常, sCurrency = " + sCurrency + ", tCurrency = " + tCurrency + ", day = " + day);
            }
        } catch (IOException e) {
            log.error("请求异常, sCurrency = " + sCurrency + ", tCurrency = " + tCurrency + ", day = " + day, e);
        }
        if (StrUtil.isBlank(html)) {
            return null;
        }

        Document doc = Jsoup.parse(html);
        if (Objects.isNull(doc)) {
            return null;
        }
        // 查找包含 <em> 标签的元素
        Elements emElements = doc.select("em");
        if (Objects.isNull(emElements)) {
            return null;
        }
        String rateStr = "";
        for (Element em : emElements) {
            String text = em.text();
            Matcher m = PATTERN.matcher(text);
            if (m.matches()) {
                rateStr = text;
                break;
            }
        }
        if (StrUtil.isEmpty(rateStr)) {
            log.error("爬取异常, sCurrency = " + sCurrency + ", tCurrency = " + tCurrency + ", day = " + day);
            log.error("异常爬取页面 {}", html);
            return null;
        }
        return new BigDecimal(rateStr);

    }

    private static void printErrorMessage(Map<String, Object> netExchangeRate) {
        log.error("未查询到汇率/网络故障:{}", JSON.toJSONString(netExchangeRate, SerializerFeature.WriteMapNullValue));
    }

    private static void printErrorInfo(String targetCurrencyName, String originalCurrencyName) {
        log.error("获取浮动汇率异常：targetCurrencyName = {} , originalCurrencyName = {}  ", targetCurrencyName, originalCurrencyName);
    }

    /**
     * 月度汇率 月初 拿月底的汇率数据，每月1号11点执行
     */

    @XxlJob("updateNextMonthExchangeRates")
    public void updateNextMonthExchangeRates() {
        log.info("updateNextMonthExchangeRates start ...");

        Result result = getResult();
        for (MonthExchangeRatePO po : result.monthExchangeRatePOS()) {
            String targetCurrencyName = result.idSymbalMap().get(po.getTargetCurrencyId());
            String originalCurrencyName = result.idSymbalMap().get(po.getOriginalCurrencyId());
            BigDecimal rate = getSearchEnHtml(clientUtil, originalCurrencyName, targetCurrencyName, result.formattedDate());
            if (Objects.isNull(rate)) {
                printMessage(targetCurrencyName, originalCurrencyName);
                continue;
            }
            MonthExchangeRatePO monthExchangeRatePO = getMonthExchangeRatePO(po, rate, result);
            List<Object> idList = monthExchangeRateMapper.selectObjs(Wrappers.<MonthExchangeRatePO>lambdaQuery().select(MonthExchangeRatePO::getId).eq(MonthExchangeRatePO::getOriginalCurrencyId, po.getOriginalCurrencyId()).eq(MonthExchangeRatePO::getTargetCurrencyId, po.getTargetCurrencyId()).eq(MonthExchangeRatePO::getCreateTime, result.now()).last("limit 1"));
            if (CollectionUtils.isEmpty(idList)) {
                exchangeRateCrawler.insert(monthExchangeRateMapper, monthExchangeRatePO);
            } else {
                printMessage(targetCurrencyName, originalCurrencyName);
            }

        }

        earlyWarning(result);


    }

    @SuppressWarnings("all")
    private void earlyWarning(Result result) {
        //新增预警逻辑：假如上月存在USD兑CNY的汇率，本月任务定时任务完成后没有产生【生效时间】=本月的 汇率需要做预警提示
        // 获取当前日期时间
        LocalDate now = LocalDate.now();

        // 获取上个月的第一天
        LocalDate firstDayOfLastMonth = now.minusMonths(1).with(TemporalAdjusters.firstDayOfMonth());
        LocalDateTime startOfLastMonth = firstDayOfLastMonth.atStartOfDay();
        // 获取上个月的最后一天
        LocalDate lastDayOfLastMonth = now.minusMonths(1).with(TemporalAdjusters.lastDayOfMonth());
        LocalDateTime endOfLastMonth = lastDayOfLastMonth.atTime(LocalTime.MAX);
        //上个月的数据
        Set<String> lastMSet = monthExchangeRateMapper.selectList(Wrappers.<MonthExchangeRatePO>lambdaQuery().select(MonthExchangeRatePO::getOriginalCurrencyId, MonthExchangeRatePO::getTargetCurrencyId).between(MonthExchangeRatePO::getEffectiveDateTime, DateUtil.format(startOfLastMonth, "yyyy-MM-dd HH:mm:ss"), DateUtil.format(endOfLastMonth, "yyyy-MM-dd HH:mm:ss"))).stream().filter(s -> Objects.nonNull(s.getOriginalCurrencyId()) && Objects.nonNull(s.getTargetCurrencyId())).map(s -> s.getOriginalCurrencyId() + ":" + s.getTargetCurrencyId()).collect(Collectors.toSet());


        // 获取当前日期时间
        // 获取当前月的第一天
        LocalDate firstDayOfMonth = now.with(TemporalAdjusters.firstDayOfMonth());
        LocalDateTime startOfMonth = firstDayOfMonth.atStartOfDay();
        // 获取当前月的最后一天
        LocalDate lastDayOfMonth = now.with(TemporalAdjusters.lastDayOfMonth());
        LocalDateTime endOfMonth = lastDayOfMonth.atTime(LocalTime.MAX);

        //当前月的的数据
        Set<String> nowMset = monthExchangeRateMapper.selectList(Wrappers.<MonthExchangeRatePO>lambdaQuery().select(MonthExchangeRatePO::getOriginalCurrencyId, MonthExchangeRatePO::getTargetCurrencyId).between(MonthExchangeRatePO::getEffectiveDateTime, DateUtil.format(startOfMonth, "yyyy-MM-dd HH:mm:ss"), DateUtil.format(endOfMonth, "yyyy-MM-dd HH:mm:ss"))).stream().filter(s -> Objects.nonNull(s.getOriginalCurrencyId()) && Objects.nonNull(s.getTargetCurrencyId())).map(s -> s.getOriginalCurrencyId() + ":" + s.getTargetCurrencyId()).collect(Collectors.toSet());

        //上个月有这个月没有的数据
        Set<String> unFindMegSet = lastMSet.stream().filter(s -> !nowMset.contains(s)).map(s -> {
            String[] split = s.split(":");
            Integer s1 = Integer.parseInt(split[0]);
            Integer s2 = Integer.parseInt(split[1]);
            String targetCurrencyName = result.idSymbalMap().get(s1);
            String originalCurrencyName = result.idSymbalMap().get(s2);
            return "本位币：" + targetCurrencyName + "  外币：" + originalCurrencyName;
        }).collect(Collectors.toSet());

        if (CollectionUtils.isNotEmpty(unFindMegSet)) {
            String msg = StrUtil.format("警告信息：月度汇率拉取异常，上月存在这些月度汇率而本月不存在 {} ", JSON.toJSONString(unFindMegSet));
            log.error(msg);
            try {
                dingTalkUtil.getRobotSendMarkdownMsg("推送NS异常", new StringBuffer(StrUtil.nullToEmpty(msg)), null, true, DING_DING_ID);
            } catch (ApiException e) {
                log.error("钉钉消息异常", e);
            }
        }
    }

    @OpLog(snaptSource = MonthExchangeRateAddSnapSource.class, title = "爬虫入库月度汇率", businessType = BusinessType.INSERT, businessModule = MonthExchangeRateBusinessModule.class, systemModule = MdmSystemModule.class)
    public MonthExchangeRatePO insert(MonthExchangeRateMapper monthExchangeRateMapper, MonthExchangeRatePO monthExchangeRatePO) {
        monthExchangeRateMapper.insert(monthExchangeRatePO);
        return monthExchangeRatePO;
    }

    /**
     * 月度汇率 月底 更新到系统，每月月底那天11点执行
     */
    @XxlJob("updateMonthlyExchangeRates")
    public void updateMonthlyExchangeRates() {
        log.info("updateMonthlyExchangeRates start ...");

        Result result = getResult();
        for (MonthExchangeRatePO po : result.monthExchangeRatePOS) {
            String targetCurrencyName = result.idSymbalMap.get(po.getTargetCurrencyId());
            String orignalCurrencyName = result.idSymbalMap.get(po.getOriginalCurrencyId());
            BigDecimal rate = getSearchEnHtml(clientUtil, orignalCurrencyName, targetCurrencyName, result.formattedDate);
            if (Objects.isNull(rate)) {
                continue;
            }
            po.setExchangeRate(rate);
            po.setUpdateTime(result.now);
            po.setUpdateBy(0);
            monthExchangeRateMapper.updateById(po);
        }
    }

    /**
     * 浮动汇率
     */
    @XxlJob("pullExchangeRateToLocal")
    public void pullExchangeRateToLocal() {
        log.info("pullExchangeRateToLocal start ...");

        List<FluctuateExchangeRatePO> fluctuateExchangeRatePOS = fluctuateExchangeRateMapper.getPullBaseInfo();
        Map<Integer, String> idSymbalMap = currencyMapper.selectList(Wrappers.lambdaQuery()).stream().collect(Collectors.toMap(CurrencyPO::getId, CurrencyPO::getCode, (v1, v2) -> v2));
        for (FluctuateExchangeRatePO po : fluctuateExchangeRatePOS) {
            try {
                String targetCurrencyName = idSymbalMap.get(po.getTargetCurrencyId());
                String originalCurrencyName = idSymbalMap.get(po.getOriginalCurrencyId());
                Map<String, Object> netExchangeRate = getNetExchangeRate(clientUtil, originalCurrencyName, targetCurrencyName);
                printErrorMessage(netExchangeRate);
                if (Objects.isNull(netExchangeRate)) {
                    printErrorInfo(targetCurrencyName, originalCurrencyName);
                    continue;
                }
                BigDecimal rate = NumberUtil.toBigDecimal((String) netExchangeRate.get("rate"));
                if (Objects.nonNull(rate)) {
                    //todo 未来修改点
                    LocalDateTime now = LocalDateTime.now();
                    FluctuateExchangeRatePO fluctuateExchangeRatePO1 = getFluctuateExchangeRatePO(po, now, rate);
                    List<Object> idList = fluctuateExchangeRateMapper.selectObjs(Wrappers.<FluctuateExchangeRatePO>lambdaQuery()
                            .select(FluctuateExchangeRatePO::getId)
                            .eq(FluctuateExchangeRatePO::getOriginalCurrencyId, po.getOriginalCurrencyId())
                            .eq(FluctuateExchangeRatePO::getTargetCurrencyId, po.getTargetCurrencyId())
                            .eq(FluctuateExchangeRatePO::getEffectiveDateTime, now).last("limit 1"));
                    if (CollectionUtils.isEmpty(idList)) {
                        fluctuateExchangeRateMapper.insert(fluctuateExchangeRatePO1);
                    }

                } else {
                    printErrorInfo(targetCurrencyName, originalCurrencyName);
                    printErrorMessage(netExchangeRate);
                }
            } catch (Exception e) {
                log.error(e.getMessage(), e);
            }

        }

    }

    private @NotNull Result getResult() {
        Map<Integer, String> idSymbalMap = currencyMapper.selectList(Wrappers.lambdaQuery()).stream().collect(Collectors.toMap(CurrencyPO::getId, CurrencyPO::getCode, (v1, v2) -> v2));

        String formattedDate = DateTimeFormatter.ofPattern("yyyy-MM-dd").format(LocalDateTime.now());
        /* 计算上个月的第一天 */
        LocalDate lastMonthFirstDay = LocalDate.now().minusMonths(1).with(TemporalAdjusters.firstDayOfMonth());
        // 计算上个月的最后一天
        LocalDate lastMonthLastDay = LocalDate.now().minusMonths(1).with(TemporalAdjusters.lastDayOfMonth());
        List<MonthExchangeRatePO> monthExchangeRatePOS = monthExchangeRateMapper.getPullBaseInfo(LocalDateTime.of(lastMonthFirstDay, LocalTime.MIN), LocalDateTime.of(lastMonthLastDay, LocalTime.MAX));
        LocalDateTime now = LocalDateTime.now();
        return new Result(idSymbalMap, formattedDate, lastMonthFirstDay, lastMonthLastDay, monthExchangeRatePOS, now);
    }

    /**
     * 获取接口汇率
     * @param sourceCurrency 源币种
     * @param toCurrency 目标币种
     * @return 结果
     */
    private Map<String, Object> getNetExchangeRate(CrawlerHttpClientUtil clientUtil, String sourceCurrency, String toCurrency) {
        if (StrUtil.hasBlank(sourceCurrency, toCurrency)) {
            return null;
        }
        String url = StrUtil.format(API_URL, sourceCurrency, toCurrency, nowApiProperties.getNowApi().getAppKey(), nowApiProperties.getNowApi().getSign());
        // 创建HttpGet请求
        HttpGet httpGet = new HttpGet(url);
        String responseString = "";
        // 发起请求并获取响应
        try (CloseableHttpResponse response = clientUtil.getHttpClient().execute(httpGet)) {
            // 获取响应状态码
            int statusCode = response.getStatusLine().getStatusCode();
            System.out.println("响应状态码: " + statusCode);

            // 获取响应实体
            HttpEntity entity = response.getEntity();
            if (entity != null) {
                responseString = EntityUtils.toString(entity);
                System.out.println("响应内容: " + responseString);
            }
        } catch (IOException e) {
            log.error("请求异常", e);
        }

        if (Objects.isNull(responseString)) {
            return null;
        }
        log.info("---> {}", responseString);

        Map<String, Object> responseMap = JSONObject.parseObject(responseString, new TypeReference<>() {
        });

        if (!Objects.equals(NowApiResponseCodeEnums.SUCCESS.getCode(), responseMap.get("success"))) {
            return null;
        }
        // noinspection unchecked
        return (Map<String, Object>) responseMap.get("result");
    }

    @AllArgsConstructor
    @Getter
    enum NowApiResponseCodeEnums {

        /**
         * 结果code
         */
        SUCCESS("1", "success");

        private final String code;

        private final String msg;

    }

    private record Result(Map<Integer, String> idSymbalMap, String formattedDate, LocalDate lastMonthFirstDay,
                          LocalDate lastMonthLastDay, List<MonthExchangeRatePO> monthExchangeRatePOS,
                          LocalDateTime now) {

    }

}
