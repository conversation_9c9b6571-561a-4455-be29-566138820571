package com.renpho.erp.mdm.infrastructure.persistence.repository.tax;


import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.renpho.erp.mdm.domain.financialaccount.FinancialAccount;
import com.renpho.erp.mdm.domain.financialaccount.FinancialAccountLookup;
import com.renpho.erp.mdm.domain.tax.*;
import com.renpho.erp.mdm.domain.tax.dto.TaxCodeGroupDto;
import com.renpho.erp.mdm.infrastructure.persistence.mapper.taxcode.TypeCodeManagerMapper;
import com.renpho.erp.mdm.infrastructure.persistence.po.taxcode.TypeCodeManagerPo;
import com.renpho.erp.mdm.infrastructure.persistence.po.taxcode.converter.TypeManagerConverter;
import com.renpho.karma.dto.Paging;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Repository;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @author: Wyatt
 * @description: 税码查询实现类
 * @date: 2024/11/21 11:59
 */
@Repository
public class TaxCodeLookupImpl extends ServiceImpl<TypeCodeManagerMapper, TypeCodeManagerPo> implements TaxCodeLookup {


    private final TypeManagerConverter typeManagerConverter;
    private final TaxTypeLookup taxTypeLookup;
    private final FinancialAccountLookup financialAccountLookup;

    public TaxCodeLookupImpl(TypeManagerConverter typeManagerConverter, TaxTypeLookup taxTypeLookup, FinancialAccountLookup financialAccountLookup) {
        this.typeManagerConverter = typeManagerConverter;
        this.taxTypeLookup = taxTypeLookup;
        this.financialAccountLookup = financialAccountLookup;
    }

    @Override
    public Paging<TypeCodeManger> findPage(TypeCodePageQuery typeCodePageQuery) {
        Page<TypeCodeManagerPo> page = new Page<>(typeCodePageQuery.getPageIndex(), typeCodePageQuery.getPageSize());
        IPage<TypeCodeManagerPo> dbData = this.baseMapper.selectPage(page, typeCodePageQuery);
        Paging<TypeCodeManger> paging = Paging.of((int) page.getSize(), (int) page.getCurrent());
        if (CollectionUtils.isEmpty(dbData.getRecords())) {
            paging.setRecords(List.of());
            return paging;
        }
        List<Integer> typeIdList = dbData.getRecords().stream().map(TypeCodeManagerPo::getTypeId).toList();
        List<TypeManager> typeManagerList = taxTypeLookup.findByIds(typeIdList);
        Map<Integer, TypeManager> typeManagerMap = CollectionUtils.isNotEmpty(typeManagerList) ? typeManagerList.stream().collect(Collectors.toMap(a -> a.getId().getId(), Function.identity(), (o, n) -> o))
                : Collections.emptyMap();

        List<Integer> accountIdList = dbData.getRecords().stream().map(TypeCodeManagerPo::getAccountId).toList();
        List<FinancialAccount> allByIdList = financialAccountLookup.findAllByIdList(accountIdList);
        Map<Integer, FinancialAccount> accountMap = CollectionUtils.isNotEmpty(allByIdList) ? allByIdList.stream().collect(Collectors.toMap(a -> a.getId().getId(), Function.identity(), (o, n) -> o))
                : Collections.emptyMap();

        List<TypeCodeManger> response = dbData.getRecords().stream().map(data -> {
            TypeCodeManger typeCodeManger = typeManagerConverter.codeToDomain(data);
            typeCodeManger.setTypeManager(typeManagerMap.get(data.getTypeId()));
            typeCodeManger.setFinancialAccount(accountMap.get(data.getAccountId()));
            return typeCodeManger;
        }).collect(Collectors.toList());
        paging.setRecords(response);
        paging.setTotalCount((int) page.getTotal());
        return paging;
    }

    @Override
    public long countName(TypeCodeId id, String codeName) {
        Optional<Integer> byId = Optional.ofNullable(id).map(TypeCodeId::getId);
        return this.lambdaQuery()
                .ne(byId.isPresent(), TypeCodeManagerPo::getId, byId.orElse(null))
                .eq(TypeCodeManagerPo::getCode, codeName)
                .count();
    }

    @Override
    public TypeCodeManger findData(TypeCodeManger typeCodeManger) {
        TypeCodeManagerPo dbData = this.lambdaQuery()
                .eq(Objects.nonNull(typeCodeManger.getTypeManager().getId()), TypeCodeManagerPo::getTypeId, typeCodeManger.getTypeManager().getId().getId())
                .one();
        return typeManagerConverter.codeToDomain(dbData);
    }

    @Override
    public List<TaxCodeGroupDto> findCountByIdList(List<Integer> idList) {
        if (CollectionUtils.isEmpty(idList)){
            return List.of();
        }
        return this.baseMapper.findCountByIdList(idList);
    }

    @Override
    public TypeCodeManger findDataNotId(TypeCodeManger queryData) {

        LambdaQueryChainWrapper<TypeCodeManagerPo> queryLambdaQuery = this.lambdaQuery();
        if (Objects.nonNull(queryData.getId()) && Objects.nonNull(queryData.getId().getId())){
            queryLambdaQuery.ne(TypeCodeManagerPo::getId, queryData.getId().getId());
        }
        if (Objects.nonNull(queryData.getTypeManager()) && Objects.nonNull(queryData.getTypeManager().getId())){
            queryLambdaQuery.eq(TypeCodeManagerPo::getTypeId, queryData.getTypeManager().getId().getId());
        }
        return queryLambdaQuery
                .eq(StringUtils.isNotBlank(queryData.getCountry()), TypeCodeManagerPo::getCountry, queryData.getCountry())
                .eq(Objects.nonNull(queryData.getCategoryType()), TypeCodeManagerPo::getCategoryType, queryData.getCategoryType())
                .eq(Objects.nonNull(queryData.getEffectiveTime()), TypeCodeManagerPo::getEffectiveTime, queryData.getEffectiveTime())
                .oneOpt()
                .map(typeManagerConverter::codeToDomain)
                .orElse(null);
    }


    @Override
    public Optional<TypeCodeManger> findById(TypeCodeId id) {
        return getOptById(id.getId())
                .map(typeManagerConverter::codeToDomain)
                .stream()
                .peek(d -> d.setTypeManager(taxTypeLookup.findById(d.getTypeManager().getId()).orElse(null)))
                .peek(d -> d.setFinancialAccount(financialAccountLookup.findById(d.getFinancialAccount().getId()).orElse(null)))
                .findFirst();
    }
}
