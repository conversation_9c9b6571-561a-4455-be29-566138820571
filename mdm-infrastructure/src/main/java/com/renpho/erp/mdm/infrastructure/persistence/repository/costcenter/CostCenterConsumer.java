package com.renpho.erp.mdm.infrastructure.persistence.repository.costcenter;

import com.renpho.erp.mdm.domain.costprice.CostCenterRepository;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.messaging.Message;

import java.util.function.Consumer;

@Slf4j
@Configuration
public class CostCenterConsumer {
    /**
     * 成本中心同步ns
     */
    @Bean
    public Consumer<Message<Integer>> consumeCostCenterSyncNs(CostCenterRepository costCenterRepository) {
        return msg -> {
            CostCenterConsumer.log.info("收到成本中心同步 ns 消息, msg header: [{}]", msg.getHeaders());
            Integer id = msg.getPayload();
            CostCenterConsumer.log.info("成本中心同步 ns 消息: [{}]", id);
            costCenterRepository.syncNs(id);
        };
    }


}
