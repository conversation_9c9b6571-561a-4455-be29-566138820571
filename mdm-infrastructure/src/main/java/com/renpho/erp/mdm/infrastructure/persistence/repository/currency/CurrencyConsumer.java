package com.renpho.erp.mdm.infrastructure.persistence.repository.currency;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.renpho.erp.mdm.domain.currency.CurrencyRepository;
import com.renpho.erp.mdm.infrastructure.persistence.mapper.currency.CurrencyMapper;
import com.renpho.erp.mdm.infrastructure.persistence.po.currency.CurrencyPO;
import com.renpho.karma.cloud.mybatisplus.po.CreationPO;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.messaging.Message;

import java.util.Collection;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.function.Consumer;

@Slf4j
@Configuration
public class CurrencyConsumer {
	@Resource
	private CurrencyRepository currencyRepository;

	@Bean
	public Consumer<Message<Integer>> consumeCurrencySyncNs() {
		return msg -> {
			CurrencyConsumer.log.info("收到货币币种同步 ns 消息, msg header: [{}]", msg.getHeaders());
			Integer id = msg.getPayload();
            currencyRepository.syncNsById(id);
		};
	}

}
