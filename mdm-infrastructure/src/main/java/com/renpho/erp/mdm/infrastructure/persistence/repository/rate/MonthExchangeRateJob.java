package com.renpho.erp.mdm.infrastructure.persistence.repository.rate;

import org.springframework.context.annotation.Configuration;

import com.renpho.erp.mdm.domain.rate.MonthExchangeRateRepository;
import com.xxl.job.core.handler.annotation.XxlJob;

import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Configuration
public class MonthExchangeRateJob {
	@Resource
	private MonthExchangeRateRepository monthExchangeRateRepository;

	@SuppressWarnings("all")
	@XxlJob("MonthExchangeRatePushNsJob")
	public void job() {
		monthExchangeRateRepository.syncNsJob();
	}

}
