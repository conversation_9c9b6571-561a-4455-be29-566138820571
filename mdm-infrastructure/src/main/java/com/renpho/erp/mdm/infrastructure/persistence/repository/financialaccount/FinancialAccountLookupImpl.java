package com.renpho.erp.mdm.infrastructure.persistence.repository.financialaccount;

import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.renpho.erp.mdm.domain.common.ActiveStatus;
import com.renpho.erp.mdm.domain.common.Created;
import com.renpho.erp.mdm.domain.common.Updated;
import com.renpho.erp.mdm.domain.company.Company;
import com.renpho.erp.mdm.domain.company.CompanyId;
import com.renpho.erp.mdm.domain.company.CompanyLookup;
import com.renpho.erp.mdm.domain.currency.Currency;
import com.renpho.erp.mdm.domain.currency.CurrencyRepository;
import com.renpho.erp.mdm.domain.financialaccount.FinancialAccount;
import com.renpho.erp.mdm.domain.financialaccount.FinancialAccountCategory;
import com.renpho.erp.mdm.domain.financialaccount.FinancialAccountId;
import com.renpho.erp.mdm.domain.financialaccount.FinancialAccountLookup;
import com.renpho.erp.mdm.domain.netsuite.NsSyncStatus;
import com.renpho.erp.mdm.infrastructure.feignclient.oum.OumUserInfoConverter;
import com.renpho.erp.mdm.infrastructure.feignclient.oum.RemoteUserDetailsFeign;
import com.renpho.erp.mdm.infrastructure.persistence.mapper.financialaccount.FinancialAccountMapper;
import com.renpho.erp.mdm.infrastructure.persistence.po.financialaccount.FinancialAccountPo;
import com.renpho.erp.mdm.infrastructure.persistence.po.financialaccount.converter.FinancialAccountConverter;
import com.renpho.erp.smc.client.dto.OumUserInfoRes;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.ListUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Repository;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2024/9/26
 */
@Repository
public class FinancialAccountLookupImpl extends ServiceImpl<FinancialAccountMapper, FinancialAccountPo>
		implements FinancialAccountLookup {

	private final CompanyLookup companyLookup;

	private final CurrencyRepository currencyRepository;

	private final RemoteUserDetailsFeign remoteUserDetailsFeign;

	private final FinancialAccountConverter financialAccountConverter;

	private final OumUserInfoConverter oumUserInfoConverter;

	@Override
	public List<FinancialAccount> findAll(FinancialAccount account, Collection<Integer> excludeIds) {
		List<FinancialAccountPo> pos = ListUtils.emptyIfNull(findAllConditions(account, excludeIds).list());
		return associations(pos);
	}

	@Override
	public List<FinancialAccount> findAllWithoutAssociations(FinancialAccount account, Collection<Integer> excludeIds) {
		List<FinancialAccountPo> pos = ListUtils.emptyIfNull(findAllConditions(account, excludeIds).list());
		return pos.stream().map(financialAccountConverter::toDomain).toList();
	}

	private LambdaQueryChainWrapper<FinancialAccountPo> findAllConditions(FinancialAccount account, Collection<Integer> excludeIds) {
		Optional<FinancialAccount> optional = Optional.ofNullable(account);
		Optional<String> hasName = optional.map(FinancialAccount::getAccountName).filter(StringUtils::isNotBlank).map("%%%s%%"::formatted);
		Optional<Integer> hasStatus = optional.map(FinancialAccount::getStatus).map(ActiveStatus::getStatus);
		Optional<String> hasAccountCode = optional.map(FinancialAccount::getAccountCode);
		Optional<String> hasNsType = optional.map(FinancialAccount::getNsType);
		Optional<String> hasAccountCategory = optional.map(FinancialAccount::getAccountCategory).map(FinancialAccountCategory::getValue);
		Optional<Integer> hasNsSyncStatus = optional.map(FinancialAccount::getNsSyncStatus).map(NsSyncStatus::getValue);
		excludeIds = CollectionUtils.emptyIfNull(excludeIds);
		return lambdaQuery()
				.notIn(CollectionUtils.isNotEmpty(excludeIds), FinancialAccountPo::getId, excludeIds)
				.eq(hasStatus.isPresent(), FinancialAccountPo::getStatus, hasStatus.orElse(null))
				.eq(hasNsType.isPresent(), FinancialAccountPo::getNsType, hasNsType.orElse(null))
				.eq(hasAccountCategory.isPresent(), FinancialAccountPo::getAccountCategory, hasAccountCategory.orElse(null))
				.eq(hasNsSyncStatus.isPresent(), FinancialAccountPo::getNsSyncStatus, hasNsSyncStatus.orElse(null))
				.like(hasAccountCode.isPresent(), FinancialAccountPo::getAccountCode, hasAccountCode.orElse(null))
				.apply(hasName.isPresent(), "JSON_SEARCH(company_name -> '$[*].name', 'ONE', {0}) IS NOT NULL", hasName.orElse(null))
				;
	}

	private List<FinancialAccount> associations(List<FinancialAccountPo> pos) {
		List<FinancialAccount> list = new ArrayList<>(pos.size());
		List<CompanyId> companyIds = new ArrayList<>(pos.size());
		Set<Integer> userIds = new TreeSet<>();
		for (FinancialAccountPo po : pos) {
			FinancialAccount domain = financialAccountConverter.toDomain(po);
			domain.setChildCount(lambdaQuery().eq(FinancialAccountPo::getParentId, po.getId()).count());
			Optional.ofNullable(po.getCurrencyId())
					.map(Currency.CurrencyID::of)
					.flatMap(currencyRepository::findById)
					.ifPresent(domain::setCurrency);
			list.add(domain);
			Optional.ofNullable(domain.getCompany())
					.map(Company::getId)
					.filter(id -> id.getId() != null)
					.ifPresent(companyIds::add);
			userIds.add(po.getCreateBy());
			userIds.add(po.getUpdateBy());
		}
		Map<CompanyId, Company> companies = companyLookup.findByIds(companyIds)
				.stream()
				.collect(Collectors.toMap(Company::getId, Function.identity()));
		Map<Integer, OumUserInfoRes> users = remoteUserDetailsFeign.findByUserIds(userIds);
		for (FinancialAccount domain : list) {
			Optional.ofNullable(domain.getCompany()).map(Company::getId).map(companies::get).ifPresent(domain::setCompany);
			oumUserInfoConverter.fillCreated(users.get(domain.getCreated().getCreateBy()), domain.getCreated());
			oumUserInfoConverter.fillUpdated(users.get(domain.getUpdated().getUpdateBy()), domain.getUpdated());
		}
		return list;
	}

	@Override
	public long countAccountCode(FinancialAccountId id, String accountCode) {
		Optional<Integer> hasId = Optional.ofNullable(id).map(FinancialAccountId::getId);
		return lambdaQuery()
				.ne(hasId.isPresent(), FinancialAccountPo::getId, hasId.orElse(null))
				.eq(FinancialAccountPo::getAccountCode, accountCode)
				.count();
	}

	@Override
	public long countNames(FinancialAccountId id, String name) {
		Optional<Integer> hasId = Optional.ofNullable(id).map(FinancialAccountId::getId);
		return lambdaQuery()
				.ne(hasId.isPresent(), FinancialAccountPo::getId, hasId.orElse(null))
				.apply("JSON_CONTAINS(account_name -> '$[*].name', {0}) > 0", "\"%s\"".formatted(name))
				.count();
	}

	@Override
	public List<FinancialAccount> findAllByIdList(List<Integer> idList) {
		List<FinancialAccountPo> pos = this.lambdaQuery().in(FinancialAccountPo::getId, idList).list();
		return pos.stream().map(financialAccountConverter::toDomain).toList();
	}

	@Override
	public Optional<FinancialAccount> findById(FinancialAccountId financialAccountId) {
		return getOptById(financialAccountId.getId())
				.map(financialAccountConverter::toDomain)
				.stream()
				.peek(d -> Optional.ofNullable(d.getCompany())
						.map(Company::getId)
						.flatMap(companyLookup::findById)
						.ifPresent(d::setCompany))
				.peek(d -> Optional.ofNullable(d.getParent())
						.map(FinancialAccount::getId)
						.flatMap(this::getOptById)
						.map(financialAccountConverter::toDomain)
						.ifPresent(d::setParent))
				.peek(d -> Optional.ofNullable(d.getCurrency())
						.map(Currency::getId)
						.flatMap(currencyRepository::findById)
						.ifPresent(d::setCurrency))
				.findFirst()
				.map(d -> {
					try {
						Set<Integer> userIds = new TreeSet<>();
						Optional<Integer> created = Optional.ofNullable(d.getCreated()).map(Created::getCreateBy);
						created.ifPresent(userIds::add);
						Optional<Integer> updated = Optional.ofNullable(d.getUpdated()).map(Updated::getUpdateBy);
						updated.ifPresent(userIds::add);
						if (CollectionUtils.isNotEmpty(userIds)) {
							Map<Integer, OumUserInfoRes> users = remoteUserDetailsFeign.findByUserIds(userIds);
							created.map(users::get).map(r -> oumUserInfoConverter.fillCreated(r, d.getCreated()));
							updated.map(users::get).map(r -> oumUserInfoConverter.fillUpdated(r, d.getUpdated()));
						}
						return d;
					} catch (Exception e) {
						return d;
					}
				});
	}

	public FinancialAccountLookupImpl(CompanyLookup companyLookup,
									  CurrencyRepository currencyRepository,
									  RemoteUserDetailsFeign remoteUserDetailsFeign,
									  FinancialAccountConverter financialAccountConverter,
									  OumUserInfoConverter oumUserInfoConverter) {
		this.companyLookup = companyLookup;
        this.currencyRepository = currencyRepository;
        this.remoteUserDetailsFeign = remoteUserDetailsFeign;
		this.financialAccountConverter = financialAccountConverter;
		this.oumUserInfoConverter = oumUserInfoConverter;
	}

}
