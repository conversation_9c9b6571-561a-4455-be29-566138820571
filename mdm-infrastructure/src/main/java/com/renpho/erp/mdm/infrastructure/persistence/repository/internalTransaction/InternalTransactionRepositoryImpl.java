package com.renpho.erp.mdm.infrastructure.persistence.repository.internalTransaction;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.renpho.erp.mdm.domain.internalTransaction.InternalTransactionRepository;
import com.renpho.erp.mdm.infrastructure.persistence.mapper.internalTransaction.InternalTransactionMapper;
import com.renpho.erp.mdm.infrastructure.persistence.po.internalTransaction.InternalTransactionPO;
import org.springframework.stereotype.Repository;

/**
 * 内部交易配置
 */
@Repository
public class InternalTransactionRepositoryImpl extends ServiceImpl<InternalTransactionMapper, InternalTransactionPO>
		implements InternalTransactionRepository {

	public InternalTransactionRepositoryImpl() {
	}

}
