package com.renpho.erp.mdm.infrastructure.persistence.repository.rate;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.renpho.erp.mdm.domain.exception.DataDuplicateException;
import com.renpho.erp.mdm.domain.rate.FluctuateExchangeRate;
import com.renpho.erp.mdm.domain.rate.FluctuateExchangeRateRepository;
import com.renpho.erp.mdm.infrastructure.persistence.mapper.rate.FluctuateExchangeRateMapper;
import com.renpho.erp.mdm.infrastructure.persistence.po.rate.FluctuateExchangeRatePO;
import com.renpho.erp.mdm.infrastructure.persistence.po.transformer.FluctuateExchangeRateTransformer;
import com.renpho.erp.mdm.infrastructure.persistence.repository.rate.converter.FluctuateExchangeRatePOConverter;
import lombok.AllArgsConstructor;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;

@AllArgsConstructor
@Component
public class FluctuateExchangeRateRepositoryImpl implements FluctuateExchangeRateRepository {

    private FluctuateExchangeRateMapper fluctuateExchangeRateMapper;
    private final FluctuateExchangeRatePOConverter fluctuateExchangeRatePOConverter;

    @Override
    public List<FluctuateExchangeRate> findLatestExchangeRateList() {
        List<FluctuateExchangeRatePO> pos = fluctuateExchangeRateMapper.findLatestExchangeRateList();
        return fluctuateExchangeRatePOConverter.toDomains(pos);
    }

    // 1.数据维度：原币+目标币+创建时间确认唯一；
    @Transactional(rollbackFor = Exception.class)
    @Override
    public Integer save(final FluctuateExchangeRate fluctuateExchangeRate) {
        final List<FluctuateExchangeRatePO> fluctuateExchangeRatePOS = this.fluctuateExchangeRateMapper.selectList(Wrappers.<FluctuateExchangeRatePO>lambdaQuery()
                .eq(FluctuateExchangeRatePO::getTargetCurrencyId, fluctuateExchangeRate.getTargetCurrencyId())
                .eq(FluctuateExchangeRatePO::getOriginalCurrencyId, fluctuateExchangeRate.getOriginalCurrencyId())
                .eq(FluctuateExchangeRatePO::getEffectiveDateTime, fluctuateExchangeRate.getEffectiveDateTime()));
        if (CollectionUtils.isNotEmpty(fluctuateExchangeRatePOS)) {
            throw new DataDuplicateException("data_duplicate_exception");
        }
        final FluctuateExchangeRatePO fluctuateExchangeRatePO = FluctuateExchangeRateTransformer.INSTANCE.domain2Po(fluctuateExchangeRate);
        this.fluctuateExchangeRateMapper.insert(fluctuateExchangeRatePO);
        return fluctuateExchangeRatePO.getId();
    }

    @Override
    public List<FluctuateExchangeRate> innerLatestList(Integer originalCurrencyId, Integer targetCurrencyId, LocalDateTime effectiveDateTime) {
        List<FluctuateExchangeRatePO> pos = fluctuateExchangeRateMapper.innerLatestList(originalCurrencyId, targetCurrencyId, effectiveDateTime);
        return fluctuateExchangeRatePOConverter.toDomains(pos);
    }
}
