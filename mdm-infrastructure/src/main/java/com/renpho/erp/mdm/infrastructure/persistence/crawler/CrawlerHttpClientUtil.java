package com.renpho.erp.mdm.infrastructure.persistence.crawler;

import jakarta.annotation.Resource;
import lombok.Data;
import lombok.Getter;
import org.apache.http.HttpHost;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Random;

/**
 * 爬虫 HttpClientUtil.
 *
 * <AUTHOR>
 * @since 2024/9/13
 */
@Component
public class CrawlerHttpClientUtil {

	@Resource
	private ProxyConfigProperties proxyConfigProperties;

	@Getter
	private CloseableHttpClient httpClient;

	public CrawlerHttpClientUtil() {
		httpClient = HttpClients.createDefault();
	}

	public void setProxy(HttpGet httpGet) {
		HttpHost proxy = getProxy();
		if (proxy != null) {
			RequestConfig requestConfig = RequestConfig.custom().setProxy(proxy).build();
			httpGet.setConfig(requestConfig);
		}
	}

	public void setProxy(HttpPost httpPost) {
		HttpHost proxy = getProxy();
		if (proxy != null) {
			RequestConfig requestConfig = RequestConfig.custom().setProxy(proxy).build();
			httpPost.setConfig(requestConfig);
		}
	}

	public static void setHead(HttpPost httpPost) {
		httpPost.addHeader("Content-Type", "application/x-www-form-urlencoded;charset=utf-8");
		httpPost.setHeader("Accept", "Accept: text/plain, */*");
		httpPost.addHeader("User-Agent",
				"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/74.0.3724.8 Safari/537.36");
		httpPost.addHeader("x-amazon-user-agent", "AmazonJavascriptScratchpad/1.0 (Language=Javascript)");
		httpPost.addHeader("X-Requested-With", "XMLHttpRequest");
	}

	private HttpHost getProxy() {
		if (!proxyConfigProperties.getProxy().isEnabled()) {
			return null; // 不启用代理
		}

		List<ProxyConfigProperties.ProxyInfo> proxyPool = proxyConfigProperties.getProxy().getPool();
		if (proxyPool.isEmpty()) {
			return null; // 没有配置代理 IP 池，默认使用本机 IP
		}

		Random random = new Random();
		ProxyConfigProperties.ProxyInfo proxyInfo = proxyPool.get(random.nextInt(proxyPool.size()));
		return new HttpHost(proxyInfo.getIp(), proxyInfo.getPort());
	}

	@Data
	public static class ProxyConfig {

		private boolean enabled;

		private List<ProxyInfo> pool;

		@Data
		public static class ProxyInfo {

			private String ip;

			private int port;

		}

	}

}