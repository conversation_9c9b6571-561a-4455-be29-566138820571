package com.renpho.erp.mdm.infrastructure.persistence.repository.costcenter;

import java.util.*;
import java.util.stream.Collectors;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.cloud.stream.function.StreamBridge;
import org.springframework.messaging.support.MessageBuilder;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.support.TransactionSynchronization;
import org.springframework.transaction.support.TransactionSynchronizationManager;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.renpho.erp.mdm.domain.costprice.CostCenter;
import com.renpho.erp.mdm.domain.costprice.CostCenterRepository;
import com.renpho.erp.mdm.domain.exception.DataDuplicateException;
import com.renpho.erp.mdm.domain.exception.DataNotFoundException;
import com.renpho.erp.mdm.infrastructure.feignclient.oum.RemoteUserDetailsFeign;
import com.renpho.erp.mdm.infrastructure.persistence.mapper.costcenter.CostCenterLanguageMapper;
import com.renpho.erp.mdm.infrastructure.persistence.mapper.costcenter.CostCenterMapper;
import com.renpho.erp.mdm.infrastructure.persistence.ns.NsApiEndpointsConfig;
import com.renpho.erp.mdm.infrastructure.persistence.po.costcenter.CostCenterLanguagePO;
import com.renpho.erp.mdm.infrastructure.persistence.po.costcenter.CostCenterPO;
import com.renpho.erp.mdm.infrastructure.persistence.po.transformer.CostCenterTransformer;
import com.renpho.erp.security.util.SecurityUtils;
import com.renpho.karma.cloud.mybatisplus.po.CreationPO;
import com.renpho.karma.cloud.mybatisplus.po.StatusPO;
import com.renpho.karma.i18n.I18nMessageKit;

import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@AllArgsConstructor
@Repository
public class CostCenterRepositoryImpl extends ServiceImpl<CostCenterMapper, CostCenterPO> implements CostCenterRepository {

	private final StreamBridge streamBridgeTemplate;
	private final CostCenterLanguageMapper costCenterLanguageMapper;
	private final RemoteUserDetailsFeign remoteUserDetailsFeign;

	private void setInfo(CostCenterPO costCenter) {
		String name = costCenterLanguageMapper
			.selectList(Wrappers.<CostCenterLanguagePO> lambdaQuery()
				.select(CostCenterLanguagePO::getName)
				.eq(CostCenterLanguagePO::getCostCenterId, costCenter.getId())
				.eq(CostCenterLanguagePO::getLanguage, "zh-CN")
				.last("limit 1"))
			.stream()
			.map(CostCenterLanguagePO::getName)
			.findFirst()
			.orElse(null);

		String code = Optional.ofNullable(costCenter.getCode()).orElse(StringUtils.EMPTY);
		if (costCenter.getParentId() == 0) {
			costCenter.setNameRoute("/" + name);
			costCenter.setCodeRoute("/" + code);
		}
		else {
			CostCenterPO parentCostCenter = getById(costCenter.getParentId());
			String parentNameRoute = Optional.ofNullable(parentCostCenter.getNameRoute()).orElse(StringUtils.EMPTY);
			String parentCodeRoute = Optional.ofNullable(parentCostCenter.getCodeRoute()).orElse(StringUtils.EMPTY);
			// 使用StringBuilder来处理可能频繁的字符串拼接操作
			costCenter.setNameRoute(parentNameRoute + "/" + name);
			costCenter.setCodeRoute(parentCodeRoute + "/" + code);
		}
		costCenter.setName(name);
	}

	@Override
	public void changeStatus(CostCenter costCenter) {
		CostCenterPO costCenterPO = CostCenterTransformer.INSTANCE.domain2PO(costCenter);
		baseMapper.update(null,
				Wrappers.<CostCenterPO> lambdaUpdate()
					.set(CostCenterPO::getStatus, costCenterPO.getStatus())
					.eq(CostCenterPO::getId, costCenterPO.getId()));
	}

	@Transactional(rollbackFor = Exception.class)
	@Override
	public Integer save(CostCenter costCenter) {
		CostCenterPO costCenterPO = CostCenterTransformer.INSTANCE.domain2PO(costCenter);
		checkCode(costCenterPO);
		costCenterPO.setUpdateName(remoteUserDetailsFeign.getUserDetails(SecurityUtils.getUser().getName()).getUserInfo().getName());
		baseMapper.insert(costCenterPO);
		saveLanguages(costCenter, costCenterPO);
		return costCenterPO.getId();
	}

	@Transactional(rollbackFor = Exception.class)
	@Override
	public void update(CostCenter costCenter) {

		// 提供按钮：编辑，日志，
		// 1.编辑将某个成本中心状态置为失效，保存需校验是否子集已全部失效或名下无子集，否则提示：确认将此成本中心和所有子集成本中心的状态改成失效？
		// 用户点击确认，则将本身和名下所有子集成本中心都失效处理；
		// （失效数据重新生效，只生效本身，子集不处理；）

		CostCenterPO costCenterPO = CostCenterTransformer.INSTANCE.domain2PO(costCenter);
		checkCode(costCenterPO);

		Result result = getResult(costCenter, costCenterPO);
		// 如果有改变状态
		if (!Objects.equals(result.dbStatus(), result.webStatus())) {
			if (Objects.equals(result.webStatus(), 0)) {
				// 1-> 0 禁用
				Set<CostCenterPO> dataList = new HashSet<>();
				foreAddData(dataList, result.id());
				if (CollectionUtils.isNotEmpty(dataList)) {
					baseMapper.update(null,
							Wrappers.<CostCenterPO> lambdaUpdate()
								.set(CostCenterPO::getStatus, 0)
								.in(CostCenterPO::getId, dataList.stream().map(CostCenterPO::getId).collect(Collectors.toList())));
				}

			} // 0-> 1生效只生效本身

		}
		costCenterPO.setUpdateName(remoteUserDetailsFeign.getUserDetails(SecurityUtils.getUser().getName()).getUserInfo().getName());
		baseMapper.updateById(costCenterPO);
		costCenterLanguageMapper
			.delete(Wrappers.<CostCenterLanguagePO> lambdaQuery().eq(CostCenterLanguagePO::getCostCenterId, costCenterPO.getId()));

		saveLanguages(costCenter, costCenterPO);
	}

	private void saveLanguages(CostCenter costCenter, @NotNull CostCenterPO costCenterPO) {
		List<CostCenterLanguagePO> languages = costCenterPO.getLanguages();
		if (CollectionUtils.isNotEmpty(languages)) {
			for (CostCenterLanguagePO language : languages) {
				language.setId(null);
				language.setCostCenterId(costCenterPO.getId());
				costCenterLanguageMapper.insert(language);
			}
		}
		costCenter.setId(CostCenter.CostCenterID.of(costCenterPO.getId()));
		afterCommitPush(costCenterPO);
	}

	private @NotNull Result getResult(CostCenter costCenter, CostCenterPO costCenterPO) {
		// 查状态
		Integer id = costCenterPO.getId();
		CostCenterPO dbCostCenterPO = baseMapper.selectById(id);
		if (Objects.isNull(dbCostCenterPO)) {
			throw new DataNotFoundException();
		}
		Integer dbStatus = dbCostCenterPO.getStatus();
		Integer webStatus = costCenter.getStatus();
		return new Result(id, dbStatus, webStatus);
	}

	private void afterCommitPush(CostCenterPO costCenterPO) {
		if (costCenterPO == null) {
			return;
		}
		Integer id = costCenterPO.getId();
		if (id == null) {
			return;
		}
		TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronization() {
			@Override
			public void afterCommit() {
				if (NsApiEndpointsConfig.getNsApiEndpointsConfig()
					.getNsApiEndPoints()
					.get(NsApiEndpointsConfig.getCostCenterProfile())
					.isUseMq()) {
					streamBridgeTemplate.send("produceCostCenterSyncNs-out-0", MessageBuilder.withPayload(id).build());

				}
				else {
					syncNs(id);
				}

			}
		});
	}

	/**
	 * 编辑将某个成本中心状态置为失效，保存需校验是否子集已全部失效或名下无子集，否则提示：确认将此成本中心和所有子集成本中心的状态改成失效？
	 */
	@Override
	public String checkHasChild(CostCenter costCenter) {
		CostCenterPO costCenterPO = CostCenterTransformer.INSTANCE.domain2PO(costCenter);
		// 查状态
		Result result = getResult(costCenter, costCenterPO);
		// 如果有改变状态
		if (!Objects.equals(result.dbStatus(), result.webStatus())) {
			if (Objects.equals(result.webStatus(), 0)) {
				// 1-> 0 禁用
				Set<CostCenterPO> dataList = new HashSet<>();
				foreAddData(dataList, result.id());
				if (dataList.stream().anyMatch(item -> item.getStatus() == 1)) {
					return I18nMessageKit.getMessage("CONFIRM_INACTIVE_COST_CENTER");
				}

			} // 0-> 1生效只生效本身

		}
		return null;
	}

	@Override
	public void syncNs(Integer id) {
		CostCenterPO currencyPO = baseMapper.selectById(id);
		if (currencyPO == null) {
			return;
		}

		setInfo(currencyPO);
		CostCenterNsRequest costCenterNsRequest = new CostCenterNsRequest();
		costCenterNsRequest.setName(currencyPO.getName());
		costCenterNsRequest.setCode(currencyPO.getCode());
		costCenterNsRequest.setNameRoute(currencyPO.getNameRoute());
		costCenterNsRequest.setCodeRoute(currencyPO.getCodeRoute());
		costCenterNsRequest.setId(currencyPO.getNsId());
		CostCenterNsResponse costCenterNsResponse = NsUtils.sendRequestAndGetResult(NsApiEndpointsConfig.getCostCenterProfile(),
				costCenterNsRequest, CostCenterNsResponse.class);
		Integer nsCostCenterId = Optional.of(costCenterNsResponse)
			.map(CostCenterNsResponse::getData)
			.orElse(Collections.emptyList())
			.stream()
			.map(CostCenterNsResponse.DataBean::getNsCostCenterId)
			.findFirst()
			.orElse(null);
		if (nsCostCenterId == null) {
			// 改状态，设置id
			baseMapper.update(Wrappers.<CostCenterPO> lambdaUpdate().set(CostCenterPO::getNsStatus, 3).eq(CreationPO::getId, id));
		}
		else {
			// 改状态，设置id
			baseMapper.update(Wrappers.<CostCenterPO> lambdaUpdate()
				.set(CostCenterPO::getNsStatus, 2)
				.set(CostCenterPO::getNsId, nsCostCenterId)
				.eq(CreationPO::getId, id));
		}

	}

	@Override
	public void syncNsJob() {
		List<Integer> idList = baseMapper.selectList(Wrappers.<CostCenterPO> lambdaQuery().select(CostCenterPO::getId))
			.stream()
			.map(CostCenterPO::getId)
			.distinct()
			.toList();
		if (CollectionUtils.isEmpty(idList)) {
			return;
		}
		for (Integer id : idList) {
			try {
				syncNs(id);
			}
			catch (Exception e) {
				log.error(e.getMessage(), e);
			}
		}
	}

	private void foreAddData(Set<CostCenterPO> costCenterPOS, Integer id) {
		int sizeStart = costCenterPOS.size();
		Set<Integer> costCenterList = costCenterPOS.stream().map(CostCenterPO::getId).collect(Collectors.toSet());
		List<CostCenterPO> dataList;
		if (CollectionUtils.isEmpty(costCenterList) && Objects.isNull(id)) {
			return;
		}
		else {
			dataList = baseMapper.selectList(Wrappers.<CostCenterPO> lambdaQuery()
				.select(CostCenterPO::getId, StatusPO::getStatus)
				.in(CollectionUtils.isNotEmpty(costCenterList), CostCenterPO::getParentId, costCenterList)
				.eq(Objects.nonNull(id), CostCenterPO::getParentId, id));
		}

		if (CollectionUtils.isEmpty(dataList)) {
			return;
		}
		costCenterPOS.addAll(dataList);
		if (sizeStart == costCenterPOS.size()) {
			return;
		}

		foreAddData(costCenterPOS, null);

	}

	@Override
	public CostCenter getById(CostCenter.CostCenterID id) {
		CostCenterPO costCenterPO = baseMapper.selectById(id.getId());
		if (Objects.isNull(costCenterPO)) {
			throw new DataNotFoundException();
		}
		List<CostCenterLanguagePO> costCenterLanguagePOS = costCenterLanguageMapper
			.selectList(Wrappers.<CostCenterLanguagePO> lambdaQuery().eq(CostCenterLanguagePO::getCostCenterId, id.getId()));
		costCenterPO.setLanguages(costCenterLanguagePOS);
		return CostCenterTransformer.INSTANCE.po2Domain(costCenterPO);
	}

	/**
	 * 检查code
	 */
	private void checkCode(CostCenterPO currencyPO) {
		List<Object> exit = baseMapper.selectObjs(Wrappers.<CostCenterPO> lambdaQuery()
			.select(CostCenterPO::getId)
			.ne(Objects.nonNull(currencyPO.getId()), CostCenterPO::getId, currencyPO.getId())
			.eq(CostCenterPO::getCode, currencyPO.getCode())
			.last("limit 1"));
		if (CollectionUtils.isNotEmpty(exit)) {
			String message = I18nMessageKit.getMessage("cost_center_code");
			throw new DataDuplicateException(message);
		}
	}

	@Override
	public Optional<CostCenter> findById(CostCenter.CostCenterID costCenterID) {
		return Optional.empty();
	}

	private record Result(Integer id, Integer dbStatus, Integer webStatus) {
	}

}
