package com.renpho.erp.mdm.infrastructure.persistence.crawler.optlog;

import com.alibaba.fastjson.JSONObject;
import com.renpho.erp.mdm.infrastructure.persistence.po.rate.MonthExchangeRatePO;
import com.renpho.erp.oplog.log.SnapshotDatatSource;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @since 2024/9/24
 */
@Component("monthExchangeRateAddSnapSourceInfrastructure")
public class MonthExchangeRateAddSnapSource implements SnapshotDatatSource {


    @Override
    public JSONObject getOldData(Object[] args) {
        return new JSONObject();
    }

    @Override
    public JSONObject getNewData(Object[] args, JSONObject result) {
        return result;
    }

    @Override
    public String getBsId(Object[] args, JSONObject result) {
        return String.valueOf(((MonthExchangeRatePO) args[1]).getId());
    }

}
