package com.renpho.erp.mdm.infrastructure.persistence.repository.saleschannel;

import com.baomidou.mybatisplus.core.toolkit.support.SFunction;
import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.renpho.erp.mdm.domain.common.ActiveStatus;
import com.renpho.erp.mdm.domain.saleschannel.*;
import com.renpho.erp.mdm.infrastructure.feignclient.oum.OumUserInfoConverter;
import com.renpho.erp.mdm.infrastructure.feignclient.oum.RemoteUserDetailsFeign;
import com.renpho.erp.mdm.infrastructure.persistence.mapper.saleschannel.SalesChannelMapper;
import com.renpho.erp.mdm.infrastructure.persistence.po.saleschannel.SalesChannelPo;
import com.renpho.erp.mdm.infrastructure.persistence.po.saleschannel.converter.SalesChannelConverter;
import com.renpho.erp.smc.client.dto.OumUserInfoRes;
import com.renpho.karma.dto.PageQuery;
import com.renpho.karma.dto.Paging;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.ListUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Repository;

import java.util.*;
import java.util.function.Function;
import java.util.function.Predicate;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2024/9/24
 */
@Repository
public class SalesChannelLookupImpl extends ServiceImpl<SalesChannelMapper, SalesChannelPo> implements SalesChannelLookup {

	private final SalesChannelSiteRepository salesChannelSiteRepository;

	private final RemoteUserDetailsFeign remoteUserDetailsFeign;

	private final SalesChannelConverter salesChannelConverter;

	private final OumUserInfoConverter oumUserInfoConverter;

	@Override
	public Paging<SalesChannel> findPage(SalesChannel root, PageQuery pageQuery) {
		List<SiteId> hasSiteIds = Optional.ofNullable(root).map(SalesChannel::getAvailableSiteIds)
				.filter(CollectionUtils::isNotEmpty)
				.stream()
				.flatMap(Collection::stream)
				.map(SiteId::new)
				.toList();
		Set<Integer> ids = Set.of();
		if (CollectionUtils.isNotEmpty(hasSiteIds)) {
			Map<SiteId, List<SalesChannelId>> channelIdBySiteIds = salesChannelSiteRepository.findChannelIdBySiteIds(hasSiteIds);
			ids = channelIdBySiteIds.values()
					.stream()
					.flatMap(Collection::stream)
					.filter(Objects::nonNull)
					.map(SalesChannelId::getId)
					.filter(Objects::nonNull)
					.collect(Collectors.toCollection(TreeSet::new));
			if (CollectionUtils.isEmpty(ids)) {
				return Paging.of(pageQuery.getPageSize(), pageQuery.getPageIndex());
			}
		}

		Page<SalesChannelPo> page = findAllConditions(root, ids)
				.orderByDesc(SalesChannelPo::getUpdateTime)
				.page(Page.of(pageQuery.getPageIndex(), pageQuery.getPageSize()));
		Paging<SalesChannel> paging = Paging.of((int) page.getSize(), (int) page.getCurrent());
		List<SalesChannelPo> records = page.getRecords();
		if (CollectionUtils.isEmpty(records)) {
			paging.setRecords(List.of());
			return paging;
		}

		List<SalesChannel> salesChannels = associations(records);
		paging.setRecords(salesChannels);
		paging.setTotalCount((int) page.getTotal());
		return paging;
	}

	@Override
	public List<SalesChannel> findAll(SalesChannel root) {
		List<SiteId> hasSiteIds = Optional.ofNullable(root).map(SalesChannel::getAvailableSiteIds)
				.filter(CollectionUtils::isNotEmpty)
				.stream()
				.flatMap(Collection::stream)
				.map(SiteId::new)
				.toList();
		Set<Integer> ids = Set.of();
		if (CollectionUtils.isNotEmpty(hasSiteIds)) {
			Map<SiteId, List<SalesChannelId>> channelIdBySiteIds = salesChannelSiteRepository.findChannelIdBySiteIds(hasSiteIds);
			ids = channelIdBySiteIds.values()
					.stream()
					.flatMap(Collection::stream)
					.filter(Objects::nonNull)
					.map(SalesChannelId::getId)
					.filter(Objects::nonNull)
					.collect(Collectors.toCollection(TreeSet::new));
			if (CollectionUtils.isEmpty(ids)) {
				return List.of();
			}
		}
		List<SalesChannelPo> records = findAllConditions(root, ids).list();
		return associations(records);
	}

	@Override
	public List<SalesChannel> findAll() {
		return findAllConditions(null, List.of()).list().stream().map(salesChannelConverter::toDomain).toList();
	}

	@Override
	public Map<SalesChannelId, SalesChannel> findByIds(Collection<SalesChannelId> ids) {
		if (CollectionUtils.isEmpty(ids)) {
			return Map.of();
		}
		Set<Integer> set = ids.stream()
				.filter(Objects::nonNull)
				.map(SalesChannelId::getId)
				.filter(Objects::nonNull)
				.collect(Collectors.toCollection(TreeSet::new));
		List<SalesChannelPo> pos = lambdaQuery()
				.in(CollectionUtils.isNotEmpty(set), SalesChannelPo::getId, set)
				.list();
		List<SalesChannel> salesChannels = associations(pos);
		return salesChannels.stream().collect(Collectors.toMap(SalesChannel::getId, Function.identity()));
	}

	@Override
	public List<SalesChannel> findByNames(Collection<String> names) {
		if (CollectionUtils.isEmpty(names)) {
			return List.of();
		}
		return findAllByValues(SalesChannelPo::getChannelName, names, StringUtils::isNotBlank);
	}

	@Override
	public List<SalesChannel> findByCodes(Collection<String> codes) {
		if (CollectionUtils.isEmpty(codes)) {
			return List.of();
		}
		return findAllByValues(SalesChannelPo::getChannelCode, codes, StringUtils::isNotBlank);
	}

	private <T> List<SalesChannel> findAllByValues(SFunction<SalesChannelPo, T> column, Collection<T> condition, Predicate<T> filter) {
		Set<T> in = condition.stream().filter(filter).collect(Collectors.toSet());
		if (CollectionUtils.isEmpty(in)) {
			return List.of();
		}

		List<SalesChannelPo> pos = lambdaQuery()
				.in(column, in)
				.list();
		return associations(pos);
	}


	private LambdaQueryChainWrapper<SalesChannelPo> findAllConditions(SalesChannel root, Collection<Integer> ids) {
		Optional<SalesChannel> optional = Optional.ofNullable(root);
		Optional<String> hasName = optional.map(SalesChannel::getChannelName).filter(StringUtils::isNotBlank);
		Optional<Integer> hasStatus = optional.map(SalesChannel::getStatus).map(ActiveStatus::getStatus);
		return lambdaQuery()
				.in(CollectionUtils.isNotEmpty(ids), SalesChannelPo::getId, ids)
				.like(hasName.isPresent(), SalesChannelPo::getChannelName, hasName.orElse(null))
				.eq(hasStatus.isPresent(), SalesChannelPo::getStatus, hasStatus.orElse(null));
	}

	@Override
	public long countNames(SalesChannelId id, String channelName) {
		Optional<Integer> hasId = Optional.ofNullable(id).map(SalesChannelId::getId);
		return lambdaQuery()
				.eq(SalesChannelPo::getChannelName, channelName)
				.ne(hasId.isPresent(), SalesChannelPo::getId, hasId.orElse(null))
				.count();
	}

	@Override
	public long countCodes(SalesChannelId id, String channelCode) {
		Optional<Integer> hasId = Optional.ofNullable(id).map(SalesChannelId::getId);
		return lambdaQuery()
				.eq(SalesChannelPo::getChannelCode, channelCode)
				.ne(hasId.isPresent(), SalesChannelPo::getId, hasId.orElse(null))
				.count();
	}

	@Override
	public List<SalesChannel> findAll(SalesChannelQuery query) {
		Optional<SalesChannelQuery> optional = Optional.ofNullable(query);
		Optional<Integer> hasChannelId = optional.map(SalesChannelQuery::getChannelId).map(SalesChannelId::getId);
		Set<Integer> hasChannelIds = optional.map(SalesChannelQuery::getChannelIds).stream().flatMap(Collection::stream).filter(Objects::nonNull).map(SalesChannelId::getId).filter(Objects::nonNull).collect(Collectors.toSet());
		Optional<String> hasChannelCode = optional.map(SalesChannelQuery::getChannelCode).filter(StringUtils::isNotBlank);
		Set<String> hasChannelCodes = optional.map(SalesChannelQuery::getChannelCodes).stream().flatMap(Collection::stream).filter(StringUtils::isNotBlank).collect(Collectors.toSet());
		Optional<Integer> hasStatus = optional.map(SalesChannelQuery::getStatus).map(ActiveStatus::getStatus);

		List<SalesChannelPo> pos = lambdaQuery()
				.eq(hasChannelId.isPresent(), SalesChannelPo::getId, hasChannelId.orElse(null))
				.eq(hasChannelCode.isPresent(), SalesChannelPo::getChannelCode, hasChannelCode.orElse(null))
				.in(hasStatus.isPresent(), SalesChannelPo::getStatus, hasStatus.orElse(null))
				.in(CollectionUtils.isNotEmpty(hasChannelIds), SalesChannelPo::getId, hasChannelIds)
				.in(CollectionUtils.isNotEmpty(hasChannelCodes), SalesChannelPo::getChannelCode, hasChannelCodes)
				.list();
		return associations(pos);
	}

	private List<SalesChannel> associations(List<SalesChannelPo> records) {
		List<SalesChannel> salesChannels = new ArrayList<>(records.size());
		List<SalesChannelId> salesChannelIds = new ArrayList<>(records.size());
		Set<Integer> userIds = new HashSet<>();
		for (SalesChannelPo record : records) {
			SalesChannel domain = salesChannelConverter.toDomain(record);
			salesChannels.add(domain);
			salesChannelIds.add(domain.getId());
			userIds.add(record.getCreateBy());
			userIds.add(record.getUpdateBy());
		}

		Map<SalesChannelId, List<SalesChannelSite>> sites = salesChannelSiteRepository.findBySalesChannelIds(salesChannelIds);
		Map<Integer, OumUserInfoRes> users = remoteUserDetailsFeign.findByUserIds(userIds);
		for (SalesChannel salesChannel : salesChannels) {
			List<SalesChannelSite> availableSites = ListUtils.emptyIfNull(sites.get(salesChannel.getId()));
			salesChannel.setAvailableSites(availableSites);
			salesChannel.setAvailableSiteIds(availableSites.stream().map(SalesChannelSite::getId).map(SiteId::getId).toList());
			oumUserInfoConverter.fillCreated(users.get(salesChannel.getCreated().getCreateBy()), salesChannel.getCreated());
			oumUserInfoConverter.fillUpdated(users.get(salesChannel.getUpdated().getUpdateBy()), salesChannel.getUpdated());
		}
		return salesChannels;
	}

	@Override
	public Optional<SalesChannel> findById(SalesChannelId id) {
		SalesChannelPo po = getById(id.getId());
		SalesChannel salesChannel = salesChannelConverter.toDomain(po);
		List<SalesChannelSite> sites = salesChannelSiteRepository.findBySalesChannelId(id);
		salesChannel.setAvailableSiteIds(sites.stream().map(SalesChannelSite::getId).map(SiteId::getId).toList());
		salesChannel.setAvailableSites(sites);
		return Optional.of(salesChannel);
	}

	public SalesChannelLookupImpl(SalesChannelSiteRepository salesChannelSiteRepository,
								  RemoteUserDetailsFeign remoteUserDetailsFeign,
								  SalesChannelConverter salesChannelConverter,
								  OumUserInfoConverter oumUserInfoConverter) {
		this.salesChannelSiteRepository = salesChannelSiteRepository;
		this.remoteUserDetailsFeign = remoteUserDetailsFeign;
		this.salesChannelConverter = salesChannelConverter;
		this.oumUserInfoConverter = oumUserInfoConverter;
	}

}
