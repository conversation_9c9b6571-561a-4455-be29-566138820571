package com.renpho.erp.mdm.infrastructure.persistence.repository.mq;

import com.renpho.erp.mdm.domain.currency.CurrencyRepository;
import com.renpho.erp.mdm.domain.rate.MonthExchangeRateRepository;
import com.renpho.erp.mdm.infrastructure.persistence.po.currency.CurrencyPO;
import jakarta.annotation.Resource;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.stereotype.Repository;

import java.util.function.Consumer;

/**
 * 消费NS消息，推送给NS
 *
 * <AUTHOR>
 * @date 2024/09/30
 */
@Repository
@RequiredArgsConstructor
@Configuration
@Slf4j
public class MdmNsRecordConsumer {
    @Resource
    private CurrencyRepository currencyRepository;
    @Resource
    private MonthExchangeRateRepository monthExchangeRateRepository;

    /**
     * 币种推送ns
     * @return res
     */
    @Bean
    public Consumer<CurrencyPO> pushCurrencyToNsConsumer() {
        return msgData -> {
            log.info("@pushCurrencyToNsConsumer: {}", msgData.toString());
            Integer id = msgData.getId();
            try {
                currencyRepository.syncNsById(id);
            } catch (Exception ex) {
                log.error("异常", ex);
            }
        };
    }


    /**
     * 月度汇率推送ns
     * @return res
     */
    @Bean
    public Consumer<CurrencyPO> pushRateToNsConsumer() {
        return msgData -> {
            log.info("@pushRateToNsConsumer: {}", msgData.toString());
            Integer id = msgData.getId();
            try {
                monthExchangeRateRepository.syncNs(id);
            } catch (Exception ex) {
                log.error("异常", ex);
            }
        };
    }

}