package com.renpho.erp.mdm.infrastructure.persistence.repository.financialaccount;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.renpho.erp.mdm.domain.common.ActiveStatus;
import com.renpho.erp.mdm.domain.company.CompanyLookup;
import com.renpho.erp.mdm.domain.currency.CurrencyRepository;
import com.renpho.erp.mdm.domain.exception.DataNotFoundException;
import com.renpho.erp.mdm.domain.financialaccount.FinancialAccount;
import com.renpho.erp.mdm.domain.financialaccount.FinancialAccountId;
import com.renpho.erp.mdm.domain.financialaccount.FinancialAccountLookup;
import com.renpho.erp.mdm.domain.financialaccount.FinancialAccountRepository;
import com.renpho.erp.mdm.infrastructure.persistence.mapper.financialaccount.FinancialAccountMapper;
import com.renpho.erp.mdm.infrastructure.persistence.po.financialaccount.FinancialAccountPo;
import com.renpho.erp.mdm.infrastructure.persistence.po.financialaccount.converter.FinancialAccountConverter;
import com.renpho.erp.security.util.SecurityUtils;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import java.util.Optional;

/**
 * 财务科目信息 Repository.
 *
 * <AUTHOR>
 * @since 2024/9/26
 */
@Repository
public class FinancialAccountRepositoryImpl extends ServiceImpl<FinancialAccountMapper, FinancialAccountPo>
		implements FinancialAccountRepository {

	private final FinancialAccountLookup financialAccountLookup;
	private final CompanyLookup companyLookup;
	private final CurrencyRepository currencyRepository;
	private final FinancialAccountConverter financialAccountConverter;

	@Override
	@Transactional(rollbackFor = Exception.class)
	public FinancialAccount add(FinancialAccount root) {
		root.assertNameNotDuplicate(financialAccountLookup);
		root.assertNsType();
		root.assertIsCurrencyRevaluation();
		root.assertCompany(companyLookup);
		root.assertCurrency(currencyRepository);
		root.assertParent(financialAccountLookup);
		FinancialAccountPo po = financialAccountConverter.toPo(root);
		save(po);
		return financialAccountLookup.findById(new FinancialAccountId(po.getId())).orElse(null);
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public FinancialAccount update(FinancialAccount root) {
        assertExist(root.getId());
		root.assertNameNotDuplicate(financialAccountLookup);
		root.assertNsType();
		root.assertIsCurrencyRevaluation();
		root.assertCompany(companyLookup);
		root.assertCurrency(currencyRepository);
		root.assertParent(financialAccountLookup);
		FinancialAccountPo po = financialAccountConverter.toPo(root);
		updateById(po);
		return financialAccountLookup.findById(new FinancialAccountId(po.getId())).orElse(null);
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public FinancialAccount switchStatus(FinancialAccountId id, ActiveStatus status) {
        FinancialAccountPo po = assertExist(id);
        po.setStatus(status.getStatus());
        updateById(po);
        return financialAccountConverter.toDomain(po);
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public FinancialAccount updateNsId(FinancialAccount root) {
        assertExist(root.getId());
		Optional<Integer> updated = Optional.ofNullable(SecurityUtils.getUserId());
		lambdaUpdate().eq(FinancialAccountPo::getId, root.getId().getId())
				.set(FinancialAccountPo::getNsSyncStatus, root.getNsSyncStatus().getValue())
				.set(FinancialAccountPo::getNsId, root.getNsId())
				.set(updated.isPresent(), FinancialAccountPo::getUpdateBy, updated.orElse(null))
				.update();
		return financialAccountLookup.findById(root.getId()).orElse(new FinancialAccount(new FinancialAccountId(null)));
    }

    private FinancialAccountPo assertExist(FinancialAccountId id) {
        FinancialAccountPo po = getById(id.getId());
        if (po == null) {
            throw new DataNotFoundException();
        }
        return po;
	}

	public FinancialAccountRepositoryImpl(FinancialAccountLookup financialAccountLookup,
										  FinancialAccountConverter financialAccountConverter, CompanyLookup companyLookup, CurrencyRepository currencyRepository) {
		this.financialAccountLookup = financialAccountLookup;
		this.financialAccountConverter = financialAccountConverter;
		this.companyLookup = companyLookup;
		this.currencyRepository = currencyRepository;
	}

}
