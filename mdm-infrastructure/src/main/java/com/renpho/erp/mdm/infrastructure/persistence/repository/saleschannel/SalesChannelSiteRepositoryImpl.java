package com.renpho.erp.mdm.infrastructure.persistence.repository.saleschannel;

import com.baomidou.mybatisplus.core.enums.SqlMethod;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.renpho.erp.mdm.domain.saleschannel.*;
import com.renpho.erp.mdm.infrastructure.feignclient.pds.RemoteCountryRegionFeign;
import com.renpho.erp.mdm.infrastructure.persistence.mapper.saleschannel.SalesChannelSiteMapper;
import com.renpho.erp.mdm.infrastructure.persistence.po.saleschannel.SalesChannelSitePo;
import com.renpho.erp.mdm.infrastructure.persistence.po.saleschannel.converter.SalesChannelSiteConverter;
import com.renpho.erp.pds.client.vo.PdsCountryRegionVo;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.ListUtils;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2024/9/19
 */
@Repository
public class SalesChannelSiteRepositoryImpl extends ServiceImpl<SalesChannelSiteMapper, SalesChannelSitePo> implements SalesChannelSiteRepository {

	private final SalesChannelSiteConverter salesChannelSiteConverter;

	private final RemoteCountryRegionFeign remoteCountryRegionFeign;

	@Override
	public List<Integer> findIdBySalesChannelId(SalesChannelId salesChannelId) {
		if (Optional.ofNullable(salesChannelId).map(SalesChannelId::getId).isEmpty()) {
			return List.of();
		}
		List<SalesChannelSitePo> pos = lambdaQuery()
				.eq(SalesChannelSitePo::getChannelId, salesChannelId.getId())
				.list();
		return ListUtils.emptyIfNull(pos)
				.stream()
				.map(SalesChannelSitePo::getSiteId)
				.toList();
	}

	@Override
	public List<SalesChannelSite> findBySalesChannelId(SalesChannelId salesChannelId) {
		List<Integer> ids = findIdBySalesChannelId(salesChannelId);
		return remoteCountryRegionFeign.findByIds(ids).values()
				.stream()
				.map(salesChannelSiteConverter::toDomain)
				.toList();
	}

	@Override
	public Map<SalesChannelId, List<Integer>> findIdsBySalesChannelIds(Collection<SalesChannelId> salesChannelIds) {
		List<Integer> ids = unboxSalesChannelIds(salesChannelIds);
		if (CollectionUtils.isEmpty(ids)) {
			return Map.of();
		}

		List<SalesChannelSitePo> pos = lambdaQuery()
				.in(SalesChannelSitePo::getChannelId, ids)
				.list();

		Map<SalesChannelId, List<Integer>> groupByChannelId = new HashMap<>();
		for (SalesChannelSitePo po : ListUtils.emptyIfNull(pos)) {
			SalesChannelId id = new SalesChannelId(po.getChannelId());
			groupByChannelId.computeIfAbsent(id, i -> new ArrayList<>()).add(po.getSiteId());
		}
		return groupByChannelId;
	}

	@Override
	public Map<SalesChannelId, List<SalesChannelSite>> findBySalesChannelIds(Collection<SalesChannelId> salesChannelIds) {
		Map<SalesChannelId, List<Integer>> siteGroupBySalesChannel = findIdsBySalesChannelIds(salesChannelIds);
		Set<Integer> siteIds = siteGroupBySalesChannel.values().stream()
				.flatMap(Collection::stream)
				.collect(Collectors.toSet());
		Map<Integer, PdsCountryRegionVo> sites = remoteCountryRegionFeign.findByIds(siteIds);

		Map<SalesChannelId, List<SalesChannelSite>> map = new HashMap<>(siteGroupBySalesChannel.size());
		for (Map.Entry<SalesChannelId, List<Integer>> entry : siteGroupBySalesChannel.entrySet()) {
			List<SalesChannelSite> list = ListUtils.emptyIfNull(entry.getValue()).stream()
					.filter(sites::containsKey)
					.map(sites::get)
					.map(salesChannelSiteConverter::toDomain)
					.toList();
			map.put(entry.getKey(), list);
		}
		return map;
	}

    @Override
    public Map<SiteId, List<SalesChannelId>> findChannelIdBySiteIds(Collection<SiteId> siteIds) {
		Set<Integer> ids = siteIds.stream()
				.filter(Objects::nonNull)
				.map(SiteId::getId)
				.filter(Objects::nonNull)
				.collect(Collectors.toCollection(TreeSet::new));

		if (CollectionUtils.isEmpty(ids)) {
			return Map.of();
		}
		List<SalesChannelSitePo> pos = lambdaQuery()
				.in(SalesChannelSitePo::getSiteId, ids)
				.list();
		if (CollectionUtils.isEmpty(pos)) {
			return Map.of();
		}
		Map<SiteId, List<SalesChannelId>> results = new HashMap<>();
		for (SalesChannelSitePo po : pos) {
			SiteId siteId = new SiteId(po.getSiteId());
			List<SalesChannelId> channels = results.computeIfAbsent(siteId, k -> new ArrayList<>());
			channels.add(new SalesChannelId(po.getChannelId()));
		}
		return results;
    }

    private List<Integer> unboxSalesChannelIds(Collection<SalesChannelId> salesChannelIds) {
		return Optional.ofNullable(salesChannelIds)
				.stream()
				.flatMap(Collection::stream)
				.filter(Objects::nonNull)
				.map(SalesChannelId::getId)
				.sorted()
				.toList();
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public void saveBatch(SalesChannelId salesChannelId, Collection<SalesChannelSite> sites) {
		lambdaUpdate().eq(SalesChannelSitePo::getChannelId, salesChannelId.getId()).remove();
		if (CollectionUtils.isNotEmpty(sites)) {
			List<SalesChannelSitePo> pos = sites.stream().map(domain -> salesChannelSiteConverter.toPo(salesChannelId, domain)).toList();
			String sqlStatement = getSqlStatement(SqlMethod.INSERT_ONE);
			executeBatch(pos, DEFAULT_BATCH_SIZE, (sqlSession, entity) -> sqlSession.insert(sqlStatement, entity));
		}
	}

	public SalesChannelSiteRepositoryImpl(SalesChannelSiteConverter salesChannelSiteConverter, RemoteCountryRegionFeign remoteCountryRegionFeign) {
		this.salesChannelSiteConverter = salesChannelSiteConverter;
		this.remoteCountryRegionFeign = remoteCountryRegionFeign;
	}

}
