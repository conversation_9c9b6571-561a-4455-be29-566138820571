package com.renpho.erp.mdm.infrastructure.persistence.mapper.store;

import java.util.List;

import org.apache.ibatis.annotations.Param;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.renpho.erp.data.permission.aop.annotation.DataPermission;
import com.renpho.erp.mdm.infrastructure.persistence.expression.StoreExpressionCondition;
import com.renpho.erp.mdm.infrastructure.persistence.po.store.StorePo;

/**
 * 店铺信息 Mapper
 *
 * <AUTHOR>
 * @since 2024/9/14
 */
public interface StoreMapper extends BaseMapper<StorePo> {

	/**
	 * 获取店铺货主列表.
	 * @param channelId 销售渠道ID
	 * @return List<StorePo>
	 */
	List<StorePo> selectStoreCompanyList(@Param("channelId") Integer channelId);

	/**
	 * 获取许可的店铺列表.
	 * @param channelId 销售渠道ID
	 * @return List<StorePo>
	 */
	@DataPermission(condition = StoreExpressionCondition.class)
	List<StorePo> selectPermissionStoreList(@Param("channelId") Integer channelId);

	/**
	 * 分页查询许可的店铺
	 * @param page 分页参数
	 * @param queryWrapper 查询条件
	 * @return 分页结果
	 * @param <P> 分页结果泛型
	 */
	@DataPermission(condition = StoreExpressionCondition.class)
	default <P extends IPage<StorePo>> P selectPermissionPage(P page, Wrapper<StorePo> queryWrapper) {
		return BaseMapper.super.selectPage(page, queryWrapper);
	}
}
