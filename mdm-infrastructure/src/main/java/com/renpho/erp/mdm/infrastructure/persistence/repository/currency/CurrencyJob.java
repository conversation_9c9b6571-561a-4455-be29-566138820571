package com.renpho.erp.mdm.infrastructure.persistence.repository.currency;

import com.renpho.erp.mdm.domain.currency.CurrencyRepository;
import com.xxl.job.core.handler.annotation.XxlJob;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Configuration;

@Slf4j
@Configuration
public class CurrencyJob {
    @Resource
    private CurrencyRepository currencyRepository;

    @SuppressWarnings("all")
    @XxlJob("CurrencyPushNsJob")
    public void job() {
        currencyRepository.syncNsJob();
    }

}
