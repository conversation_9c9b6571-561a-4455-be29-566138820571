package com.renpho.erp.mdm.infrastructure.persistence.mapper.store;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.renpho.erp.data.permission.aop.annotation.DataPermission;
import com.renpho.erp.mdm.infrastructure.persistence.po.store.StorePermissionPo;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;

import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 店铺权限表 Mapper 接口
 *
 * <AUTHOR>
 * @since 2025-03-17
 */
public interface StorePermissionMapper extends BaseMapper<StorePermissionPo> {

    /**
     * 获取店铺权限
     * @param storeId 店铺ID
     * @param userId 用户ID
     * @return List
     */
    default StorePermissionPo getByStoreIdAndUserId(Integer storeId, Integer userId) {
        return this.selectOne(new LambdaQueryWrapper<StorePermissionPo>()
                .eq(StorePermissionPo::getStoreId, storeId)
                .eq(StorePermissionPo::getUserId, userId));
    }


    /**
     * 获取店铺权限
     * @param storeIds 店铺ID集
     * @param userIds 用户ID集
     * @return List
     */
    default List<StorePermissionPo> getByStoreIdsAndUserIds(Set<Integer> storeIds, Set<Integer> userIds) {
        return this.selectList(new LambdaQueryWrapper<StorePermissionPo>()
                .in(StorePermissionPo::getStoreId, storeIds)
                .in(StorePermissionPo::getUserId, userIds));
    }

    /**
     * 获取店铺权限
     * @param storeId 店铺ID
     */
    default List<StorePermissionPo> getByStoreId(Integer storeId) {
        return this.selectList(new LambdaQueryWrapper<StorePermissionPo>()
                .eq(StorePermissionPo::getStoreId, storeId)
                .orderByDesc(StorePermissionPo::getId));
    }

    /**
     * 获取用户店铺权限Map，key为用户id，value为店铺id集合
     *
     * @param userIds 用户ID集
     * @DataPermission(required = false)，上层调用的方法如果标注数据权限注解，会导致此方法也加入了mybatis拦截，这里指定required为false，避免受干扰
     */
    @DataPermission(required = false)
    default Map<Integer, Set<Integer>> getByUserIds(Set<Integer> userIds) {
        List<StorePermissionPo> permissionList = this.selectList(new LambdaQueryWrapper<StorePermissionPo>()
                .select(StorePermissionPo::getUserId, StorePermissionPo::getStoreId)
                .in(StorePermissionPo::getUserId, userIds));
        // 用户店铺权限Map，key为用户id，value为店铺id集合
        return permissionList.stream().collect(Collectors.groupingBy(StorePermissionPo::getUserId,
                Collectors.mapping(StorePermissionPo::getStoreId, Collectors.toSet())));
    }

}
