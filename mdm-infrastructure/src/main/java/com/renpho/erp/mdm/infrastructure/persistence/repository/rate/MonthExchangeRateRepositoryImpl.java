package com.renpho.erp.mdm.infrastructure.persistence.repository.rate;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.lang.Tuple;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.renpho.erp.mdm.domain.exception.BusinessException;
import com.renpho.erp.mdm.domain.rate.MonthExchangeRate;
import com.renpho.erp.mdm.domain.rate.MonthExchangeRateRepository;
import com.renpho.erp.mdm.infrastructure.feignclient.oum.RemoteUserDetailsFeign;
import com.renpho.erp.mdm.infrastructure.persistence.mapper.rate.MonthExchangeRateMapper;
import com.renpho.erp.mdm.infrastructure.persistence.ns.NsApiEndpointsConfig;
import com.renpho.erp.mdm.infrastructure.persistence.po.rate.MonthExchangeRatePO;
import com.renpho.erp.mdm.infrastructure.persistence.po.transformer.MonthExchangeRateTransformer;
import com.renpho.erp.mdm.infrastructure.persistence.repository.costcenter.NsUtils;
import com.renpho.erp.security.util.SecurityUtils;
import com.renpho.karma.cloud.mybatisplus.po.CreationPO;
import com.renpho.karma.i18n.I18nMessageKit;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.lang.Tuple;
import lombok.AllArgsConstructor;
import lombok.Getter;
import org.apache.commons.collections4.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cloud.stream.function.StreamBridge;
import org.springframework.integration.support.MessageBuilder;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.support.TransactionSynchronization;
import org.springframework.transaction.support.TransactionSynchronizationManager;

import java.time.*;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

/**
 * 货币仓储实现.
 *
 * <AUTHOR>
 */
@AllArgsConstructor
@Repository
public class MonthExchangeRateRepositoryImpl implements MonthExchangeRateRepository {

    private static final Logger log = LoggerFactory.getLogger(MonthExchangeRateRepositoryImpl.class);
    private final MonthExchangeRateMapper monthExchangeRateMapper;
    private final StreamBridge streamBridgeTemplate;
    private final RemoteUserDetailsFeign remoteUserDetailsFeign;

    private static LocalDateTime startOfDay(LocalDate date) {
        return LocalDateTime.of(date, LocalTime.MIN);
    }

    private static LocalDateTime endOfDay(LocalDate date) {
        return LocalDateTime.of(date, LocalTime.MAX);
    }

    private static boolean sendMqPushNs(MonthExchangeRateNsRequest monthExchangeRateNsRequest) {
        MonthExchangeRateNsResponse currencyNsResponse = NsUtils.sendRequestAndGetResult(NsApiEndpointsConfig.getMonthExchangeRateProfile(), monthExchangeRateNsRequest, MonthExchangeRateNsResponse.class);
        String code = currencyNsResponse.getCode();
        boolean success = currencyNsResponse.isSuccess();
        if (Objects.equals(code, "200") && success) {
            return true;
        }
        return false;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public Integer save(MonthExchangeRate monthExchangeRate) {
        checkDate(monthExchangeRate, true);
        MonthExchangeRatePO fluctuateExchangeRatePO = MonthExchangeRateTransformer.INSTANCE.domain2Po(monthExchangeRate);
        fluctuateExchangeRatePO.setUpdateName(remoteUserDetailsFeign.getUserDetails(SecurityUtils.getUser().getName()).getUserInfo().getName());
        monthExchangeRateMapper.insert(fluctuateExchangeRatePO);

        afterCommitPush(fluctuateExchangeRatePO);
        return fluctuateExchangeRatePO.getId();
    }

    private void afterCommitPush(MonthExchangeRatePO monthExchangeRatePO) {
        Integer id = monthExchangeRatePO.getId();
        if (id == null) {
            return;
        }
        TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronization() {
            @Override
            public void afterCommit() {
                sendMqPushNs(id);
            }
        });
    }

    @Override
    public void sendMqPushNs(Integer id) {

        if (NsApiEndpointsConfig.getNsApiEndpointsConfig().getNsApiEndPoints().get(NsApiEndpointsConfig.getMonthExchangeRateProfile()).isUseMq()) {
            boolean send = streamBridgeTemplate.send("produceMonthExchangeRateSyncNS-out-0", MessageBuilder.withPayload(id).build());
            System.out.println(send);
        } else {
            syncNs(id);
        }
    }

    private Tuple checkDate(MonthExchangeRate monthExchangeRate, boolean isThrowExpetion) {
        LocalDateTime effectiveDateTime = monthExchangeRate.getEffectiveDateTime();
        LambdaQueryWrapper<MonthExchangeRatePO> queryWrapper = Wrappers.<MonthExchangeRatePO>lambdaQuery()
                .ne(Objects.nonNull(monthExchangeRate.getId()), MonthExchangeRatePO::getId, monthExchangeRate.getId())
                .and(a -> a.eq(MonthExchangeRatePO::getTargetCurrencyId, monthExchangeRate.getTargetCurrencyId())
                        .eq(MonthExchangeRatePO::getOriginalCurrencyId, monthExchangeRate.getOriginalCurrencyId()))
                .ge(Objects.nonNull(effectiveDateTime), MonthExchangeRatePO::getEffectiveDateTime, Optional.ofNullable(effectiveDateTime)
                        .map(s -> DateUtil.format(effectiveDateTime, "yyyy-MM-dd HH:mm:ss")).orElse(null));
        List<MonthExchangeRatePO> result1 = monthExchangeRateMapper.selectList(queryWrapper);
        // 新增
        if (CollectionUtils.isNotEmpty(result1)) {
            if (isThrowExpetion) {
                throw new BusinessException(I18nMessageKit.getMessage("error.effectiveDateTime.existsOrEarlier"));
            } else {
                return new Tuple(ErrorCodeEnum.MONTH_EXCHANGE_RATE_TIME_CONFLICT, result1);
            }
        }

        return null;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public Integer update(MonthExchangeRate monthExchangeRate) {
        monthExchangeRate.valideUpdate();
        checkDate(monthExchangeRate, true);

        checkCanUpdate(monthExchangeRate);
        MonthExchangeRatePO fluctuateExchangeRatePO = MonthExchangeRateTransformer.INSTANCE.domain2Po(monthExchangeRate);
        fluctuateExchangeRatePO.setUpdateName(remoteUserDetailsFeign.getUserDetails(SecurityUtils.getUser().getName()).getUserInfo().getName());
        monthExchangeRateMapper.updateById(fluctuateExchangeRatePO);
        afterCommitPush(fluctuateExchangeRatePO);
        return fluctuateExchangeRatePO.getId();
    }

    private void checkCanUpdate(MonthExchangeRate monthExchangeRate) {
        MonthExchangeRatePO monthExchangeRatePO = monthExchangeRateMapper.selectById(monthExchangeRate.getId());
        LocalDateTime effectiveDateTime = monthExchangeRatePO.getEffectiveDateTime();
        LocalDateTime endTime = MonthExchangeRateRepositoryImpl.endOfDay(effectiveDateTime.toLocalDate());
        LocalDateTime now = LocalDateTime.now();
        // 判断当前时间是否在生效日期范围内
        if (now.isAfter(endTime)) {
            throw new BusinessException(I18nMessageKit.getMessage("EDIT_RATE_BEFORE_EFFECTIVE_END"));
        }
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public MonthExchangeRate importSave(MonthExchangeRate monthExchangeRate) {
        Tuple tuple = checkDate(monthExchangeRate, false);
        if (Objects.isNull(tuple)) {
            // 没有异常直接入库
            MonthExchangeRatePO monthExchangeRatePO = MonthExchangeRateTransformer.INSTANCE.domain2Po(monthExchangeRate);
            monthExchangeRatePO.setUpdateName(remoteUserDetailsFeign.getUserDetails(SecurityUtils.getUser().getName()).getUserInfo().getName());
            monthExchangeRateMapper.insert(monthExchangeRatePO);
            monthExchangeRate.setId(monthExchangeRatePO.getId());
            afterCommitPush(monthExchangeRatePO);
            return monthExchangeRate;
        }
        ErrorCodeEnum errorCodeEnum = tuple.get(0);
        List<MonthExchangeRatePO> list = tuple.get(1);
        // 覆盖
        if (ErrorCodeEnum.MONTH_EXCHANGE_RATE_DUPLICATE.equals(errorCodeEnum)) {
            for (MonthExchangeRatePO monthExchangeRatePO : list) {
                monthExchangeRatePO.setExchangeRate(monthExchangeRate.getExchangeRate());
                monthExchangeRatePO.setUpdateBy(SecurityUtils.getUserId());
                monthExchangeRatePO.setUpdateTime(LocalDateTime.now());
                monthExchangeRatePO.setUpdateName(remoteUserDetailsFeign.getUserDetails(SecurityUtils.getUser().getName()).getUserInfo().getName());
                monthExchangeRateMapper.updateById(monthExchangeRatePO);
                afterCommitPush(monthExchangeRatePO);
            }
        }
        return monthExchangeRate;

    }

    @Override
    public void syncNs(Integer id) {
        MonthExchangeRatePO monthExchangeRatePO = monthExchangeRateMapper.getOneById(id);
        if (monthExchangeRatePO == null) {
            return;
        }
        if (monthExchangeRatePO.getOriginalCurrencyCode() == null) {
            return;
        }
        if (monthExchangeRatePO.getTargetCurrencyCode() == null) {
            return;
        }
        // 本位币不是美元推送不了。
        if (!"USD".equals(monthExchangeRatePO.getOriginalCurrencyCode())) {
            log.error("本位币不是美元推送不了:{}", monthExchangeRatePO.getOriginalCurrencyCode());
            return;
        }
        if (Objects.equals(monthExchangeRatePO.getOriginalCurrencyCode(), monthExchangeRatePO.getTargetCurrencyCode())
        ) {
            if (!"USD".equals(monthExchangeRatePO.getTargetCurrencyCode())) {
                log.error("除了USD以外，本位币跟外币相同推送不了:{}", monthExchangeRatePO.getOriginalCurrencyCode());
                return;
            }
        }
        // 推ns
        MonthExchangeRateNsRequest monthExchangeRateNsRequest = new MonthExchangeRateNsRequest();
        //这里后面概念跟名字弄反了
        monthExchangeRateNsRequest.setOriginalCurrency(monthExchangeRatePO.getTargetCurrencyCode());
        //ns那边本位是Target
        monthExchangeRateNsRequest.setTargetCurrency(monthExchangeRatePO.getOriginalCurrencyCode());
        monthExchangeRateNsRequest.setExchangeRate(monthExchangeRatePO.getExchangeRate().doubleValue());
        // 假设 effectiveDateTime 是 UTC 时间
        LocalDateTime effectiveDateTime = monthExchangeRatePO.getEffectiveDateTime();
        ZonedDateTime utcDateTime = effectiveDateTime.atZone(ZoneId.of("UTC"));
        ZonedDateTime beijingDateTime = utcDateTime.withZoneSameInstant(ZoneId.of("Asia/Shanghai"));
        monthExchangeRateNsRequest.setEffectiveDate(DateTimeFormatter.ofPattern("yyyy-MM-dd")
                .format(beijingDateTime));

        boolean ok = MonthExchangeRateRepositoryImpl.sendMqPushNs(monthExchangeRateNsRequest);
        if (ok) {
            monthExchangeRateMapper.update(Wrappers.<MonthExchangeRatePO>lambdaUpdate().set(MonthExchangeRatePO::getNsStatus, 2).eq(CreationPO::getId, id));
        } else {
            monthExchangeRateMapper.update(Wrappers.<MonthExchangeRatePO>lambdaUpdate().set(MonthExchangeRatePO::getNsStatus, 3).eq(CreationPO::getId, id));
        }

    }

    @Override
    public void syncNsJob() {
        List<Integer> idList = monthExchangeRateMapper.selectList(Wrappers.<MonthExchangeRatePO>lambdaQuery().select(MonthExchangeRatePO::getId).ne(MonthExchangeRatePO::getNsStatus, 2)).stream().map(MonthExchangeRatePO::getId).distinct().toList();
        if (CollectionUtils.isEmpty(idList)) {
            return;
        }
        for (Integer id : idList) {
            try {
                syncNs(id);
            } catch (Exception e) {
                MonthExchangeRateRepositoryImpl.log.error(e.getMessage(), e);
            }
        }
    }

    @Getter
    private enum ErrorCodeEnum {

        // 月度汇率时间上交叉
        MONTH_EXCHANGE_RATE_TIME_CONFLICT(1001, "MONTH_EXCHANGE_RATE_TIME_CONFLICT"),

        // 月度汇率录入重复
        MONTH_EXCHANGE_RATE_DUPLICATE(1002, "MONTH_EXCHANGE_RATE_DUPLICATE");

        private final int code;
        private final String message;

        ErrorCodeEnum(int code, String message) {
            this.code = code;
            this.message = message;
        }
    }

}