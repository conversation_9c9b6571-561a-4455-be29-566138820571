package com.renpho.erp.mdm.infrastructure.persistence.repository.nsapidefinition;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.renpho.erp.mdm.domain.netsuite.NsApiDefinitionId;
import com.renpho.erp.mdm.infrastructure.persistence.mapper.nsapidefinition.NsHistoryPoMapper;
import com.renpho.erp.mdm.infrastructure.persistence.po.nsapidefinition.NsHistoryPo;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Repository;

/**
 * ns请求记录 Repository.
 *
 * <AUTHOR>
 * @since 2024/9/29
 */
@Repository
public class NsHistoryRepository extends ServiceImpl<NsHistoryPoMapper, NsHistoryPo> {

	public <T> NsHistoryPo addHistory(NsApiDefinitionId id, T params) {
		NsHistoryPo history = new NsHistoryPo();
		history.setBusiness(id.name());
        history.setParams(JSON.parseObject(JSON.toJSONString(params)));
		save(history);
		return history;
	}

    public NsHistoryPo updateResult(Integer id, ResponseEntity<String> entity) {
		NsHistoryPo po = getById(id);
		po.setStatusCode(entity.getStatusCode().value());
        po.setHeaders(JSON.parseObject(JSON.toJSONString(entity.getHeaders())));
        po.setResult(JSON.parseObject(entity.getBody(), new TypeReference<>() {}));
		updateById(po);
		return po;
	}

}
