package com.renpho.erp.mdm.infrastructure.persistence.repository.costcenter;

import cn.hutool.extra.spring.SpringUtil;
import com.alibaba.fastjson.JSON;
import com.renpho.erp.mdm.infrastructure.persistence.ns.CurrencyNsResponse;
import com.renpho.erp.mdm.infrastructure.persistence.ns.NsApiEndpointsConfig;
import com.renpho.erp.mdm.infrastructure.persistence.repository.rate.NsRequestBase;
import jakarta.annotation.Resource;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.apache.http.HttpEntity;
import org.apache.http.HttpStatus;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.util.EntityUtils;
import org.jetbrains.annotations.NotNull;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

@Slf4j
public class NsUtils {
    private static final Pattern PATTERN = Pattern.compile("<!--.*?-->", Pattern.DOTALL);
    @Getter
    private static final CloseableHttpClient httpClient = HttpClients.createDefault();
    private static final NsApiEndpointsConfig nsApiEndpointsConfig = SpringUtil.getBean(NsApiEndpointsConfig.class);


    private NsUtils() {
    }

    /**
     * remove
     * @param input inp
     * @return str
     */
    public static String removeHtmlComments(String input) {
        Matcher matcher = NsUtils.PATTERN.matcher(input);
        return matcher.replaceAll("");
    }

    private static void setHead(HttpPost httpPost) {
        httpPost.addHeader("Content-Type", "application/json;charset=utf-8");
        httpPost.addHeader("User-Agent", "Mozilla/5.0");
    }

    @SuppressWarnings("all")
    public static @NotNull <T> T sendRequestAndGetResult(String profile, NsRequestBase nsRequestBase, Class<T> resultClazz) {
        NsApiEndpointsConfig.Profile myProfile = nsApiEndpointsConfig.getNsApiEndPoints().get(profile);
        nsRequestBase.setToken(myProfile.getToken());
        String token = myProfile.getToken();
        HttpPost httpPost = new HttpPost(
                myProfile.getUrl());
        NsUtils.setHead(httpPost);
        httpPost.setEntity(new StringEntity(JSON.toJSONString(nsRequestBase), "UTF-8"));
        String resultBody = "";
        CurrencyNsResponse currencyNsResponse = null;
        Map<String, String> dataMap = Collections.emptyMap();
        try (CloseableHttpResponse response = NsUtils.getHttpClient().execute(httpPost)) {
            // 判断响应状态为200，进行处理
            if (response.getStatusLine().getStatusCode() == HttpStatus.SC_OK) {
                HttpEntity httpEntity = response.getEntity();
                resultBody = EntityUtils.toString(httpEntity, "utf-8");
                log.info("请求成功，响应结果：{}", resultBody);
                resultBody = removeHtmlComments(resultBody);
                return JSON.parseObject(resultBody, resultClazz);
            }
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }
        return null;
    }
}