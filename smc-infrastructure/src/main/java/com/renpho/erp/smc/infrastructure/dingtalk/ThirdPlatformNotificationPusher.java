package com.renpho.erp.smc.infrastructure.dingtalk;

import cn.hutool.core.util.StrUtil;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.i18n.LocaleContextHolder;
import org.springframework.stereotype.Component;

@AllArgsConstructor
@Component
@Slf4j
public class ThirdPlatformNotificationPusher {

	private final DingTalkUtil dingTalkUtil;

	private final ThirdPlatformNotificationConfig thirdPlatformNotificationConfig;

	/**
	 * 推送预警钉钉消息
	 */
	public void pushNotificationTemplate(NotificationTemplateEnum notificationTemplateEnum,
			NotificationTemplateDto notificationTemplateDto) {

		String languageTag = LocaleContextHolder.getLocale().toLanguageTag();
		String zhTitle = notificationTemplateEnum.getTitle(languageTag);
		String zhContent = notificationTemplateEnum.getFilledContent(languageTag, notificationTemplateDto);
		try {
			// 发给群
			String content = StrUtil.nullToEmpty(zhTitle + "\n<br>" + zhContent);
			dingTalkUtil.getRobotSendMarkdownMsg(zhTitle, new StringBuffer(content), null, true,
					thirdPlatformNotificationConfig.getDingDingBotAccessToken());
			// 发给人
			dingTalkUtil.sendMessageTextMarkdown(thirdPlatformNotificationConfig.getDingDingUserId(), zhTitle, content);
		}
		catch (Exception e) {
			log.error("钉钉消息异常", e);
		}

	}

}
