package com.renpho.erp.smc.infrastructure.enums;

import lombok.Getter;

/**
 * 第三方账号同步状态枚举
 *
 * <AUTHOR>
 * @Date 2025/6/26 14:44
 **/
@Getter
public enum ThirdAccountSyncStatusEnum {

	/**
	 * 同步中
	 */
	IN("IN", "同步中"),

	/**
	 * 成功
	 */
	SUCCESS("SUCCESS", "成功"),

	/**
	 * 失败
	 */
	FAILED("FAILED", "失败");

	/**
	 * 代码
	 */
	private final String code;

	/**
	 * 描述
	 */
	private final String desc;

	/**
	 * 注意类型枚举
	 * @param code 代码
	 * @param desc 描述
	 */
	ThirdAccountSyncStatusEnum(String code, String desc) {
		this.code = code;
		this.desc = desc;
	}

	/**
	 * 根据编码获取枚举
	 * @return com.renpho.erp.smc.infrastructure.enums.AccountTypeEnum
	 * <AUTHOR>
	 * @Date 14:45 2025/6/26
	 * @Param [code]
	 **/
	public static ThirdAccountSyncStatusEnum getByCode(String code) {
		for (ThirdAccountSyncStatusEnum type : ThirdAccountSyncStatusEnum.values()) {
			if (type.getCode().equals(code)) {
				return type;
			}
		}
		return null;
	}

}
