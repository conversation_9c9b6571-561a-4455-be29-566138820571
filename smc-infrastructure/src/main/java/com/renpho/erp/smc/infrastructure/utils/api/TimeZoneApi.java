package com.renpho.erp.smc.infrastructure.utils.api;

import java.io.IOException;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

import org.apache.http.HttpEntity;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.util.EntityUtils;
import org.springframework.stereotype.Component;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.renpho.erp.smc.domain.systemsetting.Status;
import com.renpho.erp.smc.infrastructure.persistence.mapper.DictItemMapper;
import com.renpho.erp.smc.infrastructure.persistence.mapper.TimezoneMapper;
import com.renpho.erp.smc.infrastructure.persistence.po.DictItemPO;
import com.renpho.erp.smc.infrastructure.persistence.po.TimezonePO;
import com.renpho.erp.smc.infrastructure.utils.common.StringUtils;
import com.xxl.job.core.handler.annotation.XxlJob;

import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;

/**
 * 通过nowapi获取时区信息
 *
 * <AUTHOR>
 * @date 2024/10/24 17:50
 */
@Slf4j
@Component
public class TimeZoneApi {

	@Resource
	private NowApiProperties nowApiProperties;

	@Resource
	private NowApiHttpClientUtil clientUtil;

	@Resource
	private TimezoneMapper timezoneMapper;

	@Resource
	private DictItemMapper dictItemMapper;

	@XxlJob("updateTimeZone")
	public void updateTimeZone() {
		List<TimezonePO> inserttTimezonePOS = this.getBatchApiTimeZone();
		if (!inserttTimezonePOS.isEmpty()) {
			List<TimezonePO> currentTimezoneList = this.timezoneMapper.selectList(null);
			inserttTimezonePOS.stream().filter(t -> currentTimezoneList.stream().anyMatch(c -> c.getCityEn().equals(t.getCityEn())));

			List<TimezonePO> updateTimeZoneList = inserttTimezonePOS.stream()
				.filter(t -> currentTimezoneList.stream().anyMatch(c -> c.getCityEn().equals(t.getCityEn())))
				.peek(t -> currentTimezoneList.stream()
					.filter(c -> c.getCityEn().equals(t.getCityEn()))
					.findFirst()
					.ifPresent(c -> t.setId(c.getId())))
				.collect(Collectors.toList());
			inserttTimezonePOS.removeAll(updateTimeZoneList);
			System.out.println("updateTimeZoneList:" + updateTimeZoneList);
			System.out.println("inserttTimezonePOS:" + inserttTimezonePOS);
			for (TimezonePO t : updateTimeZoneList) {
				this.timezoneMapper.updateById(t);
			}
			for (TimezonePO t : inserttTimezonePOS) {
				this.timezoneMapper.insert(t);
			}
		}
	}

	public List<TimezonePO> getBatchApiTimeZone() {
		log.info("updateTimeZone start ...");
		List<DictItemPO> timeZoneDictItems = this.dictItemMapper.selectDictListByType("time_zone", "en-US", null);
		List<String> timezoneList = timeZoneDictItems.stream().map(DictItemPO::getDictKey).collect(Collectors.toList());
		String nowApiUrl = this.nowApiProperties.getNowApi().getUrl();
		String appKey = this.nowApiProperties.getNowApi().getAppKey();
		String sign = this.nowApiProperties.getNowApi().getSign();
		String timezoneApp = this.nowApiProperties.getNowApi().getTimezoneApp();
		StringJoiner urlJoiner = new StringJoiner("&");
		urlJoiner.add(nowApiUrl + "/?sign=" + sign).add("app=" + timezoneApp).add("appkey=" + appKey).add("format=json");
		List<TimezonePO> newDataList = new ArrayList<>();
		for (String cisyEn : timezoneList) {
			String lastUrl = urlJoiner.add("city_en=" + cisyEn).toString();
			this.getApiTimeZone(this.clientUtil, lastUrl, newDataList, cisyEn);
		}
		System.out.println("响应内容: " + newDataList);
		return newDataList;
	}

	private void getApiTimeZone(NowApiHttpClientUtil clientUtil, String apiUrl, List<TimezonePO> newDataList, String cisyEn) {
		HttpGet httpGet = new HttpGet(apiUrl);
		String responseString = "";
		try (CloseableHttpResponse response = clientUtil.getHttpClient().execute(httpGet)) {
			int statusCode = response.getStatusLine().getStatusCode();
			HttpEntity entity = response.getEntity();
			if (entity != null) {
				responseString = EntityUtils.toString(entity);
				System.out.println("响应内容: " + responseString);
			}
		}
		catch (IOException e) {
			log.error("未查到时区/网络故障/未及时缴费:{}", e);
		}
		log.info("---> {}", responseString);
		Map<String, Object> responseMap = JSONObject.parseObject(responseString);
		if (Objects.equals("1", responseMap.get("success"))) {
			Map<String, Object> resultMap = (Map<String, Object>) responseMap.get("result");
			System.out.println("响应内容: " + resultMap);
			TimezonePO timezonePO = this.toTimezonePo(resultMap);
			newDataList.add(timezonePO);
		}
		else {
			log.error("获取时区异常:{},错误信息为:{}", cisyEn, JSON.toJSONString(responseString, SerializerFeature.WriteMapNullValue));
		}
	}

	private TimezonePO toTimezonePo(Map<String, Object> resultMap) {
		TimezonePO timezonePO = new TimezonePO();
		timezonePO.setContinentsEn((String) resultMap.get("continents_en"));
		timezonePO.setContinentsCn((String) resultMap.get("continents_cn"));
		timezonePO.setContryEn((String) resultMap.get("contry_en"));
		timezonePO.setContryCn((String) resultMap.get("contry_cn"));
		timezonePO.setCityEn((String) resultMap.get("city_en"));
		timezonePO.setCityCn((String) resultMap.get("city_cn"));
		timezonePO.setStatus(Status.ACTIVE.getValue());
		timezonePO.setTimeZoneNo(("UTC" + resultMap.get("time_zone_no")).replace(":00", "").replace(":0", ""));
		timezonePO.setTimeZoneNm((String) resultMap.get("time_zone_nm"));
		timezonePO.setSmrMk((String) resultMap.get("smr_mk"));
		timezonePO.setSmrStatus((String) resultMap.get("smr_status"));
		timezonePO.setCreateBy(1);
		timezonePO.setUpdateBy(1);
		if (StringUtils.isNotBlank((String) resultMap.get("smr_str_datetime"))) {
			timezonePO.setSmrStrDatetime(
					LocalDateTime.parse((String) resultMap.get("smr_str_datetime"), DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
		}
		if (StringUtils.isNotBlank((String) resultMap.get("smr_end_datetime"))) {
			timezonePO.setSmrEndDatetime(
					LocalDateTime.parse((String) resultMap.get("smr_end_datetime"), DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
		}
		return timezonePO;
	}

}
