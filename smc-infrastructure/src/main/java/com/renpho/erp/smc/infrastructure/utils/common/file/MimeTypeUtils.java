package com.renpho.erp.smc.infrastructure.utils.common.file;

/**
 * 媒体类型工具类.
 *
 * <AUTHOR>
 */
public final class MimeTypeUtils {

	private MimeTypeUtils() {
	}

	/**
	 * 图片
	 */
	public static final String IMAGE_PNG = "image/png";

	/**
	 * 图片
	 */
	public static final String IMAGE_JPG = "image/jpg";

	/**
	 * 图片
	 */
	public static final String IMAGE_JPEG = "image/jpeg";

	/**
	 * 图片
	 */
	public static final String IMAGE_BMP = "image/bmp";

	/**
	 * 图片
	 */
	public static final String IMAGE_GIF = "image/gif";

	public static String getExtension(String prefix) {
		switch (prefix) {
			case IMAGE_PNG:
				return "png";
			case IMAGE_JPG:
				return "jpg";
			case IMAGE_JPEG:
				return "jpeg";
			case IMAGE_BMP:
				return "bmp";
			case IMAGE_GIF:
				return "gif";
			default:
				return "";
		}
	}

}
