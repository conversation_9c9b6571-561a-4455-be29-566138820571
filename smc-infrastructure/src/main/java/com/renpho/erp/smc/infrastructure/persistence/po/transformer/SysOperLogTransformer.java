package com.renpho.erp.smc.infrastructure.persistence.po.transformer;

import com.renpho.erp.oplog.log.repo.SysOperLog;
import com.renpho.erp.smc.infrastructure.persistence.po.SysOperLogPO;
import org.mapstruct.Mapper;
import org.mapstruct.MapperConfig;
import org.mapstruct.factory.Mappers;

@Mapper
@MapperConfig
public interface SysOperLogTransformer {

	/**
	 * 实例
	 */
	SysOperLogTransformer INSTANCE = Mappers.getMapper(SysOperLogTransformer.class);

	SysOperLogPO toItemPO(SysOperLog dto);

}
