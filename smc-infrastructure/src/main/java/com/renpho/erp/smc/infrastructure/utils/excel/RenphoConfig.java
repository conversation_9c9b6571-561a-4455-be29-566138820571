package com.renpho.erp.smc.infrastructure.utils.excel;

import cn.hutool.core.io.FileUtil;
import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * 读取项目相关配置.
 *
 * <AUTHOR>
 */
@Data
@Component
@ConfigurationProperties(prefix = "renpho")
public class RenphoConfig {

	/**
	 * 项目名称
	 */
	private String name;

	/**
	 * 版本
	 */
	private String version;

	/**
	 * 版权年份
	 */
	private String copyrightYear;

	/**
	 * 上传路径
	 */
	private static String profile;

	public static String getProfile() {
		return profile;
	}

	public void setProfile(String profile) {
		RenphoConfig.profile = profile;
	}

	/**
	 * 获取下载路径
	 * @return 获取下载路径
	 */
	public static String getDownloadPath() {
		String downloadPath = getProfile() + "/download/";
		FileUtil.mkdir(downloadPath);
		return downloadPath;
	}

	/**
	 * 获取默认上传路径
	 * @return 默认上传路径
	 */
	public static String getUploadPath() {
		String uploadPath = getProfile() + "/upload/";
		FileUtil.mkdir(uploadPath);
		return uploadPath;
	}

}
