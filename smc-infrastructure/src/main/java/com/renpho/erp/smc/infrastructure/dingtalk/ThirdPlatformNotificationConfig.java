package com.renpho.erp.smc.infrastructure.dingtalk;

import lombok.Data;
import lombok.Getter;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Component;

@RefreshScope
@Component
@ConfigurationProperties(prefix = "renpho.karma.third.platform")
@Data
public class ThirdPlatformNotificationConfig {

	@Getter
	private static String userProfile = "userProfile";

	private String dingDingBotAccessToken;

	private String[] dingDingUserId;

}