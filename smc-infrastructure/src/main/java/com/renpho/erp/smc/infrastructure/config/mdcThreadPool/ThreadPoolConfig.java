package com.renpho.erp.smc.infrastructure.config.mdcThreadPool;

import java.util.concurrent.ThreadPoolExecutor;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * @desc: 自定义线程池
 * @time: 2025-03-17 10:00:37
 * @author: Alina
 */
@Configuration
public class ThreadPoolConfig {

	@Bean(name = "eventMdcThreadPoolTaskExecutor")
	public MdcThreadPoolTaskExecutor eventMdcThreadPoolTaskExecutor() {
		MdcThreadPoolTaskExecutor executor = new MdcThreadPoolTaskExecutor();
		executor.setCorePoolSize(16);
		executor.setMaxPoolSize(32);
		executor.setQueueCapacity(100);
		executor.setKeepAliveSeconds(60);
		executor.setRejectedExecutionHandler(new ThreadPoolExecutor.AbortPolicy());
		executor.setThreadNamePrefix("EVENTMDC-Executor-");
		executor.initialize();
		return executor;
	}

}
