package com.renpho.erp.smc.infrastructure.utils.excel;

import lombok.Data;

/**
 * excel导入格式.
 */
@Data
public class ExcelExportData {

	/**
	 * 标题key
	 */
	private String value;

	/**
	 * 标题名称
	 */
	private String label;

	/**
	 * 分组:同一组的数据将进行合并 { "value":"pretendDoNo", "label":"do交货单号", "group":"测试" },{
	 * "value":"qualitySkuNum", "label":"采购数量", "group":"测试" } [ 测试 ] [采购数量 | do交货单号]
	 * <p>
	 * 数据处理：titleMap = queryDto.getExcelExportData().forEach(e -> titleMap.put(
	 * e.getValue(), null==e.getGroup() ? e.getLabel() : e.getGroup() +"_"+ e.getLabel()
	 * )) 方法调用：EasyExcelUtil.groupExportExcelFile()
	 */
	private String group;

}
