package com.renpho.erp.smc.infrastructure.persistence.po;

import java.util.List;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.renpho.erp.smc.domain.systemsetting.MultiLanguage;
import com.renpho.karma.cloud.mybatisplus.po.DefaultPO;

import lombok.Data;

/**
 * 角色 PO.
 *
 * <AUTHOR>
 */
@Data
@TableName("oum_role")
public class RolePO extends DefaultPO {

	/**
	 * 名称
	 */
	private String name;

	/**
	 * 标签
	 */
	private String label;

	@TableField(exist = false)
	private String nameStr;

	@TableField(exist = false)
	private List<MultiLanguage> names;

}
