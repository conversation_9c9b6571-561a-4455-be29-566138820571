package com.renpho.erp.smc.infrastructure.enums;

import lombok.Getter;

/**
 * 业务操作类型.
 *
 * <AUTHOR>
 * @date 2022/12/20 15:48
 */
@Getter
public enum EmploymentEnum {

	/**  */
	FT("FT", "Full-time Employment", "全日制", DingDingEmployeeTypeEnum.FULL_TIME),
	/**  */
	PE("PE", "Part-time Employment", "非全日制", DingDingEmployeeTypeEnum.PART_TIME),
	/**  */
	RE("RE", "Re-employ after Retirement", "退休返聘", DingDingEmployeeTypeEnum.RETIREE_REHIRED),
	/**  */
	PT("PT", "Part-time", "兼职", DingDingEmployeeTypeEnum.PART_TIME),
	/**  */
	DI("DI", "Dispatched", "劳务派遣", DingDingEmployeeTypeEnum.DISPATCHED_WORKER),
	/**  */
	LO("LO", "Labor-outsourcing", "劳务外包", DingDingEmployeeTypeEnum.SERVICE_OUTSOURCING),
	/**  */
	IN("IN", "Internship", "实习生", DingDingEmployeeTypeEnum.INTERN),
	/**  */
	SO("SO", "Service-outsourcing", "服务外包", DingDingEmployeeTypeEnum.NONE),
	/**  */
	FL("FL", "Freelance", "自由职业者", DingDingEmployeeTypeEnum.PART_TIME),;

	private final String code;

	private final String enName;

	private final String zhName;

	private final DingDingEmployeeTypeEnum dingDingEmployeeType;

	EmploymentEnum(String code, String enName, String zhName, DingDingEmployeeTypeEnum dingDingEmployeeType) {
		this.code = code;
		this.enName = enName;
		this.zhName = zhName;
		this.dingDingEmployeeType = dingDingEmployeeType;
	}

	public static EmploymentEnum getByCode(String name) {
		for (EmploymentEnum e : EmploymentEnum.values()) {
			if (e.getEnName().equals(name) || e.getZhName().equals(name) || e.getCode().equals(name)) {
				return e;
			}
		}
		return null;
	}

}
