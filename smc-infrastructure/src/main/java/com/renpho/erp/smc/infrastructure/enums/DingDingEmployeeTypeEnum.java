package com.renpho.erp.smc.infrastructure.enums;

import lombok.Getter;

/**
 * 钉钉员工类型枚举
 */
@Getter
public enum DingDingEmployeeTypeEnum {

	/** 全职 */
	FULL_TIME("全职", "Full-time"),
	/** 兼职 */
	PART_TIME("兼职", "Part-time"),
	/** 退休人员重新雇用 */
	RETIREE_REHIRED("退休返聘", "Retiree Rehire"),
	/** 派遣工人 */
	DISPATCHED_WORKER("劳务派遣", "Dispatched Worker"),
	/** 服务外包 */
	SERVICE_OUTSOURCING("劳务外包", "Service Outsourcing"),
	/** 实习生 */
	INTERN("实习", "Intern"),
	/** 没有任何 */
	NONE("无类型", "None");

	/** ZH名称 */
	private final String zhName;

	/** en Name */
	private final String enName;

	DingDingEmployeeTypeEnum(String zhName, String enName) {
		this.zhName = zhName;
		this.enName = enName;
	}

	@Override
	public String toString() {
		return "EmployeeType{" + "zhName='" + zhName + '\'' + ", enName='" + enName + '\'' + '}';
	}

}