package com.renpho.erp.smc.infrastructure.dingtalk;

import java.util.concurrent.CompletableFuture;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.function.Supplier;

import org.springframework.context.annotation.Configuration;

import io.netty.util.concurrent.DefaultThreadFactory;
import jakarta.annotation.PostConstruct;
import lombok.extern.slf4j.Slf4j;

@Configuration
@Slf4j
public class MyThreadPool {

	private int threadNum = 10;

	/** poolExecutor */
	private static ThreadPoolExecutor poolExecutor;

	@PostConstruct
	public static ThreadPoolExecutor initPool() {
		if (poolExecutor != null) {
			return poolExecutor;
		}
		synchronized (MyThreadPool.class) {
			if (poolExecutor != null) {
				return poolExecutor;
			}
			int processors = Runtime.getRuntime().availableProcessors();
			MyThreadPool.poolExecutor = new ThreadPoolExecutor(processors, processors * 2, 60, TimeUnit.SECONDS,
					new LinkedBlockingQueue<>(1000), new DefaultThreadFactory("MyThreadPool-work-thread"),
					new ThreadPoolExecutor.CallerRunsPolicy());
		}
		return poolExecutor;
	}

	/**
	 * 重写
	 * @param command ok
	 */
	public static void execute(Runnable command) {
		if (poolExecutor == null) {
			initPool();
		}
		poolExecutor.execute(command);
	}

	/**
	 * 支持异步处理
	 * @param supplier s
	 * @return ok
	 */
	public static <U> CompletableFuture<U> supplyAsync(Supplier<U> supplier) {
		if (poolExecutor == null) {
			initPool();
		}
		return CompletableFuture.supplyAsync(supplier, poolExecutor);
	}

	/**
	 * 异步处理问题
	 * @param runnable ok
	 * @return res
	 */
	public static CompletableFuture<Void> runAsync(Runnable runnable) {
		if (poolExecutor == null) {
			initPool();
		}
		return CompletableFuture.runAsync(runnable, poolExecutor);
	}

	public static void main(String[] args) {
		MyThreadPool.initPool();
	}

}
