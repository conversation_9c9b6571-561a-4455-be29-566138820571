package com.renpho.erp.smc.infrastructure.utils.email;

import java.util.Properties;

import javax.mail.Message;
import javax.mail.MessagingException;
import javax.mail.PasswordAuthentication;
import javax.mail.Session;
import javax.mail.Transport;
import javax.mail.internet.InternetAddress;
import javax.mail.internet.MimeMessage;

import lombok.extern.slf4j.Slf4j;

@Slf4j
public class EmailUtils {

	/**
	 * @param args
	 */
	public static void main(String[] args) {
		// 设置发送邮件的一些基本配置
		String host = "smtp.office365.com"; // Outlook的SMTP服务器
		final String username = "<EMAIL>"; // 你的Outlook邮箱用户名
		final String password = "QZhY7hJ,dC"; // 你的Outlook邮箱密码或生成的应用密码

		Properties props = new Properties();
		props.put("mail.smtp.auth", "true");
		props.put("mail.smtp.starttls.enable", "true"); // 启用TLS
		props.put("mail.smtp.host", host);

		props.put("mail.smtp.starttls.required", "true");
		props.put("mail.smtp.socketFactory.port", "587");
		// properties.put("mail.smtp.port", 587);
		props.put("mail.smtp.socketFactory.ssl.class", "javax.net.ssl.SSLSocketFactory");
		props.put("mail.smtp.socketFactory.ssl.fallback", "false");
		// props.put("mail.smtp.port", "587"); // Outlook SMTP服务器的端口号587或465

		// 获取默认Session对象和创建MimeMessage消息对象
		Session session = Session.getInstance(props, new javax.mail.Authenticator() {
			protected PasswordAuthentication getPasswordAuthentication() {
				return new PasswordAuthentication(username, password);
			}
		});

		try {
			// 创建一个默认的MimeMessage对象
			MimeMessage message = new MimeMessage(session);

			// 设置 From: header field of the header.
			message.setFrom(new InternetAddress(username));

			// 设置 To: header field of the header.
			message.setRecipients(Message.RecipientType.TO, InternetAddress.parse("<EMAIL>"));

			// 设置 Subject: header field
			message.setSubject("Test Mail from Java");

			// 现在设置实际的消息体
			message.setText("This is a test mail from Java program using JavaMail API.");

			// 发送消息
			Transport.send(message);

			System.out.println("Sent message successfully....");

		}
		catch (MessagingException e) {
			throw new RuntimeException(e);
		}
	}

}
