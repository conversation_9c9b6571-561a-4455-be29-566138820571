package com.renpho.erp.smc.infrastructure.utils.email;

import java.util.List;

import lombok.Data;

@Data
public class MailDto {

	/** 设置邮箱客户端(mail.smtp.host) */
	private String mailHostKey;

	/** 设置邮箱客户端(smtp.qq.com) */
	private String mailHostValue;

	/** 设置邮箱客户端(smtp.qq.com) */
	private Integer mailPort;

	/** 设置邮件主题 */
	private String mailSubject;

	/** 设置邮件正文 */
	private String mailBody;

	/** 设置邮件发件人 */
	private String mailSender;

	/** 设置邮箱客户端(smtp.qq.com) */
	private String mailPassword;

	/** 设置邮件收件人(多个收件人，以逗号隔开) */
	private String mailRecipientTO;

	/** 设置邮件抄送人(多个抄送人，以逗号隔开) */
	private String mailRecipientCC;

	/** 设置邮件抄送人(多个抄送人，以逗号隔开) */
	private String mailRecipientBCC;

	/** 设置邮箱属性 */
	private String propJson;

	/** 是否设置身份认证 */
	private boolean mailIsAuth;

	/** 设置smtp身份认证(mail.smtp.auth) */
	private String mailAuth;

	List<EmailUrl> emailUrls;

	public MailDto() {
		super();
		// TODO Auto-generated constructor stub
	}

	public MailDto(String mailHostKey, String mailHostValue, String mailSubject, String mailBody, String mailSender, String mailPassword,
			String mailRecipientTO, String mailRecipientCC, boolean mailIsAuth, String mailAuth, String mailRecipientBCC) {
		super();
		this.mailHostKey = mailHostKey;
		this.mailHostValue = mailHostValue;
		this.mailSubject = mailSubject;
		this.mailBody = mailBody;
		this.mailSender = mailSender;
		this.mailPassword = mailPassword;
		this.mailRecipientTO = mailRecipientTO;
		this.mailRecipientCC = mailRecipientCC;
		this.mailIsAuth = mailIsAuth;
		this.mailAuth = mailAuth;
		this.mailRecipientBCC = mailRecipientBCC;
	}

}
