package com.renpho.erp.smc.infrastructure.utils.common.poi;

import org.apache.poi.ss.usermodel.HorizontalAlignment;
import org.apache.poi.ss.usermodel.IndexedColors;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;
import java.math.BigDecimal;

/**
 * 自定义导出Excel数据注解.
 *
 * <AUTHOR>
 */
@Retention(RetentionPolicy.RUNTIME)
@Target(ElementType.FIELD)
public @interface Excel {

	/**
	 * 导出时在excel中排序
	 * @return 1
	 */
	int sort() default Integer.MAX_VALUE;

	/**
	 * 导出到Excel中的名字.
	 * @return 1
	 */
	String name() default "";

	/**
	 * 日期格式, 如: yyyy-MM-dd
	 * @return 1
	 */
	String dateFormat() default "";

	/**
	 * 读取内容转表达式 (如: 0=男,1=女,2=未知)
	 * @return 1
	 */
	String readConverterExp() default "";

	/**
	 * 分隔符，读取字符串组内容
	 * @return 1
	 */
	String separator() default ",";

	/**
	 * BigDecimal 精度 默认:-1(默认不开启BigDecimal格式化)
	 * @return 1
	 */
	int scale() default -1;

	/**
	 * BigDecimal 舍入规则 默认:BigDecimal.ROUND_HALF_EVEN
	 * @return 1
	 */
	int roundingMode() default BigDecimal.ROUND_HALF_EVEN;

	/**
	 * 自定义数据处理器
	 * @return 1
	 */
	Class<?> handler() default ExcelHandlerAdapter.class;

	/**
	 * 正则表达式
	 * @return 1
	 */
	String regex() default "";

	/**
	 * 正则表达式错误提示
	 * @return 1
	 */
	String message() default "";

	/**
	 * 导出时在excel中每个列的高度 单位为字符
	 * @return 1
	 */
	double height() default 14;

	/**
	 * 导出时在excel中每个列的宽 单位为字符
	 * @return 1
	 */
	double width() default 16;

	/**
	 * 文字后缀,如% 90 变成90%
	 * @return 1
	 */
	String suffix() default "";

	/**
	 * 当值为空时,字段的默认值
	 * @return 1
	 */
	String defaultValue() default "";

	/**
	 * 提示信息
	 * @return 1
	 */
	String prompt() default "";

	/**
	 * 设置只能选择不能输入的列内容.
	 * @return 1
	 */
	String[] combo() default {};

	/**
	 * 是否导出数据,应对需求:有时我们需要导出一份模板,这是标题需要但内容需要用户手工填写.
	 * @return 1
	 */
	boolean isExport() default true;

	/**
	 * 另一个类中的属性名称,支持多级获取,以小数点隔开
	 * @return 1
	 */
	String targetAttr() default "";

	/**
	 * 是否自动统计数据,在最后追加一行统计数据总和
	 * @return 1
	 */
	boolean isStatistics() default false;

	/**
	 * 导出类型（0数字 1字符串）
	 * @return 1
	 */
	ColumnType cellType() default ColumnType.STRING;

	/**
	 * 导出列头背景色
	 * @return 1
	 */
	IndexedColors headerBackgroundColor() default IndexedColors.GREY_50_PERCENT;

	/**
	 * 导出列头字体颜色
	 * @return 1
	 */
	IndexedColors headerColor() default IndexedColors.WHITE;

	/**
	 * 导出单元格背景色
	 * @return 1
	 */
	IndexedColors backgroundColor() default IndexedColors.WHITE;

	/**
	 * 导出单元格字体颜色
	 * @return 1
	 */
	IndexedColors color() default IndexedColors.BLACK;

	/**
	 * 导出字段对齐方式
	 * @return 1
	 */
	HorizontalAlignment align() default HorizontalAlignment.CENTER;

	/**
	 * 自定义数据处理器参数
	 * @return 1
	 */
	String[] args() default {};

	/**
	 * 字段类型（0：导出导入；1：仅导出；2：仅导入）
	 * @return 1
	 */
	Type type() default Type.ALL;

	enum Type {

		/**
		 * 1
		 */
		ALL(0),
		/**
		 * 1
		 */
		EXPORT(1),
		/**
		 * 1
		 */
		IMPORT(2);

		private final int value;

		Type(int value) {
			this.value = value;
		}

		int value() {
			return this.value;
		}

	}

	enum ColumnType {

		/**
		 * 1
		 */
		NUMERIC(0),
		/**
		 * 1
		 */
		STRING(1),
		/**
		 * 1
		 */
		IMAGE(2);

		private final int value;

		ColumnType(int value) {
			this.value = value;
		}

		int value() {
			return this.value;
		}

	}

	/**
	 * 如果是字典类型，请设置字典的type值 (如: sys_user_sex)
	 * @return 1
	 */
	String dictType() default "";

}
