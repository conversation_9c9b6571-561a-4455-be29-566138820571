package com.renpho.erp.smc.infrastructure.utils.email;

import java.io.File;
import java.io.UnsupportedEncodingException;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.mail.javamail.JavaMailSender;
import org.springframework.mail.javamail.MimeMessageHelper;
import org.springframework.stereotype.Component;

import com.alibaba.fastjson.JSON;
import com.renpho.erp.security.util.SecurityUtils;
import com.renpho.erp.smc.domain.oumuser.EmailTypeEnum;
import com.renpho.erp.smc.infrastructure.persistence.mapper.EmailLogMapper;
import com.renpho.erp.smc.infrastructure.persistence.po.EmailLog;

import cn.hutool.core.collection.CollectionUtil;
import jakarta.annotation.Resource;
import jakarta.mail.MessagingException;
import jakarta.mail.internet.MimeMessage;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Component
public class EmailOutLook {

	@Value("${spring.mail.username}")
	private String from;

	@Value("${spring.mail.nickname}")
	private String nickName;

	@Resource
	private JavaMailSender javaMailSender;

	@Resource
	private EmailLogMapper emailLogMapper;

	public void sendEmail(List<String> receivers, String title, String content, EmailTypeEnum emailType) {

		log.info("邮件发送主体：{} ，发送用户：{} title:{}", content, receivers, title);
		List<File> fileList = new ArrayList<>();
		// 构建邮件发送记录
		EmailLog emailLog = buildEmailLog(receivers, title, content, emailType);
		try {
			MimeMessage message = javaMailSender.createMimeMessage();
			MimeMessageHelper helper = new MimeMessageHelper(message, true, "UTF-8");
			// 邮件发送人
			helper.setFrom(from, nickName);
			// 邮件接收者
			helper.setTo(receivers.toArray(new String[0]));
			// 主题
			helper.setSubject(title);
			helper.setText(content, true);
			// 多个附件
			javaMailSender.send(message);
		}
		catch (MessagingException | UnsupportedEncodingException e) {
			log.error("邮件发送异常:", e);
			emailLog.setStatus(0);
			emailLog.setErrMsg(e.getMessage());
		}
		finally {
			if (CollectionUtil.isNotEmpty(fileList)) {
				for (File file : fileList) {
					file.delete();
				}
			}
			emailLogMapper.insert(emailLog);
		}
	}

	private EmailLog buildEmailLog(List<String> receivers, String title, String content, EmailTypeEnum emailType) {
		EmailLog emailLog = new EmailLog();
		emailLog.setFromEmail(from);
		emailLog.setFromNickName(nickName);
		emailLog.setToEmail(JSON.toJSONString(receivers));
		emailLog.setTitle(title);
		emailLog.setContent(content);
		emailLog.setType(emailType.getCode());
		emailLog.setStatus(1);
		emailLog.setCreateBy(Math.toIntExact(com.renpho.karma.web.servlet.mvc.context.SecurityUtils.getUserId() == null
				? SecurityUtils.getUserId() : com.renpho.karma.web.servlet.mvc.context.SecurityUtils.getUserId()));
		emailLog.setCreateTime(LocalDateTime.now());
		return emailLog;
	}

}
