package com.renpho.erp.smc.infrastructure.enums;

import lombok.Getter;

/**
 * 可见类型枚举
 *
 * <AUTHOR>
 * @date 2025/03/28
 */
@Getter
public enum VisibilityTypeEnum {

	/**
	 * 部门
	 */
	DEPARTMENT(1, "部门"),
	/**
	 * 用户
	 */
	USER(2, "用户");

	/**
	 * 价值
	 */
	private final Integer value;

	/**
	 * 描述
	 */
	private final String description;

	/**
	 * 可见性类型枚举
	 * @param value 价值
	 * @param description 描述
	 */
	VisibilityTypeEnum(Integer value, String description) {
		this.value = value;
		this.description = description;
	}

	/**
	 * 价值
	 * @param value 价值
	 * @return {@link VisibilityTypeEnum }
	 */
	public static VisibilityTypeEnum valueOf(Integer value) {
		for (VisibilityTypeEnum type : VisibilityTypeEnum.values()) {
			if (type.getValue().equals(value)) {
				return type;
			}
		}
		throw new IllegalArgumentException("Invalid VisibilityTypeEnum value: " + value);
	}

}
