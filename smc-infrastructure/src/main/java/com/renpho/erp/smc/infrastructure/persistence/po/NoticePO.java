package com.renpho.erp.smc.infrastructure.persistence.po;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.renpho.karma.cloud.mybatisplus.po.DefaultPO;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 系统公告表
 *
 * <AUTHOR>
 * @date 2025/03/25
 */
@EqualsAndHashCode(callSuper = true)
@Data
@AllArgsConstructor
@NoArgsConstructor
@TableName(value = "sys_notice")
public class NoticePO extends DefaultPO implements Serializable {

	/**
	 * 类型：1-通知公告、2-集团要闻
	 */
	@TableField(value = "`type`")
	private Integer type;

	/**
	 * 标题
	 */
	@TableField(value = "title")
	private String title;

	/**
	 * 封面图
	 */
	@TableField(value = "cover_image_url")
	private String coverImageUrl;

	/**
	 * 是否置顶，0 否、1 是
	 */
	@TableField(value = "is_top")
	private Integer isTop;

	/**
	 * 发布时间
	 */
	@TableField(value = "published_time")
	private LocalDateTime publishedTime;

	/**
	 * 发布人
	 */
	@TableField(value = "publisher")
	private Integer publisher;

	@TableField(exist = false)
	private String content;

	/**
	 *
	 */
	@Serial
	private static final long serialVersionUID = 1L;

}