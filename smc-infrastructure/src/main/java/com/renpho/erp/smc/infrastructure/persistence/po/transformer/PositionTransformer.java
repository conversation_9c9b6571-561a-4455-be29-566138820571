package com.renpho.erp.smc.infrastructure.persistence.po.transformer;

import com.renpho.erp.smc.domain.organizationmanagement.position.Position;
import com.renpho.erp.smc.domain.organizationmanagement.position.PositionStatus;
import com.renpho.erp.smc.domain.systemsetting.MultiLanguage;
import com.renpho.erp.smc.infrastructure.persistence.po.PositionMultilanguagePO;
import com.renpho.erp.smc.infrastructure.persistence.po.PositionPO;
import org.mapstruct.Mapper;
import org.mapstruct.MapperConfig;
import org.mapstruct.factory.Mappers;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

@Mapper
@MapperConfig
public interface PositionTransformer {

	/**
	 * 实例
	 */
	PositionTransformer INSTANCE = Mappers.getMapper(PositionTransformer.class);

	default Integer mapPositionID(Position.PositionID value) {
		if (null == value) {
			return null;
		}
		return value.getId();
	}

	default Integer mapPositionStatus(PositionStatus value) {
		if (null == value) {
			return null;
		}
		return value.getValue();
	}

	default PositionPO toPositionPO(Position position) {
		PositionPO positionPO = new PositionPO();
		positionPO.setId(mapPositionID(position.getId()));
		positionPO.setDepartmentId(String.valueOf(position.getDepartmentIds()));
		positionPO.setCode(Optional.of(position.getCode()).orElse("GENERATING"));
		positionPO.setRemark(position.getRemark());
		positionPO.setStatus(mapPositionStatus(position.getStatus()));
		return positionPO;
	}

	default PositionMultilanguagePO toPositionMultilanguagePO(MultiLanguage multiLanguage, Integer positionId) {
		PositionMultilanguagePO positionMultilanguagePO = new PositionMultilanguagePO();
		positionMultilanguagePO.setPositionId(positionId);
		positionMultilanguagePO.setName(multiLanguage.getName());
		positionMultilanguagePO.setLanguage(multiLanguage.getLanguage());
		return positionMultilanguagePO;
	}

	default List<PositionMultilanguagePO> toPositionMultilanguagePOList(List<MultiLanguage> names, Integer positionId) {
		List<PositionMultilanguagePO> positionMultilanguagePOList = new ArrayList<>(names.size());
		for (MultiLanguage multiLanguage : names) {
			positionMultilanguagePOList.add(toPositionMultilanguagePO(multiLanguage, positionId));
		}
		return positionMultilanguagePOList;
	}

	default Position toPosition(PositionPO positionPO) {
		List<Integer> departmentIds = convertToDepartmentIdsList(positionPO.getDepartmentId());
		Position position = new Position(Position.PositionID.of(positionPO.getId()), departmentIds, positionPO.getCode(),
				positionPO.getMember(), PositionStatus.enumOf(positionPO.getStatus()), positionPO.getRemark(), null);
		return position;
	}

	default List<Integer> convertToDepartmentIdsList(String departmentIdsStr) {
		// 去除字符串两端的方括号
		String trimmedStr = departmentIdsStr.replaceAll("^\\[|\\]$", "");

		// 按照逗号分割字符串
		String[] idsArray = trimmedStr.split(",");

		// 将字符串数组转换为 Integer 数组
		List<Integer> integerList = Arrays.stream(idsArray).map(String::trim).map(Integer::parseInt).collect(Collectors.toList());

		return integerList;
	}

	default List<Position> toPositionList(List<PositionPO> positionPOList) {
		return positionPOList.stream().map(INSTANCE::toPosition).collect(Collectors.toList());
	};

}
