package com.renpho.erp.smc.infrastructure.utils.email;

import lombok.Getter;

/**
 * 邮件类型枚举
 *
 * <AUTHOR>
 * @date 2025/05/13
 */
@Getter
public enum EmailTypeEnum {

	/**
	 * 检查密码新设备
	 */
	CHECK_PASSWORD_NEW_DEVICE(0, "检查密码是否新设备"),
	/**
	 * 用户重置密码
	 */
	USER_RESET_PASSWORD(1, "用户重置密码"),
	/**
	 * 管理员重置密码
	 */
	ADMIN_RESET_PASSWORD(2, "管理员重置密码"),
	/**
	 * 新用户
	 */
	NEW_USER(3, "新增用户"),
	/**
	 * 导入更新用户
	 */
	IMPORT_UPDATE_USER(4, "导入更新用户");

	/**
	 * 代码
	 */
	private final int code;

	/**
	 * 描述
	 */
	private final String description;

	/**
	 * 电子邮件类型枚举
	 * @param code 代码
	 * @param description 描述
	 */
	EmailTypeEnum(int code, String description) {
		this.code = code;
		this.description = description;
	}

	/**
	 * 来自代码
	 * @param code 代码
	 * @return {@link EmailTypeEnum }
	 */
	public static EmailTypeEnum fromCode(int code) {
		for (EmailTypeEnum type : values()) {
			if (type.getCode() == code) {
				return type;
			}
		}
		throw new IllegalArgumentException("Invalid email type code: " + code);
	}

}