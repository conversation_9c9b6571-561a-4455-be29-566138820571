package com.renpho.erp.smc.infrastructure.persistence.po.transformer;

import com.renpho.erp.smc.domain.systemsetting.param.ParamConfig;
import com.renpho.erp.smc.domain.systemsetting.param.ParamStatus;
import com.renpho.erp.smc.infrastructure.persistence.po.ParamConfigPO;
import org.mapstruct.Mapper;
import org.mapstruct.MapperConfig;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

import java.util.ArrayList;
import java.util.List;

@Mapper
@MapperConfig
public interface ParamConfigTransformer {

	/**
	 * 实例
	 */
	ParamConfigTransformer INSTANCE = Mappers.getMapper(ParamConfigTransformer.class);

	/*
	 * @Mapping( source =
	 * "java(com.renpho.erp.smc.domain.systemsetting.param.ParamStatus.enumOf( paramConfigPo.getStatus()))"
	 * , target = "status")
	 *
	 * @Mapping( source =
	 * "java(com.renpho.erp.smc.domain.systemsetting.param.ParamID.of( paramConfigPO.getId()))"
	 * , target = "id")
	 */
	@Mapping(target = "id", expression = "java(mapParamConfigID(paramConfig.getId()))")
	@Mapping(target = "status", expression = "java(mapParamConfigStatus(paramConfig.getStatus()))")
	default ParamConfigPO toParamConfigPO(ParamConfig paramConfig) {
		ParamConfigPO po = new ParamConfigPO();
		po.setId(mapParamConfigID(paramConfig.getId()));
		po.setParamKey(paramConfig.getKey());
		po.setParamName(paramConfig.getName());
		po.setRemark(paramConfig.getRemark());
		po.setStatus(mapParamConfigStatus(paramConfig.getStatus()));
		po.setParamValue(paramConfig.getValue());
		po.setDeleted(0);
		return po;
	};

	default Integer mapParamConfigID(ParamConfig.ParamID value) {
		if (value == null) {
			return null;
		}
		return value.getId();
	}

	default Integer mapParamConfigStatus(ParamStatus status) {
		return status.getValue();
	}

	default ParamConfig toParamConfig(ParamConfigPO paramConfigPO) {
		ParamConfig config = new ParamConfig(paramConfigPO.getParamName(), paramConfigPO.getParamKey(), paramConfigPO.getParamValue(),
				ParamStatus.enumOf(paramConfigPO.getStatus()), paramConfigPO.getRemark());
		return config;
	};

	default List<ParamConfig> toParamConfigList(List<ParamConfigPO> paramConfifPOS) {
		if (paramConfifPOS.isEmpty()) {
			return new ArrayList<>();
		}
		List<ParamConfig> paramConfigs = new ArrayList<>(paramConfifPOS.size());
		for (ParamConfigPO paramConfigPO : paramConfifPOS) {
			paramConfigs.add(INSTANCE.toParamConfig(paramConfigPO));
		}
		return paramConfigs;
	};

}
