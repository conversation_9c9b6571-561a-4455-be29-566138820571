package com.renpho.erp.smc.infrastructure.wecom;

import cn.felord.WeComTokenCacheable;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;

import java.util.concurrent.TimeUnit;

/**
 * Implementation of WeComTokenCacheable using Redis for caching access tokens.
 *
 * <AUTHOR>
 */
@Component
public class RedisTemplateWeComTokenCacheable implements WeComTokenCacheable {

	private static final String ACCESS_TOKEN_KEY_TEMPLATE = "token::qywx::%s::%s";

	private static final String AGENT_TICKET_KEY_TEMPLATE = "ticket::qywx::%s::%s";

	private static final String CORP_TICKET_KEY_TEMPLATE = "ticket::qywx::corp::%s::%s";

	@Autowired
	private StringRedisTemplate redisTemplate;

	// 实现 WeComTokenCacheable 方法

	@Override
	public String putAccessToken(String corpId, String agentId, String accessToken) {
		String key = String.format(ACCESS_TOKEN_KEY_TEMPLATE, corpId, agentId);
		redisTemplate.opsForValue().set(key, accessToken, 2, TimeUnit.HOURS); // 设置过期时间为2小时
		return accessToken;
	}

	@Override
	public String getAccessToken(String corpId, String agentId) {
		String key = String.format(ACCESS_TOKEN_KEY_TEMPLATE, corpId, agentId);
		return redisTemplate.opsForValue().get(key);
	}

	@Override
	public void clearAccessToken(String corpId, String agentId) {
		String key = String.format(ACCESS_TOKEN_KEY_TEMPLATE, corpId, agentId);
		redisTemplate.delete(key);
	}

	// 企业 jsapi_ticket 相关方法

	@Override
	public String putCorpTicket(String corpId, String agentId, String corpTicket) {
		String key = String.format(CORP_TICKET_KEY_TEMPLATE, corpId, agentId);
		// 设置过期时间为2小时
		redisTemplate.opsForValue().set(key, corpTicket, 2, TimeUnit.HOURS);
		return corpTicket;
	}

	@Override
	public String getCorpTicket(String corpId, String agentId) {
		String key = String.format(CORP_TICKET_KEY_TEMPLATE, corpId, agentId);
		return redisTemplate.opsForValue().get(key);
	}

	@Override
	public String putAgentTicket(String corpId, String agentId, String agentTicket) {
		String key = String.format(AGENT_TICKET_KEY_TEMPLATE, corpId, agentId);
		// 设置过期时间为1小时
		redisTemplate.opsForValue().set(key, agentTicket, 1, TimeUnit.HOURS);
		return agentTicket;
	}

	@Override
	public String getAgentTicket(String corpId, String agentId) {
		String key = String.format(AGENT_TICKET_KEY_TEMPLATE, corpId, agentId);
		return redisTemplate.opsForValue().get(key);
	}

}