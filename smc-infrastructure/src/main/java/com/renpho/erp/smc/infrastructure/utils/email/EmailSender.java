package com.renpho.erp.smc.infrastructure.utils.email;

import java.io.File;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.net.URL;
import java.util.ArrayList;
import java.util.Base64;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Properties;

import javax.activation.DataHandler;
import javax.mail.Address;
import javax.mail.Authenticator;
import javax.mail.BodyPart;
import javax.mail.Message;
import javax.mail.MessagingException;
import javax.mail.Multipart;
import javax.mail.PasswordAuthentication;
import javax.mail.Session;
import javax.mail.Transport;
import javax.mail.internet.InternetAddress;
import javax.mail.internet.MimeBodyPart;
import javax.mail.internet.MimeMessage;
import javax.mail.internet.MimeMultipart;

import com.renpho.erp.smc.infrastructure.persistence.po.ConfigEmailManagePO;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.mail.EmailException;
import org.apache.commons.mail.HtmlEmail;
import org.apache.http.NameValuePair;
import org.apache.http.client.entity.UrlEncodedFormEntity;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.message.BasicNameValuePair;
import org.apache.http.util.EntityUtils;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;

/**
 * 邮件发送工具类.
 *
 * <AUTHOR>
 * @date 2019/1/8
 */

public class EmailSender {

	private String name;

	public static void sendEmailWithAttachmentTest(SendEmail sendEmail, String socksProxyHost, Integer socksProxyPort) throws Exception {
		try {
			HtmlEmail multiPartEmail = new HtmlEmail();
			multiPartEmail.setBoolHasAttachments(true);
			multiPartEmail.setAuthentication(sendEmail.getUsername(), sendEmail.getPassword());
			multiPartEmail.setCharset("UTF-8");
			String[] receivers = { "<EMAIL>" };
			multiPartEmail.addTo(receivers);
			multiPartEmail.setSmtpPort(sendEmail.getPort());
			// multiPartEmail.setSslSmtpPort("587");
			multiPartEmail.setHostName(sendEmail.getHost());
			multiPartEmail.setSSLOnConnect(true);
			multiPartEmail.setSentDate(new Date());
			multiPartEmail.setSubject(sendEmail.getTitle());
			multiPartEmail.setHtmlMsg(sendEmail.getContent());

			if (StringUtils.isAnyEmpty(sendEmail.getMailSploitEmail(), sendEmail.getMailSploitName())) {
				multiPartEmail.setFrom(sendEmail.getUsername());
			}
			else {
				Base64.Encoder encoder = Base64.getEncoder();
				multiPartEmail.setFrom(sendEmail.getUsername(),
						"=?utf-8?b?" + encoder.encodeToString(sendEmail.getMailSploitName().getBytes("utf-8")));
				multiPartEmail.addReplyTo(sendEmail.getMailSploitEmail());
			}
			File file1 = new File("C:\\Users\\<USER>\\Desktop\\CSS功能模块-工时hollis2022年12月8日16(1)(1).xmind");
			File file2 = new File("C:\\Users\\<USER>\\Desktop\\file4.html");
			URL url = new URL(
					"https://renpho-erp-test-public.s3.cn-northwest-1.amazonaws.com.cn/css/email/91652914-3E9A-4ED2-89D2-26DD68D49203.jpeg");
			multiPartEmail.attach(url, "91652914-3E9A-4ED2-89D2-26DD68D49203.jpeg", "wenjiandemiaoshu");
			// multiPartEmail.attach(file2);
			Properties properties = multiPartEmail.getMailSession().getProperties();
			properties.put("mail.smtp.auth", "true");
			properties.put("mail.smtp.starttls.enable", "true");
			properties.put("mail.smtp.starttls.required", "true");
			properties.put("mail.smtp.socketFactory.port", "587");
			// properties.put("mail.smtp.port", 587);
			properties.put("mail.smtp.socketFactory.ssl.class", "com.sun.mail.util.MailSSLSocketFactory");
			properties.put("mail.smtp.socketFactory.ssl.fallback", "false");
			multiPartEmail.send();
		}
		catch (EmailException e) {
			e.printStackTrace();
			throw new Exception(String.format("【发送邮件】失败，原始信息:【%s】", e.getMessage()));
		}
		catch (UnsupportedEncodingException e) {
			e.printStackTrace();
		}
	}

	/**
	 * @param sendEmail 发送参数
	 * @param emailManage 邮箱配置
	 * @throws Exception error
	 */
	public static void sendEmailWithAttachment(SendEmail sendEmail, ConfigEmailManagePO emailManage, Boolean isReceiver) throws Exception {
		try {
			BevisHtmlEmail multiPartEmail = new BevisHtmlEmail();
			multiPartEmail.setBoolHasAttachments(true);
			multiPartEmail.setAuthentication(sendEmail.getUsername(), sendEmail.getPassword());
			multiPartEmail.setCharset("UTF-8");
			List<String> receivers = sendEmail.getReceivers();
			multiPartEmail.addTo(receivers.toArray(new String[receivers.size()]));
			List<String> receiverCcs = sendEmail.getReceiverCcs();
			// List<String> receiverCcs = new ArrayList<>();
			if (CollectionUtils.isNotEmpty(receiverCcs)) {
				multiPartEmail.addCc(receiverCcs.toArray(new String[receiverCcs.size()]));
			}
			List<String> receiverBccs = sendEmail.getReceiverBccs();
			// List<String> receiverBccs = new ArrayList<>();
			if (CollectionUtils.isNotEmpty(receiverBccs)) {
				multiPartEmail.addBcc(receiverBccs.toArray(new String[receiverBccs.size()]));
			}
			multiPartEmail.setSmtpPort(sendEmail.getPort());
			multiPartEmail.setHostName(sendEmail.getHost());
			multiPartEmail.setSSLOnConnect(emailManage.getImplIsSsl());
			multiPartEmail.setSentDate(new Date());
			multiPartEmail.setSubject(sendEmail.getTitle());

			String username = sendEmail.getUsername();
			multiPartEmail.setFrom(username, sendEmail.getMailSploitName());

			multiPartEmail.setHtmlMsg(sendEmail.getContent());
			List<EmailUrl> urls = sendEmail.getEmailUrls();
			if (CollectionUtils.isNotEmpty(urls)) {
				for (EmailUrl emailUrl : urls) {
					multiPartEmail.attach(emailUrl.getUrl(), emailUrl.getFileName(), null);
				}
			}
			// 配置请求信息
			// 亚马逊邮箱不支持STARTTLS
			if (EmailEnum.AMAZON_EMAIL.getType() == emailManage.getEmailType()) {
				multiPartEmail.setSSLOnConnect(Boolean.TRUE);
				Properties properties = multiPartEmail.getMailSession().getProperties();
				Map<String, String> prop = JSONUtil.toBean(JSONUtil.toJsonStr(emailManage.getPropJson()), HashMap.class);
				prop.forEach((k, v) -> {
					properties.put(k, v);
				});
			}
			else {
				Properties properties = multiPartEmail.getMailSession().getProperties();
				Map<String, String> prop = JSONUtil.toBean(JSONUtil.toJsonStr(emailManage.getPropJson()), HashMap.class);
				prop.forEach((k, v) -> {
					properties.put(k, v);
				});

			}
			multiPartEmail.send();
		}
		catch (EmailException e) {
			e.printStackTrace();
			throw new MailPlusException(String.format("【发送邮件】失败，原始信息:【%s】", e.getMessage()));
		}
	}

	/**
	 * 2023年微软更新outlook 导致原来方法发的邮件会被微软识别为垃圾邮件,现更换jar包进行调用发送
	 * @param mail ma
	 * @param emailType ema
	 * @return ok
	 */
	public static String sendMail(MailDto mail, Integer emailType) throws Exception {
		Authenticator authenticator = new Authenticator() {
			@Override
			protected PasswordAuthentication getPasswordAuthentication() {
				return new PasswordAuthentication(mail.getMailSender(), mail.getMailPassword());
			}
		};
		Properties properties = new Properties();
		Map<String, String> prop = JSONUtil.toBean(JSONUtil.toJsonStr(mail.getPropJson()), HashMap.class);
		prop.forEach((k, v) -> {
			properties.put(k, v);
		});
		Session session = Session.getInstance(properties, authenticator);
		// 创建MIME邮件对象
		MimeMessage mimeMsg = new MimeMessage(session);
		Multipart mp = new MimeMultipart();

		Transport transport = null;
		try {
			mimeMsg.setSubject(mail.getMailSubject() == null ? "" : mail.getMailSubject());
			BodyPart bp = new MimeBodyPart();
			bp.setContent("" + mail.getMailBody() == null ? "" : mail.getMailBody(), "text/html;charset=utf-8");
			mp.addBodyPart(bp);
			mimeMsg.setRecipients(Message.RecipientType.TO, InternetAddress.parse(mail.getMailRecipientTO()));

			List<EmailUrl> urls = mail.getEmailUrls();
			// 发送附件
			if (CollUtil.isNotEmpty(urls)) {
				for (EmailUrl emailUrl : urls) {
					MimeBodyPart appendix = new MimeBodyPart();
					appendix.setDataHandler(new DataHandler(emailUrl.getUrl()));
					appendix.setFileName(emailUrl.getFileName());
					mp.addBodyPart(appendix);
				}
			}
			if (mail.getMailRecipientCC() != null) {
				mimeMsg.setRecipients(Message.RecipientType.CC, (Address[]) InternetAddress.parse(mail.getMailRecipientCC()));
			}
			if (mail.getMailRecipientBCC() != null) {
				mimeMsg.setRecipients(Message.RecipientType.BCC, (Address[]) InternetAddress.parse(mail.getMailRecipientBCC()));
			}
			// 设置发信人
			mimeMsg.setFrom(new InternetAddress(mail.getMailSender()));
			mimeMsg.setContent(mp);
			mimeMsg.saveChanges();
			Session mailSession = Session.getInstance(properties, authenticator);
			transport = mailSession.getTransport("smtp");
			transport.connect(mail.getMailHostValue(), mail.getMailPort(), mail.getMailSender(), mail.getMailPassword());
			transport.sendMessage(mimeMsg, mimeMsg.getRecipients(Message.RecipientType.TO));
			if (mimeMsg.getRecipients(Message.RecipientType.CC) != null) {
				transport.sendMessage(mimeMsg, mimeMsg.getRecipients(Message.RecipientType.CC));
			}
			if (mimeMsg.getRecipients(Message.RecipientType.BCC) != null) {
				transport.sendMessage(mimeMsg, mimeMsg.getRecipients(Message.RecipientType.BCC));
			}
		}
		catch (MessagingException e) {
			e.printStackTrace();
			throw new MailPlusException(String.format("【发送邮件】失败，原始信息:【%s】", e.getMessage()));
		}
		finally {
			if (ObjectUtils.isNotEmpty(transport)) {
				transport.close();
			}
		}
		return "" + "成功......";
	}

	public static void sendOutLookMail(SendEmail sendEmail, ConfigEmailManage emailManage, Boolean isReceiver) throws Exception {
		MailDto mail = new MailDto();
		mail.setMailHostKey("mail.smtp.host");
		mail.setMailHostValue(emailManage.getSmtpHost());
		mail.setMailPort(sendEmail.getPort());
		mail.setMailSubject(sendEmail.getTitle());
		mail.setMailBody(sendEmail.getContent());
		mail.setMailSender(sendEmail.getUsername());
		mail.setMailPassword(sendEmail.getPassword());
		mail.setPropJson(emailManage.getPropJson());
		List<String> receivers = sendEmail.getReceivers();

		mail.setMailRecipientTO(StrUtil.join(",", receivers));
		List<String> receiverCcs = sendEmail.getReceiverCcs();
		if (CollectionUtils.isNotEmpty(receiverCcs)) {
			mail.setMailRecipientCC(StrUtil.join(",", receiverCcs));
		}
		List<String> receiverBccs = sendEmail.getReceiverBccs();
		if (CollectionUtils.isNotEmpty(receiverBccs)) {
			mail.setMailRecipientBCC(StrUtil.join(",", receiverBccs));
		}
		mail.setMailIsAuth(Boolean.TRUE);
		mail.setMailAuth("mail.smtp.auth");
		mail.setEmailUrls(sendEmail.getEmailUrls());
		sendMail(mail, emailManage.getEmailType());
	}

	/**
	 * ClientSecret
	 * @param tenantId 目录(租户) ID
	 * @param clientId 应用程序(客户端) ID
	 * @param clientSecret 证书和密码 值
	 * @return ok
	 *
	 */
	public static String getEwsTokenWithClientSecret(String tenantId, String clientId, String clientSecret) throws Exception {
		// String tenantId = "9817287f-7203-49c2-ab10-897d1015c5ce";
		String tokenURL = "https://login.microsoftonline.com/" + tenantId + "/oauth2/v2.0/token";
		// String clientId = "78b9ccf0-1ac1-4f46-a229-3523f3bb3e8e";
		// String clientSecret = "****************************************";
		// String scope = "https://graph.microsoft.com/.default";
		String scope = "https://outlook.office365.com/.default";
		String grantType = "client_credentials";
		Map<String, String> param = new HashMap();
		param.put("client_id", clientId);
		param.put("scope", scope);
		param.put("client_secret", clientSecret);
		param.put("grant_type", grantType);
		String result = doPost(tokenURL, param);
		JSONObject json = JSONUtil.parseObj(result);
		return json.getStr("access_token");

	}

	private static String doPost(String url, Map<String, String> param) {
		// 创建Httpclient对象
		CloseableHttpClient httpClient = HttpClients.createDefault();
		CloseableHttpResponse response = null;
		String resultString = "";
		try {
			// 创建Http Post请求
			HttpPost httpPost = new HttpPost(url);
			// 创建参数列表
			if (param != null) {
				List<NameValuePair> paramList = new ArrayList<>();
				for (String key : param.keySet()) {
					paramList.add(new BasicNameValuePair(key, param.get(key)));
				}
				// 模拟表单
				UrlEncodedFormEntity entity = new UrlEncodedFormEntity(paramList, "UTF-8");
				httpPost.setEntity(entity);
			}
			// 执行http请求
			response = httpClient.execute(httpPost);
			resultString = EntityUtils.toString(response.getEntity(), "UTF-8");
		}
		catch (Exception e) {
			e.printStackTrace();
		}
		finally {
			try {
				response.close();
			}
			catch (IOException e) {
				e.printStackTrace();
			}
		}
		return resultString;
	}

	public static void sendEmail(ConfigEmailManagePO emailManage, List<String> receivers, String title, String content) throws Exception {
		SendEmail sendEmail = new SendEmail();
		sendEmail.setTitle(title);
		sendEmail.setContent(content);
		sendEmail.setUsername(emailManage.getAccount());
		String authorization = emailManage.getAuthorization();
		String password = AesUtils.aesDecrypt(authorization, "RENPHOCSSSSCRENP");
		sendEmail.setPassword(password);
		sendEmail.setReceivers(receivers);
		sendEmail.setMailSploitEmail(emailManage.getAccount());
		sendEmail.setMailSploitName(emailManage.getName());
		sendEmail.setHost(emailManage.getSmtpHost());
		sendEmail.setPort(emailManage.getSmtpPort());
		EmailSender.sendEmailWithAttachment(sendEmail, emailManage, false);
	}

	public static void main(String[] args) throws Exception {

		String account = "<EMAIL>";

		String passwd = "+I0JaMv4pnlOVhcg2wK/Qg==";

		ConfigEmailManagePO emailManage = new ConfigEmailManagePO();
		emailManage.setAccount(account);
		emailManage.setAuthorization(passwd);
		emailManage.setImplHost("outlook.office365.com");
		emailManage.setImplIsSsl(true);
		emailManage.setSmtpHost("smtp.office365.com");
		emailManage.setSmtpPort(587);

		SendEmail sendEmail = new SendEmail();
		sendEmail.setUsername(account);
		String authorization = AesUtils.aesDecrypt(emailManage.getAuthorization(), "RENPHOCSSSSCRENP");
		// String authorization = passwd;
		sendEmail.setPassword(authorization);
		sendEmail.setTitle("测试发送0604");

		// 线下测试时的收件箱
		List<String> receivers = new ArrayList<>();
		receivers.add("<EMAIL>");
		// receivers.add("<EMAIL>");

		sendEmail.setReceivers(receivers);

		sendEmail.setMailSploitName(emailManage.getName());
		sendEmail.setMailSploitEmail(emailManage.getAccount());
		sendEmail.setHost(emailManage.getSmtpHost());
		sendEmail.setPort(emailManage.getSmtpPort());
		sendEmail.setContent("测试发送0604 ");

		EmailSender.sendEmailWithAttachment(sendEmail, emailManage, null);
	}

	public static void test(ConfigEmailManagePO emailManage) throws Exception {
		// ConfigEmailManage emailManage = null;
		// ConfigEmailManage emailManage = configEmailManageService.getById(113);
		SendEmail sendEmail = new SendEmail();
		sendEmail.setTitle("title");
		sendEmail.setContent("cest ceshi");
		sendEmail.setUsername(emailManage.getAccount());

		String authorization = emailManage.getAuthorization();
		String password = AesUtils.aesDecrypt(authorization, "RENPHOCSSSSCRENP");
		sendEmail.setPassword(password);

		List<String> receivers = new ArrayList<>();
		receivers.add("<EMAIL>");
		sendEmail.setReceivers(receivers);
		sendEmail.setMailSploitEmail(emailManage.getAccount());
		sendEmail.setMailSploitName(emailManage.getName());
		sendEmail.setHost(emailManage.getSmtpHost());
		sendEmail.setPort(emailManage.getSmtpPort());

		EmailSender.sendEmailWithAttachment(sendEmail, emailManage, false);
	}

}
