package com.renpho.erp.smc.infrastructure.dingtalk;

import java.util.Collection;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.concurrent.TimeUnit;
import java.util.function.Supplier;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.BoundSetOperations;
import org.springframework.data.redis.core.HashOperations;
import org.springframework.data.redis.core.ListOperations;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.ValueOperations;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Component;
import org.springframework.util.ObjectUtils;

import cn.hutool.core.collection.CollectionUtil;
import lombok.extern.slf4j.Slf4j;

/**
 * spring redis 工具类.
 *
 * <AUTHOR>
 **/
@SuppressWarnings("unchecked")
@Slf4j
@Component
public class RedisService {

	@Autowired
	private RedisTemplate redisTemplate;

	@Autowired
	private ThreadPoolTaskExecutor threadPoolTaskExecutor;

	/**
	 * 缓存基本的对象，Integer、String、实体类等
	 * @param key 缓存的键值
	 * @param value 缓存的值
	 */
	public <T> void setCacheObject(final String key, final T value) {
		// log.debug("设置key{}", key);
		redisTemplate.opsForValue().set(key, value);
	}

	/**
	 * 缓存基本的对象，Integer、String、实体类等
	 * @param key 缓存的键值
	 * @param value 缓存的值
	 * @param timeout 时间
	 * @param timeUnit 时间颗粒度
	 */
	public <T> void setCacheObject(final String key, final T value, final Long timeout, final TimeUnit timeUnit) {
		// log.debug("设置key{}", key);
		redisTemplate.opsForValue().set(key, value, timeout, timeUnit);
	}

	/**
	 * 如果key不存在，缓存基本的对象，Integer、String、实体类等
	 * @param key 缓存的键值
	 * @param value 缓存的值
	 * @return 是否缓存成功
	 */
	public <T> Boolean setCacheObjectIfAbsent(final String key, final T value) {
		return redisTemplate.opsForValue().setIfAbsent(key, value);
	}

	/**
	 * 缓存基本的对象，Integer、String、实体类等
	 * @param key 缓存的键值
	 * @param value 缓存的值
	 * @param timeout 时间
	 * @param timeUnit 时间颗粒度
	 */
	public <T> void setCacheObject(final String key, final T value, final Integer timeout, final TimeUnit timeUnit) {
		// log.debug("设置key{}", key);
		redisTemplate.opsForValue().set(key, value, timeout, timeUnit);
	}

	/**
	 * 如果key不存在，则缓存基本的对象，Integer、String、实体类等
	 * @param key 缓存的键值
	 * @param value 缓存的值
	 * @param timeout 时间
	 * @param timeUnit 时间颗粒度
	 * @return 是否缓存成功
	 */
	public <T> Boolean setCacheObjectIfAbsent(final String key, final T value, final Integer timeout, final TimeUnit timeUnit) {
		// log.debug("设置key{}", key);
		return redisTemplate.opsForValue().setIfAbsent(key, value, timeout, timeUnit);
	}

	public <T> void rightPush(final String key, final T value) {
		redisTemplate.opsForList().rightPush(key, value);
	}

	public <T> T leftPop(final String key) {
		ListOperations<String, T> operations = redisTemplate.opsForList();
		return operations.leftPop(key);
	}

	public Long increment(final String key, final long num) {
		return redisTemplate.opsForValue().increment(key, num);
	}

	public Long decrement(final String key, final long num) {
		return redisTemplate.opsForValue().decrement(key, num);
	}

	public boolean expire(final String key, final long timeout) {
		return expire(key, timeout, TimeUnit.SECONDS);
	}

	public boolean expire(final String key, final long timeout, final TimeUnit unit) {
		return redisTemplate.expire(key, timeout, unit);
	}

	public long getExpire(final String key) {
		return redisTemplate.getExpire(key);
	}

	public Boolean hasKey(String key) {
		return redisTemplate.hasKey(key);
	}

	public <T> T getCacheObject(final String key) {
		ValueOperations<String, T> operation = redisTemplate.opsForValue();
		return operation.get(key);
	}

	public boolean deleteObject(final String key) {
		return redisTemplate.delete(key);
	}

	public long deleteObject(final Collection collection) {
		return redisTemplate.delete(collection);
	}

	public <T> long setCacheList(final String key, final List<T> dataList) {
		Long count = redisTemplate.opsForList().rightPushAll(key, dataList);
		return count == null ? 0 : count;
	}

	public <T> List<T> getCacheList(final String key) {
		return redisTemplate.opsForList().range(key, 0, -1);
	}

	public <T> BoundSetOperations<String, T> setCacheSet(final String key, final Set<T> dataSet) {
		BoundSetOperations<String, T> setOperation = redisTemplate.boundSetOps(key);
		Iterator<T> it = dataSet.iterator();
		while (it.hasNext()) {
			setOperation.add(it.next());
		}
		return setOperation;
	}

	public <T> void delCacheSet(String key, Set<T> dataSet) {
		BoundSetOperations<String, T> setOperation = redisTemplate.boundSetOps(key);
		for (T t : dataSet) {
			setOperation.remove(t);
		}
	}

	public <T> Set<T> getCacheSet(final String key) {
		return redisTemplate.opsForSet().members(key);
	}

	public <T> void setCacheMap(final String key, final Map<String, T> dataMap) {
		if (dataMap != null) {
			redisTemplate.opsForHash().putAll(key, dataMap);
		}
	}

	public <T> Map<String, T> getCacheMap(final String key) {
		return redisTemplate.opsForHash().entries(key);
	}

	public <T> void setCacheMapValue(final String key, final String hKey, final T value) {
		redisTemplate.opsForHash().put(key, hKey, value);
	}

	/**
	 * 获取Hash中的数据
	 * @param key Redis键
	 * @param hKey Hash键
	 * @return Hash中的对象
	 */
	public <T> T getCacheMapValue(final String key, final String hKey) {
		HashOperations<String, String, T> opsForHash = redisTemplate.opsForHash();
		return opsForHash.get(key, hKey);
	}

	/**
	 * 获取多个Hash中的数据
	 * @param key Redis键
	 * @param hKeys Hash键集合
	 * @return Hash对象集合
	 */
	public <T> List<T> getMultiCacheMapValue(final String key, final Collection<Object> hKeys) {
		return redisTemplate.opsForHash().multiGet(key, hKeys);
	}

	/**
	 * 删除Hash中的某条数据
	 * @param key Redis键
	 * @param hKey Hash键
	 * @return 是否成功
	 */
	public boolean deleteCacheMapValue(final String key, final String hKey) {
		return redisTemplate.opsForHash().delete(key, hKey) > 0;
	}

	/**
	 * 获得缓存的基本对象列表
	 * @param pattern 字符串前缀
	 * @return 对象列表
	 */
	public Collection<String> keys(final String pattern) {
		return redisTemplate.keys(pattern);
	}

	/**
	 * 获取key下所有数据，并删除
	 * @param pattern 字符串前缀
	 * @return 对象列表
	 */
	public Long deleteKeys(final String pattern) {
		Set keys = redisTemplate.keys(pattern);
		if (CollectionUtil.isEmpty(keys)) {
			return 0L;
		}
		return redisTemplate.delete(keys);
	}

	private static final String EMPTY_CHAR = "[]";

	private static final Long EMPTY_CHAR_TIMEOUT = 300L;

	/**
	 * 获取reids缓存，存在则返回，不存在则查数据库
	 * @param key rediskey
	 * @return 对象列表
	 */
	public <T> T getRedisCache(String key, Supplier supplier) {
		return this.getRedisCache(key, supplier, 3600L, TimeUnit.SECONDS);
	}

	/**
	 * 获取reids缓存，存在则返回，不存在则查数据库
	 * @param key rediskey
	 * @return 对象列表
	 */
	public <T> T getRedisCache(final String key, Supplier supplier, final Long timeout, final TimeUnit unit) {
		T obj = getCacheObject(key);
		if (Objects.nonNull(obj)) {
			if (obj instanceof String && EMPTY_CHAR.equals(obj)) {
				return null;
			}

			return obj;
		}

		obj = (T) supplier.get();
		if (!ObjectUtils.isEmpty(obj)) {
			setCacheObject(key, obj, timeout, unit);
		}
		else {
			// 设置默认空字符，避免大量请求打入数据库，造成数据库宕机
			setCacheObject(key, EMPTY_CHAR, EMPTY_CHAR_TIMEOUT, TimeUnit.SECONDS);
		}

		return obj;
	}

	public <T> T deleteObject(String key, Supplier supplier) {
		if (this.hasKey(key)) {
			this.deleteObject(key);
		}
		T t = (T) supplier.get();
		threadPoolTaskExecutor.execute(() -> {
			try {
				Thread.sleep(1000);
			}
			catch (InterruptedException e) {
				throw new RuntimeException(e);
			}
			if (this.hasKey(key)) {
				this.deleteObject(key);
			}
		});
		return t;
	}

	public boolean haskey(String key) {
		return Boolean.TRUE.equals(redisTemplate.hasKey(key));
	}

}
