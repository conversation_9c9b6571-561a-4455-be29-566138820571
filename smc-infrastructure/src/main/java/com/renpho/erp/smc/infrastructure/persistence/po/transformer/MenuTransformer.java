package com.renpho.erp.smc.infrastructure.persistence.po.transformer;

import com.renpho.erp.smc.domain.systemsetting.menu.Menu;
import com.renpho.erp.smc.infrastructure.persistence.po.MenuInfoPO;
import org.mapstruct.Mapper;
import org.mapstruct.MapperConfig;
import org.mapstruct.factory.Mappers;

/**
 * <AUTHOR>
 */
@Mapper
@MapperConfig
public interface MenuTransformer {

	/**
	 * The mapper instance.
	 */
	MenuTransformer INSTANCE = Mappers.getMapper(MenuTransformer.class);

	/**
	 * Map a menu to a menu info po.
	 * @param menu the menu
	 * @return the menu info po
	 */
	MenuInfoPO toMenuInfoPO(Menu menu);

	/**
	 * Map a menu id to a menu info po.
	 * @param value the menu id
	 * @return the menu info po
	 */
	default Integer mapMenuID(Menu.MenuID value) {
		if (value == null) {
			return null;
		}
		return value.getId();
	}

	Menu toMenu(MenuInfoPO menuInfoPO);

	default Menu.MenuID mapIdToMenuId(Integer value) {
		if (value == null) {
			return null;
		}
		return Menu.MenuID.of(value);
	}

}
