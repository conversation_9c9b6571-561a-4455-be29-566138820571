package com.renpho.erp.smc.infrastructure.enums;

import lombok.Getter;

/**
 * 第三方账号同步操作类型枚举
 *
 * <AUTHOR>
 * @Date 2025/6/26 14:44
 **/
@Getter
public enum ThirdAccountSyncOperationTypeEnum {

	/**
	 * 新增
	 */
	ADD("ADD", "新增"),

	/**
	 * 更新
	 */
	UPDATE("UPDATE", "更新"),

	/**
	 * 删除
	 */
	DELETE("DELETE", "删除");

	/**
	 * 代码
	 */
	private final String code;

	/**
	 * 描述
	 */
	private final String desc;

	/**
	 * 注意类型枚举
	 * @param code 代码
	 * @param desc 描述
	 */
	ThirdAccountSyncOperationTypeEnum(String code, String desc) {
		this.code = code;
		this.desc = desc;
	}

	/**
	 * 根据编码获取枚举
	 * @return com.renpho.erp.smc.infrastructure.enums.AccountTypeEnum
	 * <AUTHOR>
	 * @Date 14:45 2025/6/26
	 * @Param [code]
	 **/
	public static ThirdAccountSyncOperationTypeEnum getByCode(String code) {
		for (ThirdAccountSyncOperationTypeEnum type : ThirdAccountSyncOperationTypeEnum.values()) {
			if (type.getCode().equals(code)) {
				return type;
			}
		}
		return null;
	}

}
