// package com.renpho.erp.smc.infrastructure.teams;
//
// import org.springframework.beans.factory.annotation.Value;
// import org.springframework.context.annotation.Bean;
// import org.springframework.context.annotation.Configuration;
//
// @Configuration
// public class TokenCredentialFactoryConfig {
//
// @Bean
// public TokenCredentialFactory tokenCredentialFactory(@Value("${teams.clientId") String
// clientId,
// @Value("${teams.clientSecret}") String clientSecret, @Value("${teams.tenantId}") String
// tenantId) {
// return new DefaultTokenCredentialFactory(clientId, clientSecret, tenantId);
// }
//
// }