package com.renpho.erp.smc.infrastructure.utils.excel;

import java.io.UnsupportedEncodingException;
import java.lang.reflect.Field;
import java.lang.reflect.Method;
import java.math.BigDecimal;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.text.DecimalFormat;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

import com.renpho.erp.smc.infrastructure.utils.common.Convert;
import com.renpho.erp.smc.infrastructure.utils.common.DateUtils;
import com.renpho.erp.smc.infrastructure.utils.common.StringUtils;
import com.renpho.erp.smc.infrastructure.utils.common.file.FileTypeUtils;
import com.renpho.erp.smc.infrastructure.utils.common.file.ImageUtils;
import com.renpho.erp.smc.infrastructure.utils.common.poi.Excel;
import com.renpho.erp.smc.infrastructure.utils.common.poi.Excel.ColumnType;
import com.renpho.erp.smc.infrastructure.utils.common.poi.Excel.Type;
import com.renpho.erp.smc.infrastructure.utils.common.poi.ExcelHandlerAdapter;
import com.renpho.erp.smc.infrastructure.utils.common.poi.Excels;

import org.apache.commons.lang3.ArrayUtils;
import org.apache.commons.lang3.RegExUtils;
import org.apache.poi.ss.usermodel.BorderStyle;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.CellStyle;
import org.apache.poi.ss.usermodel.CellType;
import org.apache.poi.ss.usermodel.ClientAnchor;
import org.apache.poi.ss.usermodel.DataValidation;
import org.apache.poi.ss.usermodel.DataValidationConstraint;
import org.apache.poi.ss.usermodel.DataValidationHelper;
import org.apache.poi.ss.usermodel.DateUtil;
import org.apache.poi.ss.usermodel.Drawing;
import org.apache.poi.ss.usermodel.FillPatternType;
import org.apache.poi.ss.usermodel.Font;
import org.apache.poi.ss.usermodel.HorizontalAlignment;
import org.apache.poi.ss.usermodel.IndexedColors;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.VerticalAlignment;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.ss.util.CellRangeAddress;
import org.apache.poi.ss.util.CellRangeAddressList;
import org.apache.poi.util.IOUtils;
import org.apache.poi.xssf.streaming.SXSSFWorkbook;
import org.apache.poi.xssf.usermodel.XSSFClientAnchor;
import org.apache.poi.xssf.usermodel.XSSFDataValidation;

import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;

/**
 * Excel相关处理.
 *
 * <AUTHOR>
 */
@Slf4j
public final class ExcelUtil<T> {

	public ExcelUtil(Class<T> clazz) {
		this.clazz = clazz;
	}

	/**
	 * 1
	 */
	public static final String FORMULA_REGEX_STR = "=|-|\\+|@";

	/**
	 * 2
	 */
	public static final String[] FORMULA_STR = { "=", "-", "+", "@" };

	/**
	 * 工作表名称
	 */
	private String sheetName;

	/**
	 * 导出类型（EXPORT:导出数据；IMPORT：导入模板）
	 */
	private Type type;

	/**
	 * 工作薄对象
	 */
	private Workbook wb;

	/**
	 * 工作表对象
	 */
	private Sheet sheet;

	/**
	 * 样式列表
	 */
	private Map<String, CellStyle> styles;

	/**
	 * 导入导出数据列表
	 */
	private List<T> list;

	/**
	 * 注解列表
	 */
	private List<Object[]> fields;

	/**
	 * 当前行号
	 */
	private int rownum;

	/**
	 * 标题
	 */
	private String title;

	/**
	 * 最大高度
	 */
	private short maxHeight;

	/**
	 * 统计列表
	 */
	private Map<Integer, Double> statistics = new HashMap<Integer, Double>();

	/**
	 * 数字格式
	 */
	private static final DecimalFormat DOUBLE_FORMAT = new DecimalFormat("######0.00");

	/**
	 * 实体对象
	 */
	public Class<T> clazz;

	/**
	 * 需要排除列属性
	 */
	public String[] excludeFields;

	/**
	 * 隐藏Excel中列属性
	 */
	public void hideColumn(String... fields) {
		this.excludeFields = fields;
	}

	public void init(List<T> list, String sheetName, String title, Type type) {
		if (list == null) {
			list = new ArrayList<T>();
		}
		this.list = list;
		this.sheetName = sheetName;
		this.type = type;
		this.title = title;
		createExcelField();
		createWorkbook();
		createTitle();
	}

	/**
	 * 创建excel第一行标题
	 */
	public void createTitle() {
		if (StringUtils.isNotEmpty(title)) {
			Row titleRow = sheet.createRow(rownum == 0 ? rownum++ : 0);
			titleRow.setHeightInPoints(30);
			Cell titleCell = titleRow.createCell(0);
			titleCell.setCellStyle(styles.get("title"));
			titleCell.setCellValue(title);
			sheet.addMergedRegion(
					new CellRangeAddress(titleRow.getRowNum(), titleRow.getRowNum(), titleRow.getRowNum(), this.fields.size() - 1));
		}
	}

	/**
	 * 对list数据源将其里面的数据导入到excel表单
	 * @param response 返回数据
	 * @param sheetName 工作表的名称
	 */
	public void exportExcel(HttpServletResponse response, List<T> list, String sheetName) {
		exportExcel(response, list, sheetName, StringUtils.EMPTY);
	}

	/**
	 * 对list数据源将其里面的数据导入到excel表单
	 * @param response 返回数据
	 * @param list 导出数据集合
	 * @param sheetName 工作表的名称
	 * @param title 标题
	 */
	public void exportExcel(HttpServletResponse response, List<T> list, String sheetName, String title) {
		response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
		response.setCharacterEncoding("utf-8");
		this.init(list, sheetName, title, Type.EXPORT);
		exportExcel(response);
	}

	/**
	 * 对list数据源将其里面的数据导入到excel表单
	 */
	public void exportExcel(HttpServletResponse response) {
		try {
			writeSheet();
			wb.write(response.getOutputStream());
		}
		catch (Exception e) {
			log.error("导出Excel异常{}", e.getMessage());
		}
		finally {
			IOUtils.closeQuietly(wb);
		}
	}

	/**
	 * 创建写入数据到Sheet
	 */
	public void writeSheet() {
		// 取出一共有多少个sheet.
		int sheetNo = Math.max(1, (int) Math.ceil(list.size() * 1.0 / 65536));
		for (int index = 0; index < sheetNo; index++) {
			createSheet(sheetNo, index);

			// 产生一行
			Row row = sheet.createRow(rownum);
			int column = 0;
			// 写入各个字段的列头名称
			for (Object[] os : fields) {
				Excel excel = (Excel) os[1];
				this.createCell(excel, row, column++);
			}
			if (Type.EXPORT.equals(type)) {
				fillExcelData(index, row);
				addStatisticsRow();
			}
		}
	}

	/**
	 * 填充excel数据
	 * @param index 序号
	 * @param row 单元格行
	 */
	public void fillExcelData(int index, Row row) {
		int startNo = index * 65536;
		int endNo = Math.min(startNo + 65536, list.size());
		for (int i = startNo; i < endNo; i++) {
			row = sheet.createRow(i + 1 + rownum - startNo);
			// 得到导出对象.
			T vo = (T) list.get(i);
			int column = 0;
			for (Object[] os : fields) {
				Field field = (Field) os[0];
				Excel excel = (Excel) os[1];
				this.addCell(excel, row, vo, field, column++);
			}
		}
	}

	/**
	 * 创建表格样式
	 * @param wb 工作薄对象
	 * @return 样式列表
	 */
	private Map<String, CellStyle> createStyles(Workbook wb) {
		// 写入各条记录,每条记录对应excel表中的一行
		Map<String, CellStyle> styles = new HashMap<>();
		CellStyle style = wb.createCellStyle();
		style.setAlignment(HorizontalAlignment.CENTER);
		style.setVerticalAlignment(VerticalAlignment.CENTER);
		Font titleFont = wb.createFont();
		titleFont.setFontName("Arial");
		titleFont.setFontHeightInPoints((short) 16);
		titleFont.setBold(true);
		style.setFont(titleFont);
		styles.put("title", style);

		style = wb.createCellStyle();
		style.setAlignment(HorizontalAlignment.CENTER);
		style.setVerticalAlignment(VerticalAlignment.CENTER);
		style.setBorderRight(BorderStyle.THIN);
		style.setRightBorderColor(IndexedColors.GREY_50_PERCENT.getIndex());
		style.setBorderLeft(BorderStyle.THIN);
		style.setLeftBorderColor(IndexedColors.GREY_50_PERCENT.getIndex());
		style.setBorderTop(BorderStyle.THIN);
		style.setTopBorderColor(IndexedColors.GREY_50_PERCENT.getIndex());
		style.setBorderBottom(BorderStyle.THIN);
		style.setBottomBorderColor(IndexedColors.GREY_50_PERCENT.getIndex());
		Font dataFont = wb.createFont();
		dataFont.setFontName("Arial");
		dataFont.setFontHeightInPoints((short) 10);
		style.setFont(dataFont);
		styles.put("data", style);

		style = wb.createCellStyle();
		style.setAlignment(HorizontalAlignment.CENTER);
		style.setVerticalAlignment(VerticalAlignment.CENTER);
		Font totalFont = wb.createFont();
		totalFont.setFontName("Arial");
		totalFont.setFontHeightInPoints((short) 10);
		style.setFont(totalFont);
		styles.put("total", style);

		styles.putAll(annotationHeaderStyles(wb, styles));
		styles.putAll(annotationDataStyles(wb));

		return styles;
	}

	/**
	 * 根据Excel注解创建表格头样式
	 * @param wb 工作薄对象
	 * @return 自定义样式列表
	 */
	private Map<String, CellStyle> annotationHeaderStyles(Workbook wb, Map<String, CellStyle> styles) {
		Map<String, CellStyle> headerStyles = new HashMap<>();
		for (Object[] os : fields) {
			Excel excel = (Excel) os[1];
			String key = StringUtils.format("header_{}_{}", excel.headerColor(), excel.headerBackgroundColor());
			if (!headerStyles.containsKey(key)) {
				CellStyle style;
				style = wb.createCellStyle();
				style.cloneStyleFrom(styles.get("data"));
				style.setAlignment(HorizontalAlignment.CENTER);
				style.setVerticalAlignment(VerticalAlignment.CENTER);
				style.setFillForegroundColor(excel.headerBackgroundColor().index);
				style.setFillPattern(FillPatternType.SOLID_FOREGROUND);
				Font headerFont = wb.createFont();
				headerFont.setFontName("Arial");
				headerFont.setFontHeightInPoints((short) 10);
				headerFont.setBold(true);
				headerFont.setColor(excel.headerColor().index);
				style.setFont(headerFont);
				headerStyles.put(key, style);
			}
		}
		return headerStyles;
	}

	/**
	 * 根据Excel注解创建表格列样式
	 * @param wb 工作薄对象
	 * @return 自定义样式列表
	 */
	private Map<String, CellStyle> annotationDataStyles(Workbook wb) {
		Map<String, CellStyle> styles = new HashMap<>();
		for (Object[] os : fields) {
			Excel excel = (Excel) os[1];
			String key = StringUtils.format("data_{}_{}_{}", excel.align(), excel.color(), excel.backgroundColor());
			if (!styles.containsKey(key)) {
				CellStyle style;
				style = wb.createCellStyle();
				style.setAlignment(excel.align());
				style.setVerticalAlignment(VerticalAlignment.CENTER);
				style.setBorderRight(BorderStyle.THIN);
				style.setRightBorderColor(IndexedColors.GREY_50_PERCENT.getIndex());
				style.setBorderLeft(BorderStyle.THIN);
				style.setLeftBorderColor(IndexedColors.GREY_50_PERCENT.getIndex());
				style.setBorderTop(BorderStyle.THIN);
				style.setTopBorderColor(IndexedColors.GREY_50_PERCENT.getIndex());
				style.setBorderBottom(BorderStyle.THIN);
				style.setBottomBorderColor(IndexedColors.GREY_50_PERCENT.getIndex());
				style.setFillPattern(FillPatternType.SOLID_FOREGROUND);
				style.setFillForegroundColor(excel.backgroundColor().getIndex());
				Font dataFont = wb.createFont();
				dataFont.setFontName("Arial");
				dataFont.setFontHeightInPoints((short) 10);
				dataFont.setColor(excel.color().index);
				style.setFont(dataFont);
				styles.put(key, style);
			}
		}
		return styles;
	}

	/**
	 * 创建单元格
	 * @return 1
	 */
	public Cell createCell(Excel attr, Row row, int column) {
		// 创建列
		Cell cell = row.createCell(column);
		// 写入列信息
		cell.setCellValue(attr.name());
		setDataValidation(attr, column);
		cell.setCellStyle(styles.get(StringUtils.format("header_{}_{}", attr.headerColor(), attr.headerBackgroundColor())));
		return cell;
	}

	/**
	 * 设置单元格信息
	 * @param value 单元格值
	 * @param attr 注解相关
	 * @param cell 单元格信息
	 */
	public void setCellVo(Object value, Excel attr, Cell cell) {
		if (ColumnType.STRING == attr.cellType()) {
			String cellValue = Convert.toStr(value);
			// 对于任何以表达式触发字符 =-+@开头的单元格，直接使用tab字符作为前缀，防止CSV注入。
			if (StringUtils.startsWithAny(cellValue, FORMULA_STR)) {
				cellValue = RegExUtils.replaceFirst(cellValue, FORMULA_REGEX_STR, "\t$0");
			}
			cell.setCellValue(StringUtils.isNull(cellValue) ? attr.defaultValue() : cellValue + attr.suffix());
		}
		else if (ColumnType.NUMERIC == attr.cellType()) {
			if (StringUtils.isNotNull(value)) {
				cell.setCellValue(StringUtils.contains(Convert.toStr(value), ".") ? Convert.toDouble(value) : Convert.toInt(value));
			}
		}
		else if (ColumnType.IMAGE == attr.cellType()) {
			ClientAnchor anchor = new XSSFClientAnchor(0, 0, 0, 0, (short) cell.getColumnIndex(), cell.getRow().getRowNum(),
					(short) (cell.getColumnIndex() + 1), cell.getRow().getRowNum() + 1);
			String imagePath = Convert.toStr(value);
			if (StringUtils.isNotEmpty(imagePath)) {
				byte[] data = ImageUtils.getImage(imagePath);
				getDrawingPatriarch(cell.getSheet()).createPicture(anchor,
						cell.getSheet().getWorkbook().addPicture(data, getImageType(data)));
			}
		}
	}

	/**
	 * 获取画布
	 * @param sheet 2
	 * @return 1
	 */
	public static Drawing<?> getDrawingPatriarch(Sheet sheet) {
		if (sheet.getDrawingPatriarch() == null) {
			sheet.createDrawingPatriarch();
		}
		return sheet.getDrawingPatriarch();
	}

	/**
	 * 获取图片类型,设置图片插入类型
	 * @return 1
	 */
	public int getImageType(byte[] value) {
		String type = FileTypeUtils.getFileExtendName(value);
		if ("JPG".equalsIgnoreCase(type)) {
			return Workbook.PICTURE_TYPE_JPEG;
		}
		else if ("PNG".equalsIgnoreCase(type)) {
			return Workbook.PICTURE_TYPE_PNG;
		}
		return Workbook.PICTURE_TYPE_JPEG;
	}

	/**
	 * 创建表格样式
	 */
	public void setDataValidation(Excel attr, int column) {
		if (attr.name().contains("注：")) {
			sheet.setColumnWidth(column, 6000);
		}
		else {
			// 设置列宽
			sheet.setColumnWidth(column, (int) ((attr.width() + 0.72) * 256));
		}
		if (StringUtils.isNotEmpty(attr.prompt()) || attr.combo().length > 0) {
			// 提示信息或只能选择不能输入的列内容.
			setPromptOrValidation(sheet, attr.combo(), attr.prompt(), column, column);
		}
	}

	/**
	 * 添加单元格
	 * @return 1
	 */
	public Cell addCell(Excel attr, Row row, T vo, Field field, int column) {
		Cell cell = null;
		try {
			// 设置行高
			row.setHeight(maxHeight);
			// 根据Excel中设置情况决定是否导出,有些情况需要保持为空,希望用户填写这一列.
			if (attr.isExport()) {
				// 创建cell
				cell = row.createCell(column);
				cell.setCellStyle(styles.get(StringUtils.format("data_{}_{}_{}", attr.align(), attr.color(), attr.backgroundColor())));

				// 用于读取对象中的属性
				Object value = getTargetValue(vo, field, attr);
				String dateFormat = attr.dateFormat();
				String readConverterExp = attr.readConverterExp();
				String separator = attr.separator();
				if (StringUtils.isNotEmpty(dateFormat) && StringUtils.isNotNull(value)) {
					cell.setCellValue(parseDateToStr(dateFormat, value));
				}
				else if (StringUtils.isNotEmpty(readConverterExp) && StringUtils.isNotNull(value)) {
					cell.setCellValue(convertByExp(Convert.toStr(value), readConverterExp, separator));
				}
				else if (value instanceof BigDecimal && -1 != attr.scale()) {
					cell.setCellValue((((BigDecimal) value).setScale(attr.scale(), attr.roundingMode())).doubleValue());
				}
				else if (!attr.handler().equals(ExcelHandlerAdapter.class)) {
					cell.setCellValue(dataFormatHandlerAdapter(value, attr));
				}
				else {
					// 设置列类型
					setCellVo(value, attr, cell);
				}
				addStatisticsData(column, Convert.toStr(value), attr);
			}
		}
		catch (Exception e) {
			log.error("导出Excel失败", e);
		}
		return cell;
	}

	/**
	 * 设置 POI XSSFSheet 单元格提示或选择框
	 */
	public void setPromptOrValidation(Sheet sheet, String[] textlist, String promptContent, int firstCol, int endCol) {
		DataValidationHelper helper = sheet.getDataValidationHelper();
		DataValidationConstraint constraint = textlist.length > 0 ? helper.createExplicitListConstraint(textlist)
				: helper.createCustomConstraint("DD1");
		CellRangeAddressList regions = new CellRangeAddressList(1, 100, firstCol, endCol);
		DataValidation dataValidation = helper.createValidation(constraint, regions);
		if (StringUtils.isNotEmpty(promptContent)) {
			// 如果设置了提示信息则鼠标放上去提示
			dataValidation.createPromptBox("", promptContent);
			dataValidation.setShowPromptBox(true);
		}
		// 处理Excel兼容性问题
		if (dataValidation instanceof XSSFDataValidation) {
			dataValidation.setSuppressDropDownArrow(true);
			dataValidation.setShowErrorBox(true);
		}
		else {
			dataValidation.setSuppressDropDownArrow(false);
		}
		sheet.addValidationData(dataValidation);
	}

	/**
	 * 解析导出值 0=男,1=女,2=未知
	 * @param propertyValue 参数值
	 * @param converterExp 翻译注解
	 * @param separator 分隔符
	 * @return 解析后值
	 */
	public static String convertByExp(String propertyValue, String converterExp, String separator) {
		StringBuilder propertyString = new StringBuilder();
		String[] convertSource = converterExp.split(",");
		for (String item : convertSource) {
			String[] itemArray = item.split("=");
			if (StringUtils.containsAny(separator, propertyValue)) {
				for (String value : propertyValue.split(separator)) {
					if (itemArray[0].equals(value)) {
						propertyString.append(itemArray[1] + separator);
						break;
					}
				}
			}
			else {
				if (itemArray[0].equals(propertyValue)) {
					return itemArray[1];
				}
			}
		}
		return StringUtils.stripEnd(propertyString.toString(), separator);
	}

	/**
	 * 反向解析值 男=0,女=1,未知=2
	 * @param propertyValue 参数值
	 * @param converterExp 翻译注解
	 * @param separator 分隔符
	 * @return 解析后值
	 */
	public static String reverseByExp(String propertyValue, String converterExp, String separator) {
		StringBuilder propertyString = new StringBuilder();
		String[] convertSource = converterExp.split(",");
		for (String item : convertSource) {
			String[] itemArray = item.split("=");
			if (StringUtils.containsAny(separator, propertyValue)) {
				for (String value : propertyValue.split(separator)) {
					if (itemArray[1].equals(value)) {
						propertyString.append(itemArray[0] + separator);
						break;
					}
				}
			}
			else {
				if (itemArray[1].equals(propertyValue)) {
					return itemArray[0];
				}
			}
		}
		return StringUtils.stripEnd(propertyString.toString(), separator);
	}

	/**
	 * 数据处理器
	 * @param value 数据值
	 * @param excel 数据注解
	 * @return 1
	 */
	public String dataFormatHandlerAdapter(Object value, Excel excel) {
		try {
			Object instance = excel.handler().newInstance();
			Method formatMethod = excel.handler().getMethod("format", new Class[] { Object.class, String[].class });
			value = formatMethod.invoke(instance, value, excel.args());
		}
		catch (Exception e) {
			log.error("不能格式化数据 " + excel.handler(), e.getMessage());
		}
		return Convert.toStr(value);
	}

	/**
	 * 合计统计信息
	 */
	private void addStatisticsData(Integer index, String text, Excel entity) {
		if (entity != null && entity.isStatistics()) {
			Double temp = 0D;
			if (!statistics.containsKey(index)) {
				statistics.put(index, temp);
			}
			try {
				temp = Double.valueOf(text);
			}
			catch (NumberFormatException e) {
			}
			statistics.put(index, statistics.get(index) + temp);
		}
	}

	/**
	 * 创建统计行
	 */
	public void addStatisticsRow() {
		if (statistics.size() > 0) {
			Row row = sheet.createRow(sheet.getLastRowNum() + 1);
			Set<Integer> keys = statistics.keySet();
			Cell cell = row.createCell(0);
			cell.setCellStyle(styles.get("total"));
			cell.setCellValue("合计");

			for (Integer key : keys) {
				cell = row.createCell(key);
				cell.setCellStyle(styles.get("total"));
				cell.setCellValue(DOUBLE_FORMAT.format(statistics.get(key)));
			}
			statistics.clear();
		}
	}

	/**
	 * 获取bean中的属性值
	 * @return 1
	 */
	private Object getTargetValue(T vo, Field field, Excel excel) throws Exception {
		Object o = field.get(vo);
		if (StringUtils.isNotEmpty(excel.targetAttr())) {
			String target = excel.targetAttr();
			if (target.contains(".")) {
				String[] targets = target.split("[.]");
				for (String name : targets) {
					o = getValue(o, name);
				}
			}
			else {
				o = getValue(o, target);
			}
		}
		return o;
	}

	/**
	 * 以类的属性的get方法方法形式获取值
	 * @return 1
	 */
	private Object getValue(Object o, String name) throws Exception {
		if (StringUtils.isNotNull(o) && StringUtils.isNotEmpty(name)) {
			Class<?> clazz = o.getClass();
			Field field = clazz.getDeclaredField(name);
			field.setAccessible(true);
			o = field.get(o);
		}
		return o;
	}

	/**
	 * 得到所有定义字段
	 */
	private void createExcelField() {
		this.fields = getFields();
		this.fields = this.fields.stream()
			.sorted(Comparator.comparing(objects -> ((Excel) objects[1]).sort()))
			.collect(Collectors.toList());
		this.maxHeight = getRowHeight();
	}

	/**
	 * 获取字段注解信息
	 * @return 1
	 */
	public List<Object[]> getFields() {
		List<Object[]> fields = new ArrayList<>();
		List<Field> tempFields = new ArrayList<>();
		tempFields.addAll(Arrays.asList(clazz.getSuperclass().getDeclaredFields()));
		tempFields.addAll(Arrays.asList(clazz.getDeclaredFields()));
		for (Field field : tempFields) {
			if (!ArrayUtils.contains(this.excludeFields, field.getName())) {
				// 单注解
				if (field.isAnnotationPresent(Excel.class)) {
					Excel attr = field.getAnnotation(Excel.class);
					if (attr != null && (attr.type() == Type.ALL || attr.type() == type)) {
						field.setAccessible(true);
						fields.add(new Object[] { field, attr });
					}
				}
				// 多注解
				if (field.isAnnotationPresent(Excels.class)) {
					Excels attrs = field.getAnnotation(Excels.class);
					Excel[] excels = attrs.value();
					for (Excel attr : excels) {
						if (attr != null && (attr.type() == Type.ALL || attr.type() == type)) {
							field.setAccessible(true);
							fields.add(new Object[] { field, attr });
						}
					}
				}
			}
		}
		return fields;
	}

	/**
	 * 根据注解获取最大行高
	 * @return 最大行高
	 */
	public short getRowHeight() {
		double maxHeight = 0;
		for (Object[] os : this.fields) {
			Excel excel = (Excel) os[1];
			maxHeight = Math.max(maxHeight, excel.height());
		}
		return (short) (maxHeight * 20);
	}

	/**
	 * 创建一个工作簿
	 */
	public void createWorkbook() {
		this.wb = new SXSSFWorkbook(500);
		this.sheet = wb.createSheet();
		wb.setSheetName(0, sheetName);
		this.styles = createStyles(wb);
	}

	/**
	 * 创建工作表
	 * @param sheetNo sheet数量
	 * @param index 序号
	 */
	public void createSheet(int sheetNo, int index) {
		// 设置工作表的名称.
		if (sheetNo > 1 && index > 0) {
			this.sheet = wb.createSheet();
			this.createTitle();
			wb.setSheetName(index, sheetName + index);
		}
	}

	/**
	 * 获取单元格值
	 * @param row 获取的行
	 * @param column 获取单元格列号
	 * @return 单元格值
	 */
	public Object getCellValue(Row row, int column) {
		if (row == null) {
			return row;
		}
		Object val = "";
		try {
			Cell cell = row.getCell(column);
			if (StringUtils.isNotNull(cell)) {
				if (cell.getCellType() == CellType.NUMERIC || cell.getCellType() == CellType.FORMULA) {
					val = cell.getNumericCellValue();
					if (DateUtil.isCellDateFormatted(cell)) {
						val = DateUtil.getJavaDate((Double) val); // POI Excel 日期格式转换
					}
					else {
						if ((Double) val % 1 != 0) {
							val = new BigDecimal(val.toString());
						}
						else {
							val = new DecimalFormat("0").format(val);
						}
					}
				}
				else if (cell.getCellType() == CellType.STRING) {
					val = cell.getStringCellValue();
				}
				else if (cell.getCellType() == CellType.BOOLEAN) {
					val = cell.getBooleanCellValue();
				}
				else if (cell.getCellType() == CellType.ERROR) {
					val = cell.getErrorCellValue();
				}

			}
		}
		catch (Exception e) {
			return val;
		}
		return val;
	}

	/**
	 * 格式化不同类型的日期对象
	 * @param dateFormat 日期格式
	 * @param val 被格式化的日期对象
	 * @return 格式化后的日期字符
	 */
	public String parseDateToStr(String dateFormat, Object val) {
		if (val == null) {
			return "";
		}
		String str;
		if (val instanceof Date) {
			str = DateUtils.parseDateToStr(dateFormat, (Date) val);
		}
		else if (val instanceof LocalDateTime) {
			str = DateUtils.parseDateToStr(dateFormat, DateUtils.toDate((LocalDateTime) val));
		}
		else if (val instanceof LocalDate) {
			str = DateUtils.parseDateToStr(dateFormat, DateUtils.toDate((LocalDate) val));
		}
		else {
			str = val.toString();
		}
		return str;
	}

	/**
	 * 设置响应结果
	 * @param response 响应结果对象
	 * @param fileName 文件名
	 * @throws UnsupportedEncodingException 不支持编码异常
	 */
	public static void setExcelResponseProp(HttpServletResponse response, String fileName) throws UnsupportedEncodingException {

		// 设置导出文件名称（避免乱码）
		fileName = URLEncoder.encode(fileName.concat(".xlsx"), StandardCharsets.UTF_8).replaceAll("\\+", "%20");
		// 设置响应头信息，文件名编码防止中文乱码
		response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
		response.setCharacterEncoding("utf-8");
		response.setHeader("Content-Disposition", "attachment; filename=" + fileName);

	}

}
