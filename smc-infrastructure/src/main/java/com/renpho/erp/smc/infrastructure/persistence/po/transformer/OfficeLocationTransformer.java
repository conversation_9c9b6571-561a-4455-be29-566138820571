package com.renpho.erp.smc.infrastructure.persistence.po.transformer;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

import org.mapstruct.Mapper;
import org.mapstruct.MapperConfig;
import org.mapstruct.factory.Mappers;

import com.renpho.erp.smc.domain.organizationmanagement.office.OfficeLocation;
import com.renpho.erp.smc.domain.systemsetting.MultiLanguage;
import com.renpho.erp.smc.infrastructure.persistence.po.OfficeLocationLanguagePO;
import com.renpho.erp.smc.infrastructure.persistence.po.OfficeLocationPO;

@Mapper
@MapperConfig
public interface OfficeLocationTransformer {

	/**
	 * 实例
	 */
	OfficeLocationTransformer INSTANCE = Mappers.getMapper(OfficeLocationTransformer.class);

	default Integer mapOfficeLocationID(OfficeLocation.OfficeLocationID value) {
		if (null == value) {
			return null;
		}
		return value.getId();
	}

	default OfficeLocationPO toOfficeLocationPO(OfficeLocation officeLocation) {
		OfficeLocationPO officeLocationPO = new OfficeLocationPO();
		officeLocationPO.setId(this.mapOfficeLocationID(officeLocation.getId()));
		officeLocationPO.setStatus(officeLocation.getStatus());
		return officeLocationPO;
	}

	default List<OfficeLocationLanguagePO> toOfficeLocationLanguagePOList(List<MultiLanguage> names, Integer officeLocationId) {
		List<OfficeLocationLanguagePO> officeLocationLanguagePOList = new ArrayList<>(names.size());
		for (MultiLanguage multiLanguage : names) {
			officeLocationLanguagePOList.add(this.toOfficeLocationLanguagePO(multiLanguage, officeLocationId));
		}
		return officeLocationLanguagePOList;
	};

	default OfficeLocationLanguagePO toOfficeLocationLanguagePO(MultiLanguage multiLanguage, Integer officeLocationId) {
		OfficeLocationLanguagePO officeLocationLanguagePO = new OfficeLocationLanguagePO();
		officeLocationLanguagePO.setOfficeLocationId(officeLocationId);
		officeLocationLanguagePO.setName(multiLanguage.getName());
		officeLocationLanguagePO.setLanguage(multiLanguage.getLanguage());
		return officeLocationLanguagePO;
	}

	default OfficeLocation toOfficeLocation(OfficeLocationPO officeLocationPO,
			List<OfficeLocationLanguagePO> officeLocationLanguagePOList) {
		return new OfficeLocation(OfficeLocation.OfficeLocationID.of(officeLocationPO.getId()), officeLocationPO.getStatus(),
				this.toMultiLanguage(officeLocationLanguagePOList));
	}

	private List<MultiLanguage> toMultiLanguage(List<OfficeLocationLanguagePO> officeLocationLanguagePOList) {
		if (officeLocationLanguagePOList.isEmpty()) {
			return Collections.emptyList();
		}
		List<MultiLanguage> multiLanguages = new ArrayList<>(officeLocationLanguagePOList.size());
		for (OfficeLocationLanguagePO officeLocationLanguagePO : officeLocationLanguagePOList) {
			MultiLanguage multiLanguage = new MultiLanguage();
			multiLanguage.setLanguage(officeLocationLanguagePO.getLanguage());
			multiLanguage.setName(officeLocationLanguagePO.getName());
			multiLanguages.add(multiLanguage);
		}
		return multiLanguages;
	}

}
