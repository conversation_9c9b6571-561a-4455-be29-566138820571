// package com.renpho.erp.smc.infrastructure.teams;
//
// import com.microsoft.graph.serviceclient.GraphServiceClient;
// import com.microsoft.kiota.authentication.AuthenticationProvider;
// import org.springframework.beans.factory.config.BeanDefinition;
// import org.springframework.context.annotation.Bean;
// import org.springframework.context.annotation.Scope;
// import org.springframework.stereotype.Component;
//
// @Component
// public class GraphServiceClientFactory {
//
// @Scope(BeanDefinition.SCOPE_PROTOTYPE)
// @Bean(name = "graphServiceClientByAccessToken")
// public GraphServiceClient createGraphClient(String accessToken) {
// AuthenticationProvider authProvider = new TokenAuthAccessProvider(accessToken);
// return new GraphServiceClient(authProvider);
// }
//
// }