package com.renpho.erp.smc.infrastructure.persistence.po;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.renpho.karma.cloud.mybatisplus.po.AlterationPO;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 系统消息表
 */
@EqualsAndHashCode(callSuper = true)
@Data
@AllArgsConstructor
@NoArgsConstructor
@TableName(value = "sys_message")
public class MessagePO extends AlterationPO<Integer, Integer, LocalDateTime, Integer> implements Serializable {

	/**
	 * 消息ID
	 */
	@TableField(value = "message_id")
	private String messageId;

	/**
	 * 消息接收人用户ID
	 */
	@TableField(value = "user_id")
	private Integer userId;

	/**
	 * 消息类型 TEXT（文本）
	 */
	@TableField(value = "`type`")
	private String type;

	/**
	 * 消息标题
	 */
	@TableField(value = "title")
	private String title;

	/**
	 * 消息内容
	 */
	@TableField(value = "content")
	private String content;

	/**
	 * 系统名称
	 */
	@TableField(value = "service")
	private String service;

	/**
	 * 功能模块
	 */
	@TableField(value = "`module`")
	private String module;

	/**
	 * 已读状态 0未读 1已读
	 */
	@TableField(value = "is_read")
	private Integer isRead;

	@Serial
	private static final long serialVersionUID = 1L;

}