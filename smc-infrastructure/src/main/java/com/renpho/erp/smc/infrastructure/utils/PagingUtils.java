package com.renpho.erp.smc.infrastructure.utils;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.renpho.karma.dto.Paging;

import java.util.Collection;

/**
 * 分页转换工具.
 *
 * <AUTHOR>
 * @date 2024.9.18
 */
public final class PagingUtils {

	private PagingUtils() {
	}

	/**
	 * 将 IPage 转换为 Paging 对象
	 * @param iPage MyBatis Plus 的 IPage 对象
	 * @param <T> 数据类型
	 * @return 转换后的 Paging 对象
	 */
	public static <T> Paging<T> convertToPaging(IPage<T> iPage) {
		// 从 IPage 中提取分页数据
		// 当前页
		int pageIndex = (int) iPage.getCurrent();
		// 每页记录数
		int pageSize = (int) iPage.getSize();
		// 总记录数
		int totalCount = (int) iPage.getTotal();
		// 当前页的记录列表
		Collection<T> records = iPage.getRecords();

		Paging<T> paging = new Paging<>();
		paging.setPageIndex(pageIndex);
		paging.setPageSize(pageSize);
		paging.setTotalCount(totalCount);
		paging.setRecords(records);

		// 返回构造好的 Paging 对象
		return paging;
	}

}
