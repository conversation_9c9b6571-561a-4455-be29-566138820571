package com.renpho.erp.smc.infrastructure.enums;

public enum GenderEnum {

	/**  */
	Male("1", "Male", "男"),
	/**  */
	Female("2", "Female", "女"),
	/**  */
	X("3", "X", "未知"),;

	private final String code;

	private final String enname;

	private final String zhname;

	GenderEnum(String code, String enname, String zhname) {
		this.code = code;
		this.enname = enname;
		this.zhname = zhname;
	}

	public static GenderEnum getNameByCode(String code) {
		for (GenderEnum e : GenderEnum.values()) {
			if (e.getCode().equals(code)) {
				return e;
			}
		}
		return null;
	}

	public static String getCode(String enname) {
		for (GenderEnum e : GenderEnum.values()) {
			if (e.getEnname().equals(enname) || e.getZhname().equals(enname)) {
				return e.getCode();
			}
		}
		return null;
	}

	public String getCode() {
		return code;
	}

	public String getEnname() {
		return enname;
	}

	public String getZhname() {
		return zhname;
	}

}
