package com.renpho.erp.smc.infrastructure.utils.common;

import jakarta.servlet.http.HttpServletRequest;

public class RequestUrlUtil {

	private String url;

	public static String getReqUrl(HttpServletRequest request) {
		String scheme = request.getScheme();
		String serverName = request.getServerName();
		int serverPort = request.getServerPort();
		String contextPath = request.getContextPath();
		StringBuffer url = new StringBuffer();
		url.append(scheme).append("://").append(serverName);
		if (serverPort != 80 && serverPort != 443) {
			url.append(":").append(serverPort);
		}
		url.append(contextPath);
		String resUrl = url.toString();
		return resUrl;
	}

}
