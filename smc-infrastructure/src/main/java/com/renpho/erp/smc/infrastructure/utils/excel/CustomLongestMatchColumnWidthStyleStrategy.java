// 3.0.5版本
package com.renpho.erp.smc.infrastructure.utils.excel;

import cn.hutool.core.util.StrUtil;
import com.alibaba.excel.enums.CellDataTypeEnum;
import com.alibaba.excel.metadata.Head;
import com.alibaba.excel.metadata.data.WriteCellData;
import com.alibaba.excel.util.MapUtils;
import com.alibaba.excel.write.metadata.holder.WriteSheetHolder;
import com.alibaba.excel.write.style.column.LongestMatchColumnWidthStyleStrategy;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.poi.ss.usermodel.Cell;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 优化自定义最长匹配列宽样式策略 会略微影响速度，对导出速度有要求时，建议不使用.
 *
 * <AUTHOR>
 */
public class CustomLongestMatchColumnWidthStyleStrategy extends LongestMatchColumnWidthStyleStrategy {

	private static final int MAX_COLUMN_WIDTH = 255;

	private final Map<Integer, Map<Integer, Integer>> cache = MapUtils.newHashMapWithExpectedSize(8);

	@Override
	protected void setColumnWidth(WriteSheetHolder writeSheetHolder, List<WriteCellData<?>> cellDataList, Cell cell, Head head,
			Integer relativeRowIndex, Boolean isHead) {
		boolean needSetWidth = isHead || !CollectionUtils.isEmpty(cellDataList);
		if (!needSetWidth) {
			return;
		}
		Map<Integer, Integer> maxColumnWidthMap = cache.get(writeSheetHolder.getSheetNo());
		if (maxColumnWidthMap == null) {
			maxColumnWidthMap = new HashMap<Integer, Integer>(16);
			cache.put(writeSheetHolder.getSheetNo(), maxColumnWidthMap);
		}
		Integer columnWidth = dataLength(cellDataList, cell, isHead, head);
		if (columnWidth < 0) {
			return;
		}
		if (columnWidth > MAX_COLUMN_WIDTH) {
			columnWidth = MAX_COLUMN_WIDTH;
		}
		Integer maxColumnWidth = maxColumnWidthMap.get(cell.getColumnIndex());
		if (maxColumnWidth == null || columnWidth > maxColumnWidth) {
			maxColumnWidthMap.put(cell.getColumnIndex(), columnWidth);
			writeSheetHolder.getSheet().setColumnWidth(cell.getColumnIndex(), columnWidth * 256);
		}
	}

	private Integer dataLength(List<WriteCellData<?>> cellDataList, Cell cell, Boolean isHead, Head head) {
		if (isHead) {
			return cell.getStringCellValue().getBytes().length;
		}
		WriteCellData<?> cellData = cellDataList.get(0);
		CellDataTypeEnum type = cellData.getType();
		if (type == null) {
			return -1;
		}
		int addWidth = 4;
		int titleLength = head.getHeadNameList().get(0).getBytes().length + addWidth;
		int length = 0;
		String cellStr;
		switch (type) {
			case STRING:
				cellStr = cellData.getStringValue();
				if (StrUtil.isEmpty(cellStr)) {
					break;
				}
				if (cellStr.contains("\n")) {
					String[] cellStrList = cellStr.split("\n");
					for (String str : cellStrList) {
						int strLength = str.getBytes().length + addWidth;
						if (strLength > length) {
							length = strLength;
						}
					}
				}
				else {
					length = cellData.getStringValue().getBytes().length + addWidth;
				}
				break;
			case BOOLEAN:
				length = cellData.getBooleanValue().toString().getBytes().length + addWidth;
				break;
			case NUMBER:
				cellStr = cellData.getNumberValue().toString();
				if (StrUtil.isEmpty(cellStr)) {
					break;
				}
				if (cellStr.contains("\n")) {
					length = cellStr.substring(0, cellStr.indexOf("\n")).getBytes().length + addWidth + addWidth;
				}
				else {
					length = cellStr.getBytes().length + addWidth + addWidth;
				}
				break;
			default:
				length = -1;
		}
		if (length < 10) {
			// 宽带最小10
			length = 10;
		}
		else if (length > 50) {
			// 宽带最大50
			length = 50;
		}
		return Math.max(length, titleLength);
	}

}
