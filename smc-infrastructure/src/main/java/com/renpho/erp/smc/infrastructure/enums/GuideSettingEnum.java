package com.renpho.erp.smc.infrastructure.enums;

public enum GuideSettingEnum {

	/** 需要引导 */
	requireGuide(1, "NeedGuide", "需要引导"),
	/** 不需要引导 */
	noRequireGuide(0, "NoGuide", "不需要引导");

	private final int code;

	private final String enname;

	private final String zhname;

	GuideSettingEnum(int code, String enname, String zhname) {
		this.code = code;
		this.enname = enname;
		this.zhname = zhname;
	}

	public static GuideSettingEnum getNameByCode(Integer code) {
		for (GuideSettingEnum e : GuideSettingEnum.values()) {
			if (e.getCode() == code) {
				return e;
			}
		}
		return null;
	}

	public static int getCode(String enname) {
		for (GuideSettingEnum e : GuideSettingEnum.values()) {
			if (e.getEnname().equals(enname) || e.getZhname().equals(enname)) {
				return e.getCode();
			}
		}
		return 1;
	}

	public int getCode() {
		return this.code;
	}

	public String getEnname() {
		return this.enname;
	}

	public String getZhname() {
		return this.zhname;
	}

}
