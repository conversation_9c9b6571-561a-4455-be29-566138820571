package com.renpho.erp.smc.infrastructure.dingtalk;

/**
 * 钉钉常量类.
 *
 * <AUTHOR>
 * @date 2022/12/19 11:44
 */
public final class DingTalkStatus {

	private DingTalkStatus() {

	}

	/**
	 * 获取所有考勤组信息URL
	 */
	public static final String GET_SIMPLE_GROUPS = "https://oapi.dingtalk.com/topapi/attendance/getsimplegroups";

	/**
	 * 获取钉钉考勤记录URL
	 */
	public static final String GET_ATTENDANCE_LIST = "https://oapi.dingtalk.com/attendance/list";

	/**
	 * 发送消息URL
	 */
	public static final String TO_MESSAGE_CORPCONVERSATION = "https://oapi.dingtalk.com/topapi/message/corpconversation/asyncsend_v2";

	/**
	 * 获取报表假期数据URL
	 */
	public static final String GET_LEAVE_TIME_BY_NAMES = "https://oapi.dingtalk.com/topapi/attendance/getleavetimebynames";

	/**
	 * 获取考勤报表列值URL
	 */
	public static final String GET_ATTENDANCE_COLUMN_VAL = "https://oapi.dingtalk.com/topapi/attendance/getcolumnval";

	/**
	 * 批量查询人员排班信息
	 */
	public static final String GET_SCHEDULE_LISTBYUSERS = "https://oapi.dingtalk.com/topapi/attendance/schedule/listbyusers";

}
