package com.renpho.erp.smc.infrastructure.enums;

import lombok.Getter;

/**
 * 账号类型枚举
 *
 * <AUTHOR>
 * @Date 2025/6/26 14:44
 **/
@Getter
public enum ThirdAccountTypeEnum {

	/**
	 * 钉钉
	 */
	DING_TALK("DingTalk", "钉钉"),

	/**
	 * 企业微信
	 */
	WE_COM("WeCom", "企业微信");

	/**
	 * 代码
	 */
	private final String code;

	/**
	 * 描述
	 */
	private final String desc;

	/**
	 * 注意类型枚举
	 * @param code 代码
	 * @param desc 描述
	 */
	ThirdAccountTypeEnum(String code, String desc) {
		this.code = code;
		this.desc = desc;
	}

	/**
	 * 根据编码获取枚举
	 * @return com.renpho.erp.smc.infrastructure.enums.AccountTypeEnum
	 * <AUTHOR>
	 * @Date 14:45 2025/6/26
	 * @Param [code]
	 **/
	public static ThirdAccountTypeEnum getByCode(String code) {
		for (ThirdAccountTypeEnum type : ThirdAccountTypeEnum.values()) {
			if (type.getCode().equals(code)) {
				return type;
			}
		}
		return null;
	}

}
