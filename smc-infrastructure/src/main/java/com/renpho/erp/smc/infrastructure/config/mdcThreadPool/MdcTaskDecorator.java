package com.renpho.erp.smc.infrastructure.config.mdcThreadPool;

import java.util.Map;

import org.slf4j.MDC;
import org.springframework.core.task.TaskDecorator;

import lombok.extern.slf4j.Slf4j;

/**
 * @desc: MdcTaskDecorator 确保在异步线程中也能正确获取父线程的 MDC
 * @time: 2025-03-17 09:49:11
 * @author: Alina
 */
@Slf4j
public class MdcTaskDecorator implements TaskDecorator {

	@Override
	public Runnable decorate(Runnable runnable) {
		Map<String, String> contextMap = MDC.getCopyOfContextMap();
		return () -> {
			try {
				if (contextMap != null) {
					MDC.setContextMap(contextMap);
				}
				runnable.run();
			}
			catch (Exception e) {
				// 确保在记录错误日志时，MDC 的上下文仍然存在
				if (contextMap != null) {
					MDC.setContextMap(contextMap);
				}
				log.error(String.format("Thread [%s] encountered an error: %s", Thread.currentThread().getName(), e.getMessage()), e);
			}
			finally {
				MDC.clear();
			}
		};
	}
}
