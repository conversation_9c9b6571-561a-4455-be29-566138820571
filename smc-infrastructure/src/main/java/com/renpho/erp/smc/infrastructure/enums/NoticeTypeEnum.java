package com.renpho.erp.smc.infrastructure.enums;

import lombok.Getter;

/**
 * 通知类型枚举
 *
 * <AUTHOR>
 * @date 2025/03/28
 */
@Getter
public enum NoticeTypeEnum {

	/**
	 * 注意
	 */
	NOTICE(1, "通知公告"),
	/**
	 * 集团新闻
	 */
	GROUP_NEWS(2, "集团要闻");

	/**
	 * 代码
	 */
	private final int code;

	/**
	 * 描述
	 */
	private final String description;

	/**
	 * 注意类型枚举
	 * @param code 代码
	 * @param description 描述
	 */
	NoticeTypeEnum(int code, String description) {
		this.code = code;
		this.description = description;
	}

	/**
	 * 来自代码
	 * @param code 代码
	 * @return {@link NoticeTypeEnum }
	 */
	public static NoticeTypeEnum fromCode(int code) {
		for (NoticeTypeEnum type : NoticeTypeEnum.values()) {
			if (type.getCode() == code) {
				return type;
			}
		}
		throw new IllegalArgumentException("Unknown code: " + code);
	}

}