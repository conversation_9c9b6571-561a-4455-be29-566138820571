package com.renpho.erp.smc.infrastructure.utils.email;

/**
 * <AUTHOR>
 * @date 2022/6/14 10:39
 */
public enum EmailEnum {

	/** QQ_EMAIL */
	QQ_EMAIL("QQ邮箱", 1, "Junk", "Sent Messages"), OUTLOOK("outlook邮箱", 2, "Junk", "Sent"),
	/** 垃圾信箱名字会随着页面设置的语言进行改变 */
	GMAIL("gmail邮箱", 3, "none", "[Gmail]/已发邮件"),
	/** QQ_EMAIL */
	AMAZON_EMAIL("amazon邮箱", 4, "Junk E-mail", "Sent Items"), NET_EASE_EMAIL("163邮箱", 5, "垃圾邮件", "已发送"),
	/** QQ_EMAIL */
	YAHOO_EMAIL("雅虎邮箱", 6, "", ""), // yahoo没有把 Spam文件夹放出来
	/** QQ_EMAIL */
	HORDE("horde邮箱", 7, "", ""), // 没有垃圾邮箱
	/** QQ_EMAIL */
	QQ_EXMAIL("qq企业邮箱", 8, "Junk", "Sent Messages"), HOTMAIL("hotmail邮箱", 9, "Junk", "Sent"),
	/** QQ_EMAIL */
	OUTLOOK_ENTERPRISE("outlook企业邮箱", 10, "Junk Email", "Sent Items");

	private String name;

	private Integer type;

	/**
	 * 垃圾邮箱名字
	 */
	private String junk;

	private String send;

	EmailEnum(String name, Integer type, String junk, String send) {
		this.name = name;
		this.type = type;
		this.junk = junk;
		this.send = send;

	}

	public String getJunk() {
		return junk;
	}

	public void setJunk(String junk) {
		this.junk = junk;
	}

	public String getName() {
		return name;
	}

	public void setName(String name) {
		this.name = name;
	}

	public Integer getType() {
		return type;
	}

	public void setType(Integer type) {
		this.type = type;
	}

	public static String getName(Integer type) {
		for (EmailEnum emailEnum : values()) {
			if (emailEnum.getType().equals(type)) {
				return emailEnum.getName();
			}
		}

		return "";
	}

	public static String getJunk(Integer type) {
		for (EmailEnum emailEnum : values()) {
			if (emailEnum.getType().equals(type)) {
				return emailEnum.getJunk();
			}

		}

		return "";
	}

	public String getSend() {
		return send;
	}

	public void setSend(String send) {
		this.send = send;
	}

	public static String getSend(Integer type) {
		for (EmailEnum emailEnum : values()) {
			if (emailEnum.getType().equals(type)) {
				return emailEnum.getSend();
			}
		}

		return "";
	}

}
