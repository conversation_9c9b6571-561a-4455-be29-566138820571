package com.renpho.erp.smc.infrastructure.dto;

import lombok.Data;

/**
 * 三方平台用户信息
 */
@Data
public class ThirdPlatformUserDto {

	/** 平台关联用户id */
	private String userid;

	/**
	 * 钉钉/unionid
	 */

	private String unionid;

	/** 用户id */
	private Integer sysUserId;

	/**
	 * 姓名
	 */
	private String name;

	/**
	 * 是否已绑定了用户
	 */
	private boolean bindUser;

	/**
	 * 状态：0已删除 1正常
	 */
	private Integer status;

	/**
	 * 平台类型
	 */
	private String thirdPlatformType;

}
