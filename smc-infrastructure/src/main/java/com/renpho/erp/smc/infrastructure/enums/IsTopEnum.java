package com.renpho.erp.smc.infrastructure.enums;

import lombok.Getter;

/**
 * 是最高枚举
 *
 * <AUTHOR>
 * @date 2025/03/28
 */
@Getter
public enum IsTopEnum {

	/**
	 * 不
	 */
	NO(0),
	/**
	 * 是
	 */
	YES(1);

	/**
	 * 价值
	 */
	private final int value;

	/**
	 * 是最高枚举
	 * @param value 价值
	 */
	IsTopEnum(int value) {
		this.value = value;
	}

	/**
	 * 从价值
	 * @param value 价值
	 * @return {@link IsTopEnum }
	 */
	public static IsTopEnum fromValue(int value) {
		for (IsTopEnum isTop : IsTopEnum.values()) {
			if (isTop.getValue() == value) {
				return isTop;
			}
		}
		throw new IllegalArgumentException("Unknown value: " + value);
	}

}