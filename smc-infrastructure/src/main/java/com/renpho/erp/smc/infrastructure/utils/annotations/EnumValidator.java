package com.renpho.erp.smc.infrastructure.utils.annotations;

import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.google.common.base.CaseFormat;
import jakarta.validation.*;

import java.lang.annotation.*;
import java.util.Arrays;

/**
 * <AUTHOR>
 */
@Documented
@Constraint(validatedBy = EnumValidator.Validator.class)
@Target(ElementType.FIELD)
@Retention(RetentionPolicy.RUNTIME)
@ReportAsSingleViolation
public @interface EnumValidator {

	String message() default "枚举值不正确";

	Class<?>[] groups() default {};

	Class<? extends Payload>[] payload() default {};

	Class<? extends Enum> value();

	boolean convertCase() default false;

	CaseFormat from() default CaseFormat.UPPER_UNDERSCORE;

	CaseFormat to() default CaseFormat.LOWER_CAMEL;

	class Validator implements ConstraintValidator<EnumValidator, String> {

		private EnumValidator enumValue;

		@Override
		public void initialize(EnumValidator enumValue) {
			this.enumValue = enumValue;
		}

		@Override
		public boolean isValid(String text, ConstraintValidatorContext constraintValidatorContext) {
			if (StringUtils.isEmpty(text)) {
				return true;
			}
			return Arrays.stream(enumValue.value().getEnumConstants()).anyMatch(e -> {
				String name = e.name();
				if (enumValue.convertCase()) {
					name = enumValue.from().to(enumValue.to(), name);
				}
				return name.equals(text);
			});
		}

	}

}
