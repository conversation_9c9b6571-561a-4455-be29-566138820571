package com.renpho.erp.smc.infrastructure.config;

import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.EnableAspectJAutoProxy;

/**
 * 事务代理主键，允许在切面中获取代理对象
 *
 * <AUTHOR>
 * @since 2024/11/12
 */
@Configuration
@EnableAspectJAutoProxy(proxyTargetClass = true, exposeProxy = true) // 使用 CGLIB
																		// 动态代理并暴露代理对象
public class AopConfig {

}
