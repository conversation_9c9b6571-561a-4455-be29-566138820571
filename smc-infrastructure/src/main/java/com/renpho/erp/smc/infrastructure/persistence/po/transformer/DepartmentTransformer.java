package com.renpho.erp.smc.infrastructure.persistence.po.transformer;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Optional;

import org.mapstruct.Mapper;
import org.mapstruct.MapperConfig;
import org.mapstruct.factory.Mappers;

import com.alibaba.fastjson.JSON;
import com.renpho.erp.smc.domain.organizationmanagement.Department.Department;
import com.renpho.erp.smc.domain.organizationmanagement.Department.DepartmentStatus;
import com.renpho.erp.smc.domain.systemsetting.MultiLanguage;
import com.renpho.erp.smc.infrastructure.persistence.dto.DepartmentTreeDto;
import com.renpho.erp.smc.infrastructure.persistence.dto.DepartmentUserSimpleDto;
import com.renpho.erp.smc.infrastructure.persistence.dto.DepartmentUserTreeDto;
import com.renpho.erp.smc.infrastructure.persistence.po.DepartmentLanguagePO;
import com.renpho.erp.smc.infrastructure.persistence.po.DepartmentPO;
import com.renpho.erp.smc.infrastructure.persistence.po.DepartmentUserPO;

@Mapper
@MapperConfig
public interface DepartmentTransformer {

	/**
	 * 实例
	 */
	DepartmentTransformer INSTANCE = Mappers.getMapper(DepartmentTransformer.class);

	default Integer mapDepartmentID(Department.DepartmentID value) {
		if (null == value) {
			return null;
		}
		return value.getId();
	}

	default DepartmentPO toDepartmentPO(Department department) {
		DepartmentPO departmentPO = new DepartmentPO();
		departmentPO.setId(this.mapDepartmentID(department.getId()));
		departmentPO.setCode(Optional.of(department.getCode()).orElse("GENERATING"));
		departmentPO.setParentId(department.getParentId());
		departmentPO.setLevelLabel(department.getLevelLabel());
		departmentPO.setSort(department.getSort());
		departmentPO.setStatus(department.getStatus().getValue());
		departmentPO.setManagerId(department.getManagerId());
		return departmentPO;
	}

	default List<DepartmentLanguagePO> toDepartmentLanguagePOList(List<MultiLanguage> names, Integer departmentId) {
		List<DepartmentLanguagePO> departmentLanguagePOList = new ArrayList<>(names.size());
		for (MultiLanguage multiLanguage : names) {
			departmentLanguagePOList.add(this.toDepartmentLanguagePO(multiLanguage, departmentId));
		}
		return departmentLanguagePOList;
	}

	default DepartmentLanguagePO toDepartmentLanguagePO(MultiLanguage multiLanguage, Integer departmentId) {
		DepartmentLanguagePO departmentLanguagePO = new DepartmentLanguagePO();
		departmentLanguagePO.setDepartmentId(departmentId);
		departmentLanguagePO.setName(multiLanguage.getName());
		departmentLanguagePO.setLanguage(multiLanguage.getLanguage());
		return departmentLanguagePO;
	}

	private List<MultiLanguage> toMultiLanguage(List<DepartmentLanguagePO> departmentLanguagePOList) {
		if (departmentLanguagePOList.isEmpty()) {
			return Collections.emptyList();
		}
		List<MultiLanguage> multiLanguages = new ArrayList<>(departmentLanguagePOList.size());
		for (DepartmentLanguagePO departmentLanguagePO : departmentLanguagePOList) {
			MultiLanguage multiLanguage = new MultiLanguage();
			multiLanguage.setLanguage(departmentLanguagePO.getLanguage());
			multiLanguage.setName(departmentLanguagePO.getName());
			multiLanguages.add(multiLanguage);
		}
		return multiLanguages;
	}

	default Department toDepartment(DepartmentPO departmentPO, List<DepartmentLanguagePO> departmentLanguagePOList) {

		Department department = new Department(Department.DepartmentID.of(departmentPO.getId()), departmentPO.getParentId(),
				departmentPO.getCode(), departmentPO.getManagerId(), DepartmentStatus.enumOf(departmentPO.getStatus()),
				departmentPO.getSort(), departmentPO.getLevelLabel(), this.toMultiLanguage(departmentLanguagePOList));
		return department;
	}

	default List<DepartmentTreeDto> toDepartmentTreePO(List<DepartmentPO> pos) {
		List<DepartmentTreeDto> treePOS = new ArrayList<>();
		for (DepartmentPO po : pos) {
			DepartmentTreeDto treePO = new DepartmentTreeDto();
			treePO.setCode(po.getCode());
			treePO.setParentId(po.getParentId());
			treePO.setId(po.getId());
			treePO.setSort(po.getSort());
			treePO.setStatus(po.getStatus());
			treePO.setName(po.getName());
			treePO.setUsers((List<DepartmentUserTreeDto>) JSON.parse(po.getUserJson()));
			treePO.setDeleted(po.getDeleted());
			treePOS.add(treePO);

		}
		return treePOS;
	}

	default List<DepartmentUserTreeDto> toDepartmentUserTreePO(List<DepartmentPO> pos) {
		List<DepartmentUserTreeDto> treePOS = new ArrayList<>();
		for (DepartmentPO po : pos) {
			DepartmentUserTreeDto treePO = new DepartmentUserTreeDto();
			treePO.setParentId(po.getParentId());
			treePO.setId(po.getId());
			treePO.setManagerName(po.getManagerName());
			treePO.setSort(po.getSort());
			treePO.setStatus(po.getStatus());
			treePO.setName(po.getName());
			// treePO.setUsers((List<DepartmentUserTreePO>) JSON.parse(po.getUserJson()));
			treePOS.add(treePO);
		}
		return treePOS;
	}

	default List<DepartmentUserSimpleDto> toDepartmentUserSimplePO(List<DepartmentUserPO> linkUserPOList) {
		List<DepartmentUserSimpleDto> userSimplePOList = new ArrayList<>();
		for (DepartmentUserPO po : linkUserPOList) {
			DepartmentUserSimpleDto departmentUserSimpleDto = new DepartmentUserSimpleDto();
			departmentUserSimpleDto.setUserId(po.getUserId());
			departmentUserSimpleDto.setUserName(po.getUserName());
			departmentUserSimpleDto.setDepartmentId(po.getDepartmentId());
			userSimplePOList.add(departmentUserSimpleDto);
		}
		return userSimplePOList;
	}

}
