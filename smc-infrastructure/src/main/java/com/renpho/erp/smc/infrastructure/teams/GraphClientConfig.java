// package com.renpho.erp.smc.infrastructure.teams;
//
// import com.azure.core.credential.TokenCredential;
// import com.microsoft.graph.serviceclient.GraphServiceClient;
// import org.springframework.context.annotation.Bean;
// import org.springframework.context.annotation.Configuration;
//
// @Configuration
// public class GraphClientConfig {
//
// private final TokenCredentialFactory credentialFactory;
//
// public GraphClientConfig(TokenCredentialFactory credentialFactory) {
// this.credentialFactory = credentialFactory;
// }
//
// @Bean
// public GraphServiceClient graphServiceClient() {
// TokenCredential credential = credentialFactory.createCredential();
// String[] scopes = new String[] { "https://graph.microsoft.com/.default" };
// return new GraphServiceClient(credential, scopes);
// }
//
// // @Bean(name = "graphicServiceClientByAccessToken")
// // @Scope(BeanDefinition.SCOPE_PROTOTYPE)
// // public GraphServiceClient graphClientByAccessToken(String accessToken) {
// // AuthenticationProvider authProvider = new TokenAuthAccessProvider(accessToken);
// // return new GraphServiceClient(authProvider);
// // }
//
// }
