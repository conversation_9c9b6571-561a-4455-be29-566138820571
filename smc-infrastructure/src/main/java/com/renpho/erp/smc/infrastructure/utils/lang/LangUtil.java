package com.renpho.erp.smc.infrastructure.utils.lang;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.JSON;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */

public final class LangUtil {

	/**
	 * 构造器设置为私有
	 */
	private LangUtil() {
	}

	/**
	 * 将数据库 Json String 转化为 MultiLanguage
	 * @param json 数据库多语言字段
	 * @return List 前端数组
	 */
	public static List<MultiLanguage> getMultiLanguage(String json) {

		if (json != null) {
			List<MultiLanguage> multiLanguages;
			try {
				ObjectMapper mapper = new ObjectMapper();
				multiLanguages = mapper.readValue(json, new TypeReference<>() {
				});
			}
			catch (Exception e) {
				throw new RuntimeException("数据库多语言字段格式错误");
			}

			return multiLanguages;
		}

		return null;
	}

	/**
	 * 将数据库 Json String 转化为 MultiLanguage
	 * @param json 数据库多语言字段
	 * @return List 前端数组
	 */
	public static List<com.renpho.erp.smc.domain.systemsetting.MultiLanguage> getMultiLanguage2(String json) {

		List<com.renpho.erp.smc.domain.systemsetting.MultiLanguage> multiLanguages = new ArrayList<>();
		if (json != null) {
			try {
				ObjectMapper mapper = new ObjectMapper();
				multiLanguages = mapper.readValue(json, new TypeReference<>() {
				});
			}
			catch (Exception e) {
				throw new RuntimeException("数据库多语言字段格式错误");
			}
		}

		return multiLanguages;
	}

	/**
	 * 将 MultiLanguage 转化为 Json String
	 * @param multiLanguages 前端数组
	 * @return String 数据库多语言字段
	 */
	public static String getMultiLanguageString(List<MultiLanguage> multiLanguages) {
		if (CollUtil.isNotEmpty(multiLanguages)) {
			return JSON.toJSONString(multiLanguages);
		}

		return null;
	}

	/**
	 * 将 MultiLanguage 转化为 Map
	 * @param multiLanguages 前端数组
	 * @return String 数据库多语言字段
	 */
	public static Map<String, String> getMultiLanguageMap(List<MultiLanguage> multiLanguages) {
		if (CollUtil.isNotEmpty(multiLanguages)) {
			Map<String, String> map = new java.util.HashMap<>();
			for (MultiLanguage multiLanguage : multiLanguages) {
				map.put(multiLanguage.getLanguage(), multiLanguage.getName());
			}
			return map;
		}
		return null;
	}

}
