package com.renpho.erp.smc.infrastructure.utils.api;

import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.springframework.stereotype.Component;

import lombok.Getter;

/**
 * <AUTHOR>
 * @Date 2024/10/24 18:59
 */
@Component
public class NowApiHttpClientUtil {

	@Getter
	private CloseableHttpClient httpClient;

	public NowApiHttpClientUtil() {
		this.httpClient = HttpClients.createDefault();
	}

}
