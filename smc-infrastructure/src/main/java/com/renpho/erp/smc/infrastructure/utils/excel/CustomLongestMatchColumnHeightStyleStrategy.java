package com.renpho.erp.smc.infrastructure.utils.excel;

import com.alibaba.excel.write.handler.RowWriteHandler;
import com.alibaba.excel.write.handler.context.RowWriteHandlerContext;
import com.alibaba.excel.write.metadata.holder.WriteSheetHolder;
import org.apache.poi.ss.usermodel.*;

import java.util.Iterator;

/**
 * 自适应列高，单元格设置自动换行.
 *
 * <AUTHOR>
 * @date 2022/4/28 11:02
 */
public class CustomLongestMatchColumnHeightStyleStrategy implements RowWriteHandler {

	/**
	 * 最大高度
	 */
	private static final Integer MAX_COLUMN_HEIGHT = 6;

	/**
	 * 自动换行样式
	 */
	private CellStyle cellStyleWrapText;

	@Override
	public void afterRowDispose(RowWriteHandlerContext context) {
		if (context.getHead() != null) {
			if (!context.getHead()) {
				this.setContentColumnHeight(context.getWriteSheetHolder(), context.getRow());
			}
		}
	}

	protected void setContentColumnHeight(WriteSheetHolder writeSheetHolder, Row row) {
		Iterator<Cell> cellIterator = row.cellIterator();
		Workbook workbook = writeSheetHolder.getSheet().getWorkbook();
		CellStyle cellStyle;
		// 避免重复创建样式
		if (cellStyleWrapText != null) {
			cellStyle = cellStyleWrapText;
		}
		else {
			cellStyle = workbook.createCellStyle();
			cellStyle.setWrapText(true);
			cellStyleWrapText = cellStyle;
		}
		if (!cellIterator.hasNext()) {
			return;
		}
		// 默认为 1行高度
		int maxHeight = 1;
		while (cellIterator.hasNext()) {
			Cell cell = cellIterator.next();
			if (cell.getCellType() == CellType.STRING) {
				if (cell.getStringCellValue().contains("\n")) {
					cell.setCellStyle(cellStyle);
					// 所需行数
					int heightNum = cell.getStringCellValue().split("\n").length;
					// 高度最高3行
					if (heightNum >= MAX_COLUMN_HEIGHT) {
						maxHeight = MAX_COLUMN_HEIGHT;
					}
					else if (maxHeight < heightNum) {
						maxHeight = heightNum;
					}
				}
			}
		}
		// 如果已设置的高度小于本次计算的高度则重新设置，否则不进行设置
		if (row.getHeight() < (short) (maxHeight * 300)) {
			row.setHeight((short) (maxHeight * 300));
		}
	}

}
