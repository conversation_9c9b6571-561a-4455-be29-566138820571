package com.renpho.erp.smc.infrastructure.utils.excel;

import java.util.ArrayList;
import java.util.List;

import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;

public class ExcelListener<T> extends AnalysisEventListener<T> {

	private List<T> data = new ArrayList<>();

	// 从第二行开始读取，把每行内容封装到t对象里面
	@Override
	public void invoke(T t, AnalysisContext analysisContext) {
		data.add(t);
	}

	public List<T> getData() {
		return data;
	}

	@Override
	public void doAfterAllAnalysed(AnalysisContext analysisContext) {

	}

}
