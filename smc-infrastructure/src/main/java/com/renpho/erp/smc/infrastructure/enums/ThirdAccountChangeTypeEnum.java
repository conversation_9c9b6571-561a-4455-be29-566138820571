package com.renpho.erp.smc.infrastructure.enums;

import lombok.Getter;

/**
 * 第三方账号变更类型枚举
 *
 * <AUTHOR>
 * @Date 2025/6/26 14:44
 **/
@Getter
public enum ThirdAccountChangeTypeEnum {

	/**
	 * 用户
	 */
	USER("USER", "用户"),

	/**
	 * 部门
	 */
	DEPT("DEPT", "部门");

	/**
	 * 代码
	 */
	private final String code;

	/**
	 * 描述
	 */
	private final String desc;

	/**
	 * 注意类型枚举
	 * @param code 代码
	 * @param desc 描述
	 */
	ThirdAccountChangeTypeEnum(String code, String desc) {
		this.code = code;
		this.desc = desc;
	}

	/**
	 * 根据编码获取枚举
	 * @return com.renpho.erp.smc.infrastructure.enums.AccountTypeEnum
	 * <AUTHOR>
	 * @Date 14:45 2025/6/26
	 * @Param [code]
	 **/
	public static ThirdAccountChangeTypeEnum getByCode(String code) {
		for (ThirdAccountChangeTypeEnum type : ThirdAccountChangeTypeEnum.values()) {
			if (type.getCode().equals(code)) {
				return type;
			}
		}
		return null;
	}

}
