package com.renpho.erp.smc.infrastructure.redis;

import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.springframework.data.redis.core.RedisCallback;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Component;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.collect.Lists;
import com.renpho.erp.smc.domain.authoritymanagement.role.UserDataPermission;
import com.renpho.erp.smc.domain.authoritymanagement.role.UserFieldPermission;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * redis用户菜单权限接口.
 *
 * <AUTHOR>
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class UserMenuPermissionCache {

	private final RedisTemplate redisTemplate;

	/**
	 * 用户菜单数据权限集 缓存key
	 */
	private static final String USER_DATA_PERMISSION_KEY = "smc:user:dataPermission:";

	/**
	 * 用户数据敏感权限集 缓存key
	 */
	private static final String USER_DATA_SENSITIVE_KEY = "smc:user:dataSensitive:";

	/**
	 * 批量设置用户菜单数据权限缓存（先删，再设置）
	 * @param userDataPermissionList 用户数据权限列表
	 */
	public void batchSetUserMenuPermission(final List<UserDataPermission> userDataPermissionList) {
		if (CollectionUtils.isEmpty(userDataPermissionList)) {
			return;
		}
		// 批量删除用户数据权限缓存
		this.batchUnlinkUserMenuPermission(userDataPermissionList);
		// 批量设置用户数据权限缓存
		this.batchHashUserMenuPermission(userDataPermissionList);

	}

	/**
	 * 批量删除用户菜单数据权限缓存
	 */
	private void batchUnlinkUserMenuPermission(final List<UserDataPermission> userDataPermissionList) {
		// 用户id列表
		final List<Integer> userIdList = userDataPermissionList.stream().map(UserDataPermission::getUserId).distinct().toList();
		// redis 6.x版本，使用UNLINK 异步删除，避免阻塞redis主线程
		this.batchUnlink(userIdList, UserMenuPermissionCache.USER_DATA_PERMISSION_KEY);
	}

	/**
	 * UNLINK 异步删除
	 */
	private void batchUnlink(final List<Integer> userIdList, final String key) {
		// redis 6.x版本，使用UNLINK 异步删除，避免阻塞redis主线程
		// 如果删除的键很多，分批处理
		final int batchSize = 100;
		for (int i = 0; i < userIdList.size(); i += batchSize) {
			final int end = Math.min(i + batchSize, userIdList.size());
			final Set<String> batchKeys = userIdList.subList(i, end).stream().map(u -> key + u).collect(Collectors.toSet());
			// 每批执行 unlink
			this.redisTemplate.unlink(batchKeys);
			// 后续观察，如果异步删除导致redis负载高，这里可以睡眠一下再继续执行
		}
	}

	/**
	 * 批量设置用户菜单数据权限缓存
	 */
	private void batchHashUserMenuPermission(final List<UserDataPermission> userDataPermissionList) {
		// 多个用户，多个Hash，用Pipeline单个网络请求
		this.redisTemplate.executePipelined(((RedisCallback<Void>) connection -> {
			userDataPermissionList.forEach(userDataPermission -> {
				// 单个用户的数据权限
				final Map<String, Set<Integer>> menuMap = userDataPermission.getMenuMap();
				if (MapUtils.isEmpty(menuMap)) {
					return;
				}
				// 后续观察，单个用户是否有很多菜单及数据权限，如果有大key问题，这里的hash再优化拆分成多个hash
				final String key = UserMenuPermissionCache.USER_DATA_PERMISSION_KEY + userDataPermission.getUserId();
				final Map<byte[], byte[]> map = menuMap.entrySet()
					.stream()
					.collect(Collectors.toMap(entry -> entry.getKey().getBytes(), entry -> {
						try {
							return new ObjectMapper().writeValueAsString(entry.getValue()).getBytes();
						}
						catch (final JsonProcessingException e) {
							throw new RuntimeException(e);
						}
					}));
				connection.hashCommands().hMSet(key.getBytes(), map);
			});
			return null;
		}));
	}

	/**
	 * 批量设置用户字段脱敏权限缓存（先删，再设置）
	 * @param userFieldPermissionList 用户字段脱敏权限列表
	 */
	public void batchSetUserFieldPermission(final List<UserFieldPermission> userFieldPermissionList) {
		if (CollectionUtils.isEmpty(userFieldPermissionList)) {
			return;
		}
		// 批量删除用户字段敏感权限缓存
		this.batchUnlinkUserFieldPermission(userFieldPermissionList);
		// 批量设置用户字段敏感权限缓存
		this.batchHashUserFieldPermission(userFieldPermissionList);
	}

	/**
	 * 批量设置用户字段敏感权限缓存
	 */
	private void batchHashUserFieldPermission(final List<UserFieldPermission> userFieldPermissionList) {
		// 多个用户，多个Hash，用Pipeline单个网络请求
		this.redisTemplate.executePipelined(((RedisCallback<Void>) connection -> {
			userFieldPermissionList.forEach(userMenuPermission -> {
				// 单个用户的数据权限
				Map<String, Boolean> fieldMap = userMenuPermission.getFieldMap();
				if (MapUtils.isEmpty(fieldMap)) {
					fieldMap = new HashMap<>();
					fieldMap.put("*", Boolean.FALSE); // 当没有设置权限时，设置为星号，表示都不可见，减少客户端无数据时，远程调用
				}
				final String key = UserMenuPermissionCache.USER_DATA_SENSITIVE_KEY + userMenuPermission.getUserId();
				// 如果有很多很多字段，大key问题，这里的hash再优化拆分成多个hash
				final Map<byte[], byte[]> map = fieldMap.entrySet()
					.stream()
					.collect(Collectors.toMap(entry -> entry.getKey().getBytes(), entry -> entry.getValue().toString().getBytes()));
				connection.hashCommands().hMSet(key.getBytes(), map);
			});
			return null;
		}));
	}

	/**
	 * 批量删除用户字段敏感权限缓存
	 * @param userFieldPermissionList 用户字段敏感权限列表
	 */
	private void batchUnlinkUserFieldPermission(final List<UserFieldPermission> userFieldPermissionList) {
		// 用户id列表
		final List<Integer> userIdList = userFieldPermissionList.stream().map(UserFieldPermission::getUserId).distinct().toList();
		// redis 6.x版本，使用UNLINK 异步删除，避免阻塞redis主线程
		this.batchUnlink(userIdList, UserMenuPermissionCache.USER_DATA_SENSITIVE_KEY);
	}

	/**
	 * 获取用户数据权限缓存
	 * @param userId 用户id
	 * @return 用户数据权限
	 */
	public UserDataPermission getDataPermission(final Integer userId) {
		final Map<String, Object> entries = this.redisTemplate.opsForHash()
			.entries(UserMenuPermissionCache.USER_DATA_PERMISSION_KEY + userId);
		if (entries.isEmpty()) {
			return null;
		}
		final UserDataPermission userDataPermission = new UserDataPermission();
		userDataPermission.setUserId(userId);
		final ObjectMapper objectMapper = new ObjectMapper();
		final Map<String, Set<Integer>> menuMap = entries.entrySet().stream().collect(Collectors.toMap(Map.Entry::getKey, entry -> {
			try {
				return objectMapper.readValue(entry.getValue().toString(),
						objectMapper.getTypeFactory().constructCollectionType(HashSet.class, Integer.class));
			}
			catch (final JsonProcessingException e) {
				throw new RuntimeException(e);
			}
		}));
		userDataPermission.setMenuMap(menuMap);
		return userDataPermission;
	}

	/**
	 * 设置用户数据权限缓存
	 * @param userDataPermission 用户数据权限
	 */
	public void setDataPermission(final UserDataPermission userDataPermission) {
		this.batchHashUserMenuPermission(Lists.newArrayList(userDataPermission));
	}

}
