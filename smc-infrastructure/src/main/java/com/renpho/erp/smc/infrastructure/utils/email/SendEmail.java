package com.renpho.erp.smc.infrastructure.utils.email;

import java.util.List;

import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
public class SendEmail {

	/**
	 * 本方帐号
	 */
	String username;

	/**
	 * 密码/密钥
	 */
	String password;

	/**
	 * 主题
	 */
	String title;

	/**
	 * 接收人
	 */
	List<String> receivers;

	/**
	 * 抄送人
	 */
	List<String> receiverCcs;

	/**
	 * 密抄人
	 */
	List<String> receiverBccs;

	/**
	 * 内容
	 */
	String content;

	/**
	 * 别名
	 */
	String mailSploitName;

	/**
	 * 拼接别名
	 */
	String mailSploitEmail;

	/**
	 * 附件集合
	 */
	List<EmailUrl> emailUrls;

	/**
	 * 邮箱地址例:smtp.qq.com
	 */
	String host;

	/**
	 * 端口号
	 */
	Integer port;

	@Override
	public String toString() {
		return "SendEmail{" + "username='" + username + '\'' + ", password='" + password + '\'' + ", title='" + title + '\''
				+ ", receivers=" + receivers + ", receiverCcs=" + receiverCcs + ", receiverBccs=" + receiverBccs + ", content='" + content
				+ '\'' + ", mailSploitName='" + mailSploitName + '\'' + ", mailSploitEmail='" + mailSploitEmail + '\'' + ", emailUrls="
				+ emailUrls + ", host='" + host + '\'' + ", port=" + port + '}';
	}

}
