package com.renpho.erp.smc.infrastructure.log;

import com.alibaba.fastjson.JSONObject;
import com.renpho.erp.oplog.log.repo.MsgLogData;
import com.renpho.erp.oplog.log.repo.SysOperLog;
import com.renpho.erp.smc.infrastructure.persistence.mapper.SysOperLogMapper;
import com.renpho.erp.smc.infrastructure.persistence.po.SysOperLogPO;
import com.renpho.erp.smc.infrastructure.persistence.po.transformer.SysOperLogTransformer;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import java.util.function.Consumer;

@Repository
@RequiredArgsConstructor
@Configuration
@Slf4j
public class AsyncLogRepositoryImpl {

	private final SysOperLogMapper sysOperLogMapper;

	@Bean
	public Consumer<MsgLogData> opLogAutoConsumer() {
		AsyncLogRepositoryImpl asyncLog = this;
		return new Consumer<MsgLogData>() {
			@Override
			public void accept(MsgLogData msgData) {
				log.info("@opLogAutoConsumer: {}", msgData.toString());
				try {
					if (msgData.getContent() != null) {
						asyncLog.saveSysLog(msgData.getContent());
					}
					if (CollectionUtils.isNotEmpty(msgData.getContentList())) {
						for (SysOperLog sysOperLog : msgData.getContentList()) {
							try {
								asyncLog.saveSysLog(sysOperLog);
							}
							catch (Exception e) {
								log.error(e.getMessage(), e);
							}
						}
					}
				}
				catch (Exception e) {
					log.error("@opLogAutoConsumer_error:{} msgid:{} json:{}", e, msgData.getId(), JSONObject.toJSONString(msgData));
				}
			}
		};
	}

	@Transactional(rollbackFor = Exception.class)
	public void saveSysLog(SysOperLog sysOperLog) {
		SysOperLogPO po = SysOperLogTransformer.INSTANCE.toItemPO(sysOperLog);
		sysOperLogMapper.insert(po);
	}

	public void saveForUpdateLog(String json) {
		// TODO Auto-generated method stub
		throw new UnsupportedOperationException("Unimplemented method 'saveForUpdateLog'");
	}

}