package com.renpho.erp.smc.infrastructure.enums;

import lombok.Getter;

/**
 * 丁字计划状态枚举
 *
 * <AUTHOR>
 * @date 2025/04/07
 */
@Getter
public enum DingDingScheduleStatusEnum {

	/**
	 * 公认
	 */
	ACCEPTED("accepted"),
	/**
	 * 确认
	 */
	CONFIRMED("confirmed"),
	/**
	 * 取消
	 */
	CANCELLED("cancelled");

	/**
	 * 地位
	 */
	private final String status;

	DingDingScheduleStatusEnum(String status) {
		this.status = status;
	}

}