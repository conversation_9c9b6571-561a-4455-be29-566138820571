package com.renpho.erp.smc.infrastructure.persistence.po.transformer;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;

import org.mapstruct.Mapper;
import org.mapstruct.MapperConfig;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

import com.renpho.erp.smc.domain.authoritymanagement.role.Role;
import com.renpho.erp.smc.domain.systemsetting.MultiLanguage;
import com.renpho.erp.smc.infrastructure.persistence.po.RoleLanguagePO;
import com.renpho.erp.smc.infrastructure.persistence.po.RoleMenuPO;
import com.renpho.erp.smc.infrastructure.persistence.po.RolePO;

/**
 * 角色转换.
 *
 * <AUTHOR>
 */
@Mapper
@MapperConfig
public interface RoleTransformer {

	/**
	 * 角色转换实例
	 */
	RoleTransformer INSTANCE = Mappers.getMapper(RoleTransformer.class);

	@Mapping(expression = "java(map(role.getLabel()))", target = "label")
	RolePO toRolePO(Role role);

	@Mapping(expression = "java(map(role.getLabel()))", target = "label")
	Role toRole(RolePO role);

	List<Role.RoleMenu> toRoleMenuList(List<RoleMenuPO> roleMenuPOList);

	default Role.RoleID map(Integer value) {
		return Role.RoleID.of(value);
	}

	default Integer map(Role.RoleID value) {
		return Objects.isNull(value) ? null : value.getId();
	}

	default List<String> map(String label) {
		if (null == label) {
			return null;
		}
		return Arrays.stream(label.split(",")).toList();
	}

	default String map(List<String> label) {
		if (null == label) {
			return null;
		}
		return String.join(",", label);
	}

	default List<RoleLanguagePO> toRoleLanguagePOList(List<MultiLanguage> names, Integer roleId) {
		List<RoleLanguagePO> roleLanguagePOList = new ArrayList<>(names.size());
		for (MultiLanguage multiLanguage : names) {
			roleLanguagePOList.add(this.toRoleLanguagePO(multiLanguage, roleId));
		}
		return roleLanguagePOList;
	}

	default RoleLanguagePO toRoleLanguagePO(MultiLanguage multiLanguage, Integer roleId) {
		RoleLanguagePO roleLanguagePO = new RoleLanguagePO();
		roleLanguagePO.setRoleId(roleId);
		roleLanguagePO.setName(multiLanguage.getName());
		roleLanguagePO.setLanguage(multiLanguage.getLanguage());
		return roleLanguagePO;
	}

}
