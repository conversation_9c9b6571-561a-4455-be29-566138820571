package com.renpho.erp.smc.infrastructure.utils;

import cn.hutool.extra.spring.SpringUtil;
import lombok.experimental.UtilityClass;
import org.springframework.data.redis.core.RedisTemplate;

/**
 * 缓存工具类.
 *
 * <AUTHOR>
 */
@UtilityClass
public class CacheUtil {

	/**
	 * 设置缓存.
	 * @param key 缓存键
	 * @param value 缓存值
	 */
	public void put(String key, Object value) {
		getRedisTemplate().opsForValue().set(key, value);
	}

	/**
	 * 删除缓存.
	 * @param key 缓存键
	 */
	public void remove(String key) {
		getRedisTemplate().delete(key);
	}

	/**
	 * 获取缓存实例.
	 * @return RedisTemplate
	 */
	public RedisTemplate<String, Object> getRedisTemplate() {
		return SpringUtil.getBean("redisTemplate");
	}

}
