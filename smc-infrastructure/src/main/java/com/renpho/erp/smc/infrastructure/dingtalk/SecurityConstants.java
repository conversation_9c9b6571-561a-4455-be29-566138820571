package com.renpho.erp.smc.infrastructure.dingtalk;

/**
 * 权限相关通用常量.
 *
 * <AUTHOR>
 */
public final class SecurityConstants {

	private SecurityConstants() {

	}

	/**
	 * 用户ID字段
	 */
	public static final String DETAILS_USER_ID = "user_id";

	/**
	 * 用户名字段
	 */
	public static final String DETAILS_USERNAME = "username";

	/**
	 * 授权信息字段
	 */
	public static final String AUTHORIZATION_HEADER = "authorization";

	/**
	 * 请求来源
	 */
	public static final String FROM_SOURCE = "from-source";

	/**
	 * 内部请求
	 */
	public static final String INNER = "inner";

	/**
	 * 用户标识
	 */
	public static final String USER_KEY = "user_key";

	/**
	 * 登录用户
	 */
	public static final String LOGIN_USER = "login_user";

	/** 钉钉接口token */
	public static final String DINGTALK_TOKEN = "dingTalk:token";

	/**
	 * 授权信息字段
	 */
	public static final String TRACE_ID = "TRACE_ID";

}
