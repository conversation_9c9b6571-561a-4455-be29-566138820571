package com.renpho.erp.smc.infrastructure.enums;

import lombok.Getter;

/**
 * 推动状态枚举
 *
 * <AUTHOR>
 * @date 2025/03/28
 */
@Getter
public enum PushStatusEnum {

	/**
	 * 草稿
	 */
	DRAFT(0),
	/**
	 * 出版
	 */
	PUBLISHED(1);

	/**
	 * 价值
	 */
	private final int value;

	/**
	 * 推动状态枚举
	 * @param value 价值
	 */
	PushStatusEnum(int value) {
		this.value = value;
	}

	/**
	 * 从价值
	 * @param value 价值
	 * @return {@link PushStatusEnum }
	 */
	public static PushStatusEnum fromValue(int value) {
		for (PushStatusEnum status : PushStatusEnum.values()) {
			if (status.getValue() == value) {
				return status;
			}
		}
		throw new IllegalArgumentException("Unknown value: " + value);
	}

}