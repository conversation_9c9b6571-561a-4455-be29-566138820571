package com.renpho.erp.smc.infrastructure.persistence.po.transformer;

import com.renpho.erp.smc.domain.organizationmanagement.costCenterAssign.CostCenterAssign;

import com.renpho.erp.smc.infrastructure.persistence.po.*;
import org.mapstruct.Mapper;
import org.mapstruct.MapperConfig;
import org.mapstruct.factory.Mappers;

@Mapper
@MapperConfig
public interface CostCenterAssignTransformer {

	/**
	 * 实例
	 */
	CostCenterAssignTransformer INSTANCE = Mappers.getMapper(CostCenterAssignTransformer.class);

	default Integer mapCostCenterAssignID(CostCenterAssign.CostCenterAssignID value) {
		if (null == value) {
			return null;
		}
		return value.getId();
	}

	default CostCenterAssignPO toCostCenterAssignPO(CostCenterAssign costCenterAssign) {
		CostCenterAssignPO po = new CostCenterAssignPO();
		po.setId(mapCostCenterAssignID(costCenterAssign.getId()));
		po.setUserId(costCenterAssign.getUserId());
		po.setCostCenterId(costCenterAssign.getCostCenterId());
		po.setEfficetiveDate(costCenterAssign.getEfficetiveDate());
		return po;
	}

}
