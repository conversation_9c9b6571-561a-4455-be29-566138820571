package com.renpho.erp.smc.infrastructure.persistence.po.transformer;

import com.alibaba.fastjson.JSON;
import com.renpho.erp.smc.domain.systemsetting.Status;
import com.renpho.erp.smc.domain.systemsetting.dict.Dict;
import com.renpho.erp.smc.domain.systemsetting.dict.DictItem;
import com.renpho.erp.smc.domain.systemsetting.dict.DictStatus;
import com.renpho.erp.smc.infrastructure.persistence.po.DictItemPO;
import com.renpho.erp.smc.infrastructure.persistence.po.DictTypeLanguagePO;
import com.renpho.erp.smc.infrastructure.persistence.po.DictTypePO;
import com.renpho.erp.smc.infrastructure.utils.lang.MultiLanguage;
import org.mapstruct.Mapper;
import org.mapstruct.MapperConfig;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

import java.util.List;

@Mapper
@MapperConfig
public interface DictTransformer {

	/**
	 * 实例
	 */
	DictTransformer INSTANCE = Mappers.getMapper(DictTransformer.class);

	@Mapping(source = "dict.type", target = "dictType")
	default DictTypePO toDictTypePO(Dict dict) {
		if (dict == null) {
			return null;
		}

		DictTypePO dictTypePO = new DictTypePO();

		dictTypePO.setDictType(dict.getType());
		dictTypePO.setId(mapDictID(dict.getId()));
		dictTypePO.setStatus(mapDictStatus(dict.getStatus()));
		dictTypePO.setRemark(dict.getRemark());

		dictTypePO.setDictName(JSON.toJSONString(dict.getNames()));

		return dictTypePO;
	};

	DictItemPO toDictItemPO(DictItem item);

	default Integer mapDictID(Dict.DictID value) {
		if (null == value) {
			return null;
		}
		return value.getId();
	}

	default Integer mapDictItemID(DictItem.DictItemID value) {
		return value.getId();
	}

	default Integer mapDictStatus(DictStatus value) {
		return value.getValue();
	}

	default Integer mapStatus(Status value) {
		return value.getValue();
	}

	default DictItem.DictItemID mapIdToDictItemId(Integer value) {
		return DictItem.DictItemID.of(value);
	}

	DictItem toDictItem(DictItemPO dictItemPO);

	List<DictItem> toDictItemList(List<DictItemPO> itemPOList);

	List<MultiLanguage> getMultiLanguageString(List<com.renpho.erp.smc.domain.systemsetting.MultiLanguage> names);

	default DictTypeLanguagePO toDictTypeLanguagePO(com.renpho.erp.smc.domain.systemsetting.MultiLanguage multiLanguage, Integer id) {
		DictTypeLanguagePO po = new DictTypeLanguagePO();
		po.setLanguage(multiLanguage.getLanguage());
		po.setName(multiLanguage.getName());
		po.setDictTypeId(id);
		return po;
	}

}
