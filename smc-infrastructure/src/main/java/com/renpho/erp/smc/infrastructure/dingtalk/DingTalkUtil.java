package com.renpho.erp.smc.infrastructure.dingtalk;

import cn.hutool.core.date.DateTime;
import cn.hutool.core.util.ArrayUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.aliyun.dingtalkcontact_1_0.models.GetUserHeaders;
import com.aliyun.dingtalkcontact_1_0.models.GetUserResponseBody;
import com.aliyun.dingtalkoauth2_1_0.models.GetUserTokenRequest;
import com.aliyun.dingtalkoauth2_1_0.models.GetUserTokenResponse;
import com.aliyun.teaopenapi.models.Config;
import com.aliyun.teautil.models.RuntimeOptions;
import com.dingtalk.api.DefaultDingTalkClient;
import com.dingtalk.api.DingTalkClient;
import com.dingtalk.api.request.*;
import com.dingtalk.api.response.*;
import com.renpho.karma.dto.R;
import com.taobao.api.ApiException;
import com.taobao.api.FileItem;
import com.taobao.api.TaobaoRequest;
import com.taobao.api.TaobaoResponse;
import io.micrometer.common.util.StringUtils;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.binary.Base64;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.crypto.Mac;
import javax.crypto.spec.SecretKeySpec;
import java.net.URLEncoder;
import java.util.*;
import java.util.concurrent.TimeUnit;

/**
 * 丁说话util
 *
 * <AUTHOR>
 * @date 2025/07/07
 */
@Slf4j
@Component
public class DingTalkUtil {

	/** 应用程序密钥 */
	@Value("${dingTalk.APP_KEY}")
	private String appKey;

	/** App Secret */
	@Value("${dingTalk.APP_SECRET}")
	private String appSecret;

	/** 代理ID */
	@Getter
	@Value("${dingTalk.AGENT_ID}")
	private Long agentId;

	/** AES密钥 */
	private String aesKey;

	/** aes令牌 */
	private String aesToken;

	/** 重新服务 */
	@Autowired
	private RedisService redisService;

	/**
	 * tree
	 * @return {@link String }
	 */

	public String getToken() {
		String token = null;
		if (redisService != null) {
			token = redisService.getCacheObject(SecurityConstants.DINGTALK_TOKEN);
		}
		if (token == null) {
			OapiGettokenRequest request = new OapiGettokenRequest();
			request.setAppkey(this.appKey);
			request.setAppsecret(this.appSecret);
			request.setHttpMethod("GET");
			OapiGettokenResponse response = execute(request, ConstantUrl.DINGTALK_GET_TOKEN, null);
			token = response.getAccessToken();
			if (redisService != null) {
				redisService.setCacheObject(SecurityConstants.DINGTALK_TOKEN, token, 60L, TimeUnit.MINUTES);

			}
		}
		return token;
	}

	/**
	 * 获取用户信息 企业应用-免登
	 * @param requestAuthCode params
	 * @param accessToken params
	 * @return res userId
	 */
	public String getUserInfo(String requestAuthCode, String accessToken) {
		// 获取用户信息
		OapiUserGetuserinfoRequest request = new OapiUserGetuserinfoRequest();
		request.setCode(requestAuthCode);
		request.setHttpMethod("GET");
		OapiUserGetuserinfoResponse response = execute(request, ConstantUrl.DINGTALK_GET_USER_INFO, accessToken);
		if (response.getErrcode() != 0L) {
			throw new RuntimeException(response.getErrmsg());
		}
		return response.getUserid();
	}

	/**
	 * 根据userId获取用户详情
	 * @param userId yonhu
	 * @param accessToken params
	 * @return res
	 */
	public R<OapiV2UserGetResponse.UserGetResponse> getUserDetail(String userId, String accessToken) {
		OapiV2UserGetRequest request = new OapiV2UserGetRequest();
		request.setUserid(userId);
		request.setLanguage("zh_CN");
		OapiV2UserGetResponse response = execute(request, ConstantUrl.DINGTALK_GET_USER_DETAIL, accessToken);
		// errorCode:0 请求成功
		if (response.getErrcode() != 0L) {
			throw new RuntimeException("获取用户详情失败:" + response.getErrmsg());
		}
		return R.success(response.getResult());
	}

	/**
	 * 获取部门列表
	 * @param deptId Dept ID
	 * @param mainList 主列表
	 */
	public void queryAllDepartment(long deptId, List<TreeDepartment> mainList) {
		log.info("@getDepartment:{}", deptId);
		this.queryDepartmentListByParentDeptId(deptId, mainList);
		log.info("@getDepartment finish");

	}

	/**
	 * 查询部门列表经过父母部门ID
	 * @param deptId Dept ID
	 * @param treeList 树列
	 */
	private void queryDepartmentListByParentDeptId(long deptId, List<TreeDepartment> treeList) {
		log.info("@getDepartmentList::{}", deptId);

		DingTalkClient client = new DefaultDingTalkClient("https://oapi.dingtalk.com/topapi/v2/department/listsub");
		OapiV2DepartmentListsubRequest req = new OapiV2DepartmentListsubRequest();
		req.setDeptId(deptId);
		req.setLanguage("zh_CN");
		String accessToken = getToken();
		OapiV2DepartmentListsubResponse rsp = null;
		try {
			rsp = client.execute(req, accessToken);
		}
		catch (ApiException e) {
			throw new RuntimeException(e);
		}
		JSONObject json = JSON.parseObject(rsp.getBody());
		JSONArray array = json.getJSONArray("result");
		if (array.isEmpty()) {
			return;
		}
		for (int i = 0; i < array.size(); i++) {
			TreeDepartment treeDepartment = new TreeDepartment();
			JSONObject dept = array.getJSONObject(i);
			Long clientDeptId = dept.getLong("dept_id");
			treeDepartment.setParentId(deptId);
			treeDepartment.setId(clientDeptId);
			treeDepartment.setName(dept.getString("name"));

			queryDepartmentListByParentDeptId(clientDeptId, treeDepartment.getChildren());

			treeList.add(treeDepartment);
		}
	}

	/**
	 * 获取部门下的用户列表
	 * @param deptId ok
	 * @param userInfoMap 用户信息图
	 * @throws Exception 例外
	 */
	public void queryUserInfoByDept(long deptId, Map<String, OumDingTalkUserDto> userInfoMap) throws Exception {

		OapiV2UserListResponse rsp = getOapiV2UserListResponse(deptId);
		if (!rsp.isSuccess()) {
			return;
		}
		JSONObject jsonObject = JSONObject.parseObject(rsp.getBody());
		JSONArray jsonArray = Optional.ofNullable(jsonObject)
			.map(s -> (s.getJSONObject("result")))
			.map(s -> s.getJSONArray("list"))
			.orElse(new JSONArray());
		for (int i = 0; i < jsonArray.size(); i++) {
			JSONObject json = jsonArray.getJSONObject(i);
			// 缓存起来，后续查询
			OumDingTalkUserDto third = new OumDingTalkUserDto();
			third.setAvatarUrl(json.getString("avatar"));
			third.setDeptId(deptId);
			third.setPlatform("dingtalk");
			third.setName(json.getString("name"));
			third.setUnionid(json.getString("unionid"));
			third.setUserid(json.getString("userid"));
			third.setJobNumber(json.getString("job_number"));
			third.setEmail(json.getString("email"));
			userInfoMap.put(json.getString("userid"), third);
		}
	}

	/**
	 * 获取OAPI V2用户列表响应
	 * @param deptId Dept ID
	 * @return {@link OapiV2UserListResponse }
	 * @throws ApiException API例外
	 */
	public OapiV2UserListResponse getOapiV2UserListResponse(long deptId) throws ApiException {
		log.info(Thread.currentThread().getName() + ":" + deptId);
		DingTalkClient client = new DefaultDingTalkClient("https://oapi.dingtalk.com/topapi/v2/user/list");
		OapiV2UserListRequest req = new OapiV2UserListRequest();
		req.setDeptId(deptId);
		req.setCursor(0L);
		req.setSize(100L);
		req.setOrderField("modify_desc");
		req.setContainAccessLimit(false);
		req.setLanguage("zh_CN");
		String accessToken = getToken();
		return client.execute(req, accessToken);
	}

	/**
	 * 根据userId获取用户详情
	 * @param userId uid
	 * @return res res
	 */
	public OapiV2UserGetResponse.UserGetResponse getUserDetail(String userId) {
		return getUserDetail(userId, getToken()).getData();
	}

	/**
	 * 通过代码获取用户信息
	 * @param loginTmpCode 登录TMP代码
	 * @return {@link String }
	 * @throws Exception 例外
	 */
	public String getUserInfoByCode(String loginTmpCode) throws Exception {
		DefaultDingTalkClient client = new DefaultDingTalkClient(ConstantUrl.DINGTALK_GET_USERINFO_BYCODE);
		OapiSnsGetuserinfoBycodeRequest request = new OapiSnsGetuserinfoBycodeRequest();
		// 通过扫描二维码，跳转指定的redirect_uri后，向url中追加的code临时授权码
		request.setTmpAuthCode(loginTmpCode);
		OapiSnsGetuserinfoBycodeResponse response = client.execute(request, this.appKey, this.appSecret);
		// 返回unionid
		return response.getUserInfo().getUnionid();
	}

	/**
	 * 根据unionid获取用户id，userId
	 * @param unionid params
	 * @param accessToken params
	 * @return res
	 */
	public String getUserIdByUnionId(String unionid, String accessToken) {
		OapiUserGetbyunionidRequest request = new OapiUserGetbyunionidRequest();
		request.setUnionid(unionid);
		OapiUserGetbyunionidResponse response = execute(request, ConstantUrl.DINGTALK_GET_USERID_BYUNIONID, accessToken);
		if (response.getErrcode() != 0L) {
			throw new RuntimeException(response.getErrmsg());
		}
		return response.getResult().getUserid();
	}

	/**
	 * 获取部门下人员列表 调用本接口获取指定部门中的用户详细信息
	 * @param deptId 部门id
	 * @param cursor 分页查询的游标
	 * @param size 分页大小
	 * @param accessToken 访问令牌
	 * @return res
	 */
	public OapiV2UserListResponse getUserGroupInfoDetails(Long deptId, Long cursor, Long size, String accessToken) {
		OapiV2UserListRequest request = new OapiV2UserListRequest();
		request.setDeptId(deptId);
		request.setCursor(cursor);
		request.setSize(size);
		request.setOrderField("modify_desc");
		request.setContainAccessLimit(false);
		request.setLanguage("zh_CN");
		OapiV2UserListResponse response = execute(request, "https://oapi.dingtalk.com/topapi/v2/user/list", accessToken);
		return response;
	}

	/**
	 * 获取部门详情
	 * @param deptId params
	 * @param accessToken params
	 * @return res
	 */
	public OapiV2DepartmentGetResponse.DeptGetResponse getDepartmentDetails(Long deptId, String accessToken) {
		OapiV2DepartmentGetRequest request = new OapiV2DepartmentGetRequest();
		request.setDeptId(deptId);
		request.setLanguage("zh_CN");
		OapiV2DepartmentGetResponse response = execute(request, "https://oapi.dingtalk.com/topapi/v2/department/get", accessToken);
		if (response.getErrcode() != 0L) {
			throw new RuntimeException(response.getErrmsg());
		}
		return response.getResult();
	}

	/**
	 * 获取部门详情
	 * @param deptId params
	 * @return res
	 */
	public OapiV2DepartmentGetResponse.DeptGetResponse getDepartmentDetails(Long deptId) {
		return getDepartmentDetails(deptId, getToken());
	}

	/**
	 * 获取指定部门的所有父部门列表
	 * @param deptId params
	 * @return res
	 */
	public List<Long> listParentByDept(Long deptId) {
		OapiV2DepartmentListparentbydeptRequest request = new OapiV2DepartmentListparentbydeptRequest();
		request.setDeptId(deptId);
		OapiV2DepartmentListparentbydeptResponse response = execute(request, ConstantUrl.DINGTALK_GET_PARENT_BY_DEPT, getToken());
		if (response.getErrcode() != 0L) {
			throw new RuntimeException(response.getErrmsg());
		}
		return response.getResult().getParentIdList();
	}

	/**
	 * 获取审批实例详情 更新时间：2021-08-03. 调用本接口根据审批实例ID，获取审批实例详情。详情包括审批表单信息、操作记录列表、操作人、抄送人、审批任务列表。
	 * https://oapi.dingtalk.com/topapi/processinstance/get
	 * @param processInstanceId 流程实例ID
	 * @param accessToken 访问令牌
	 * @return res
	 */
	public OapiProcessinstanceGetResponse.ProcessInstanceTopVo getProcessInstance(String processInstanceId, String accessToken) {
		OapiProcessinstanceGetRequest request = new OapiProcessinstanceGetRequest();
		request.setProcessInstanceId(processInstanceId);
		OapiProcessinstanceGetResponse response = execute(request, "https://oapi.dingtalk.com/topapi/processinstance/get", accessToken);
		return response.getProcessInstance();
	}

	/**
	 * 发送消息文本标记
	 * @param dingtalkUserIds DingTalk用户ID
	 * @param title 标题
	 * @param content 内容
	 */
	public void sendMessageTextMarkdown(String[] dingtalkUserIds, String title, String content) {
		if (null != dingtalkUserIds && dingtalkUserIds.length > 0) {
			OapiMessageCorpconversationAsyncsendV2Request request = new OapiMessageCorpconversationAsyncsendV2Request();
			// 指定用户接收，逗号分隔
			request.setUseridList(ArrayUtil.join(dingtalkUserIds, ","));
			OapiMessageCorpconversationAsyncsendV2Request.Msg msg = new OapiMessageCorpconversationAsyncsendV2Request.Msg();
			OapiMessageCorpconversationAsyncsendV2Request.Markdown markdown = new OapiMessageCorpconversationAsyncsendV2Request.Markdown();
			markdown.setText(content);
			markdown.setTitle(title);
			msg.setMarkdown(markdown);
			msg.setMsgtype("markdown");
			request.setMsg(msg);
			try {
				sendMessage(request);
			}
			catch (Exception e) {
				throw new RuntimeException(e.getMessage());
			}
		}
		else {
			throw new RuntimeException("必须指定用户接收工作通知");
		}
	}

	/**
	 * 发送消息
	 * @param request 要求
	 * @throws ApiException API例外
	 */
	private void sendMessage(OapiMessageCorpconversationAsyncsendV2Request request) throws ApiException {
		DingTalkClient client = new DefaultDingTalkClient(DingTalkStatus.TO_MESSAGE_CORPCONVERSATION);
		request.setAgentId(this.agentId);
		request.setToAllUser(false);
		// token从redis中获取
		String token = getToken();
		OapiMessageCorpconversationAsyncsendV2Response rsp = client.execute(request, token);
		if (rsp.getErrcode() == -1) {
			log.info(rsp.getBody());
			// 如果token过期则重新获取一次
			try {
				token = getToken();
				rsp = client.execute(request, token);
			}
			catch (Exception e) {
				e.printStackTrace();
			}
		}
		log.info(rsp.getBody());
	}

	/**
	 * 发送消息-文本
	 * @param dingtalkUserIds DingTalk用户ID
	 * @param messageText 消息文字
	 */
	public void sendMessageText(String[] dingtalkUserIds, String messageText) {
		if (null != dingtalkUserIds && dingtalkUserIds.length > 0) {
			OapiMessageCorpconversationAsyncsendV2Request request = new OapiMessageCorpconversationAsyncsendV2Request();
			// 指定用户接收，逗号分隔
			request.setUseridList(ArrayUtil.join(dingtalkUserIds, ","));
			OapiMessageCorpconversationAsyncsendV2Request.Msg msg = new OapiMessageCorpconversationAsyncsendV2Request.Msg();
			msg.setMsgtype("text");
			msg.setText(new OapiMessageCorpconversationAsyncsendV2Request.Text());
			msg.getText().setContent(messageText);
			request.setMsg(msg);
			try {
				sendMessage(request);
			}
			catch (Exception e) {
				throw new RuntimeException(e.getMessage());
			}
		}
		else {
			throw new RuntimeException("必须指定用户接收工作通知");
		}
	}

	/**
	 * 推送机器人消息 获取url签名
	 * @param milliseconds params
	 * @param secret params
	 * @return res
	 */
	public String sign(Long milliseconds, String secret) {
		try {
			// String secret =
			// "SECc96db194adf0f06370bba6bfa1981ea9a53b4a276498e9b9ac73b40184b2ee2f";
			String stringToSign = milliseconds + "\n" + secret;
			Mac mac = Mac.getInstance("HmacSHA256");
			mac.init(new SecretKeySpec(secret.getBytes("UTF-8"), "HmacSHA256"));
			byte[] signData = mac.doFinal(stringToSign.getBytes("UTF-8"));
			String sign = URLEncoder.encode(new String(Base64.encodeBase64(signData)), "UTF-8");
			return sign;
		}
		catch (Exception e) {
			e.printStackTrace();
		}
		return null;
	}

	/**
	 * 发起请求
	 * @param request params
	 * @param serverUrl params
	 * @param token params
	 * @return res
	 */
	public <T extends TaobaoResponse> T execute(TaobaoRequest<T> request, String serverUrl, String token) {
		DingTalkClient client = new DefaultDingTalkClient(serverUrl);
		try {
			T response = client.execute(request, token);
			return response;
		}
		catch (Exception e) {
			log.error("钉钉请求失败: {}", e.getMessage());
			e.printStackTrace();
			throw new RuntimeException("钉钉请求失败");
		}
	}

	/**
	 * 事件订阅sag 解密
	 * @param msgSignature 味精签名
	 * @param timeStamp 时间戳记
	 * @param nonce nonce
	 * @param encryptMsg 解密内容
	 * @return res
	 */
	public String decryptMsg(String msgSignature, String timeStamp, String nonce, String encryptMsg) {
		try {
			// 1. 从http请求中获取加解密参数
			// 2. 使用加解密类型
			// Constant.OWNER_KEY 说明：
			// 1、开发者后台配置的订阅事件为应用级事件推送，此时OWNER_KEY为应用的APP_KEY。
			// 2、调用订阅事件接口订阅的事件为企业级事件推送，
			// 此时OWNER_KEY为：企业的appkey（企业内部应用）或SUITE_KEY（三方应用）
			DingCallbackCrypto callbackCrypto = new DingCallbackCrypto(this.aesToken, this.aesKey, this.appKey);
			return callbackCrypto.getDecryptMsg(msgSignature, timeStamp, nonce, encryptMsg);
		}
		catch (Exception e) {
			e.printStackTrace();
		}
		return null;
	}

	/**
	 * 加密味精
	 * @param msg 味精
	 * @return {@link Map }<{@link String }, {@link String }>
	 */
	public Map<String, String> encryptMsg(String msg) {
		try {
			DingCallbackCrypto callbackCrypto = new DingCallbackCrypto(this.aesToken, this.aesKey, this.appKey);
			return callbackCrypto.getEncryptedMap(msg);
		}
		catch (Exception e) {
			e.printStackTrace();
		}
		return null;
	}

	/**
	 * 上传文件
	 * @param filePath 文件路径
	 * @param fileType 文件类型
	 * @return {@link OapiMediaUploadResponse }
	 */
	public OapiMediaUploadResponse uploadFile(String filePath, String fileType) {
		OapiMediaUploadResponse rsp = null;
		try {
			DingTalkClient client = new DefaultDingTalkClient("https://oapi.dingtalk.com/media/upload");
			OapiMediaUploadRequest req = new OapiMediaUploadRequest();
			req.setType(fileType);
			// 要上传的媒体文件
			FileItem item = new FileItem(filePath);
			req.setMedia(item);
			rsp = client.execute(req, this.getToken());
		}
		catch (ApiException e) {
			throw new RuntimeException(e);
		}
		log.info("钉钉上传媒体文件响应：{}", JSONUtil.toJsonStr(rsp));
		return rsp;
	}

	/**
	 * 发送机器人组消息
	 * @param msgParam msg param
	 * @param msgKey msg密钥
	 * @param openConversationId 公开对话ID
	 * @param robotCode 机器人代码
	 * @param token 令牌
	 * @param coolAppCode 酷应用程序代码
	 */
	public void sendRobotGroupMessages(String msgParam, String msgKey, String openConversationId, String robotCode, String token,
			String coolAppCode) {
		try {
			com.aliyun.teaopenapi.models.Config config = new com.aliyun.teaopenapi.models.Config();
			config.protocol = "https";
			config.regionId = "central";
			com.aliyun.dingtalkrobot_1_0.Client client = new com.aliyun.dingtalkrobot_1_0.Client(config);
			com.aliyun.dingtalkrobot_1_0.models.OrgGroupSendHeaders headers = new com.aliyun.dingtalkrobot_1_0.models.OrgGroupSendHeaders();
			headers.xAcsDingtalkAccessToken = this.getToken();
			com.aliyun.dingtalkrobot_1_0.models.OrgGroupSendRequest request = new com.aliyun.dingtalkrobot_1_0.models.OrgGroupSendRequest();
			request.setMsgKey(msgKey);
			request.setMsgParam(msgParam);
			request.setOpenConversationId(openConversationId);
			request.setRobotCode(robotCode);
			request.setToken(token);
			request.setCoolAppCode(coolAppCode);
			client.orgGroupSendWithOptions(request, headers, new com.aliyun.teautil.models.RuntimeOptions());
		}
		catch (Exception _err) {
			_err.printStackTrace();
			throw new RuntimeException(_err);
		}
	}

	/**
	 * 获取用户信息
	 * @param authCode 验证代码
	 * @return {@link GetUserResponseBody }
	 * @throws Exception 例外
	 */ // ======================新版api========================================================================================
	public GetUserResponseBody getUserInfo(String authCode) throws Exception {
		com.aliyun.dingtalkoauth2_1_0.Client client = authClient();
		GetUserTokenRequest getUserTokenRequest = new GetUserTokenRequest().setClientId(this.appKey)
			.setClientSecret(this.appSecret)
			.setCode(authCode)
			.setGrantType("authorization_code");
		GetUserTokenResponse getUserTokenResponse = client.getUserToken(getUserTokenRequest);
		String accessToken = getUserTokenResponse.getBody().getAccessToken();
		// 换取第三方用户登录token
		return getUserinfo(accessToken);

	}

	/**
	 * auth客户端
	 * @return {@link com.aliyun.dingtalkoauth2_1_0.Client }
	 * @throws Exception 例外
	 */
	private static com.aliyun.dingtalkoauth2_1_0.Client authClient() throws Exception {
		Config config = new Config();
		config.protocol = "https";
		config.regionId = "central";
		return new com.aliyun.dingtalkoauth2_1_0.Client(config);
	}

	/**
	 * 联系客户
	 * @return {@link com.aliyun.dingtalkcontact_1_0.Client }
	 * @throws Exception 例外
	 */
	private static com.aliyun.dingtalkcontact_1_0.Client contactClient() throws Exception {
		Config config = new Config();
		config.protocol = "https";
		config.regionId = "central";
		return new com.aliyun.dingtalkcontact_1_0.Client(config);
	}

	/**
	 * 获取用户个人信息
	 * @param accessToken params
	 * @return res
	 * @throws Exception 例外
	 */
	private static GetUserResponseBody getUserinfo(String accessToken) throws Exception {
		com.aliyun.dingtalkcontact_1_0.Client client = contactClient();
		GetUserHeaders getUserHeaders = new GetUserHeaders();
		getUserHeaders.xAcsDingtalkAccessToken = accessToken;

		GetUserResponseBody me = client.getUserWithOptions("me", getUserHeaders, new RuntimeOptions()).getBody();
		log.info(JSONUtil.parseObj(me).toString());
		return me;
	}

	/**
	 * 获取出勤清单
	 * @param workDateFrom 工作日期
	 * @param workDateTo 工作日期
	 * @param userIdList 用户ID列表
	 * @return {@link JSONArray }
	 */
	public JSONArray getAttendanceList(String workDateFrom, String workDateTo, List<String> userIdList) {
		JSONArray jsonArray = new JSONArray();
		try {
			requestAttendanceList(workDateFrom, workDateTo, userIdList, 0L, 50L, jsonArray);
		}
		catch (Exception e) {
			log.info("获取用户考勤记录异常", e);
		}
		return jsonArray;
	}

	/**
	 * 获取钉钉考勤记录
	 * @param workDateFrom 工作日期
	 * @param workDateTo 工作日期
	 * @param userIdList 用户ID列表
	 * @param offset 抵消
	 * @param limit 限制
	 * @param jsonArray Json Array
	 * @throws Exception 例外
	 */
	private void requestAttendanceList(String workDateFrom, String workDateTo, List<String> userIdList, Long offset, Long limit,
			JSONArray jsonArray) throws Exception {
		DingTalkClient client = new DefaultDingTalkClient(DingTalkStatus.GET_ATTENDANCE_LIST);
		OapiAttendanceListRequest req = new OapiAttendanceListRequest();
		req.setWorkDateFrom(workDateFrom);
		req.setWorkDateTo(workDateTo);
		req.setUserIdList(userIdList);
		req.setOffset(offset);
		req.setLimit(limit);
		String token = getToken();
		OapiAttendanceListResponse rsp = client.execute(req, token);
		JSONObject resJson = JSONObject.parseObject(rsp.getBody());
		if (rsp.getErrcode().equals(0L)) {
			jsonArray.addAll(resJson.getJSONArray("recordresult"));
			if (resJson.getBoolean("hasMore")) {
				requestAttendanceList(workDateFrom, workDateTo, userIdList, offset + limit, limit, jsonArray);
			}
		}
		else {
			throw new RuntimeException("请求异常" + resJson);
		}
	}

	/**
	 * 获取所有考勤组信息
	 * @param offset 抵消
	 * @param limit 限制
	 * @param jsonArray Json Array
	 * @throws ApiException API例外
	 */
	private void requestSimpleGroups(Long offset, Long limit, JSONArray jsonArray) throws ApiException {
		DingTalkClient client = new DefaultDingTalkClient(DingTalkStatus.GET_SIMPLE_GROUPS);
		OapiAttendanceGetsimplegroupsRequest req = new OapiAttendanceGetsimplegroupsRequest();
		req.setOffset(offset);
		req.setSize(limit);
		OapiAttendanceGetsimplegroupsResponse rsp = client.execute(req, getToken());
		JSONObject resJson = JSONObject.parseObject(rsp.getBody());
		if (rsp.getErrcode().equals(0L)) {
			JSONObject resultJson = resJson.getJSONObject("result");
			jsonArray.addAll(resultJson.getJSONArray("groups"));
			if (resultJson.getBoolean("has_more")) {
				requestSimpleGroups(offset + limit, limit, jsonArray);
			}
		}
		else {
			throw new RuntimeException("请求异常" + resJson);
		}
	}

	/**
	 * 批量查询人员排班信息
	 * @param userIds 用户ID字符串，用逗号分隔，
	 * @param startDate 开始时间
	 * @param endDate 结束时间,最大范围7天
	 * @return res
	 * @throws ApiException API例外
	 */
	public List<OapiAttendanceScheduleListbyusersResponse.TopScheduleVo> requestScheduleListbyusers(String userIds, DateTime startDate,
			DateTime endDate) throws ApiException {
		DingTalkClient client = new DefaultDingTalkClient(DingTalkStatus.GET_SCHEDULE_LISTBYUSERS);
		OapiAttendanceScheduleListbyusersRequest req = new OapiAttendanceScheduleListbyusersRequest();
		req.setOpUserId("16397410931122588");
		req.setUserids(userIds);
		req.setFromDateTime(startDate.getTime());
		req.setToDateTime(endDate.getTime());
		OapiAttendanceScheduleListbyusersResponse rsp = client.execute(req, getToken());
		JSONObject resJson = JSONObject.parseObject(rsp.getBody());
		if (rsp.getErrcode().equals(0L)) {
			return rsp.getResult();
		}
		else {
			throw new RuntimeException("请求异常" + resJson);
		}
	}

	/**
	 * 获取报表假期数据
	 * @param fromDate 从日期开始
	 * @param toDate 迄今为止
	 * @param leaveNames 留下名称
	 * @param userId 用户身份
	 * @return res
	 * @throws ApiException API例外
	 */
	public Map<String, List<OapiAttendanceGetleavetimebynamesResponse.ColumnDayAndVal>> getLeaveTimeByNames(Date fromDate, Date toDate,
			String[] leaveNames, String userId) throws ApiException {
		DingTalkClient client = new DefaultDingTalkClient(DingTalkStatus.GET_LEAVE_TIME_BY_NAMES);
		OapiAttendanceGetleavetimebynamesRequest req = new OapiAttendanceGetleavetimebynamesRequest();
		req.setUserid(userId);
		req.setLeaveNames(StrUtil.join(",", leaveNames));
		req.setFromDate(fromDate);
		req.setToDate(toDate);
		OapiAttendanceGetleavetimebynamesResponse rsp = client.execute(req, getToken());
		if (rsp.getErrcode().equals(0L)) {
			List<OapiAttendanceGetleavetimebynamesResponse.ColumnValForTopVo> columnValForTopVoList = rsp.getResult().getColumns();
			Map<String, List<OapiAttendanceGetleavetimebynamesResponse.ColumnDayAndVal>> map = new HashMap<>();
			for (OapiAttendanceGetleavetimebynamesResponse.ColumnValForTopVo columnValForTopVo : columnValForTopVoList) {
				map.put(columnValForTopVo.getColumnvo().getName(), columnValForTopVo.getColumnvals());
			}
			return map;
		}
		else {
			log.error("请求异常" + rsp.getBody());
			throw new RuntimeException("请求异常" + rsp.getBody());
		}
	}

	/**
	 * 出勤获取列瓦尔
	 * @param fromDate 从日期开始
	 * @param toDate 迄今为止
	 * @param columnIdList 列ID列表
	 * @param userId 用户身份
	 * @return {@link Map }<{@link Long }, {@link List
	 * }<{@link OapiAttendanceGetcolumnvalResponse.ColumnDayAndVal }>>
	 * @throws ApiException API例外
	 */
	public Map<Long, List<OapiAttendanceGetcolumnvalResponse.ColumnDayAndVal>> attendanceGetColumnVal(Date fromDate, Date toDate,
			Long[] columnIdList, String userId) throws ApiException {
		DingTalkClient client = new DefaultDingTalkClient(DingTalkStatus.GET_ATTENDANCE_COLUMN_VAL);
		OapiAttendanceGetcolumnvalRequest req = new OapiAttendanceGetcolumnvalRequest();
		req.setUserid(userId);
		req.setColumnIdList(StrUtil.join(",", columnIdList));
		req.setFromDate(fromDate);
		req.setToDate(toDate);
		OapiAttendanceGetcolumnvalResponse rsp = client.execute(req, getToken());
		if (rsp.getErrcode().equals(0L)) {
			List<OapiAttendanceGetcolumnvalResponse.ColumnValForTopVo> columnValForTopVoList = rsp.getResult().getColumnVals();
			Map<Long, List<OapiAttendanceGetcolumnvalResponse.ColumnDayAndVal>> map = new HashMap<>();
			for (OapiAttendanceGetcolumnvalResponse.ColumnValForTopVo columnValForTopVo : columnValForTopVoList) {
				map.put(columnValForTopVo.getColumnVo().getId(), columnValForTopVo.getColumnVals());
			}
			return map;
		}
		else {
			log.error("请求异常" + rsp.getBody());
			throw new RuntimeException("请求异常" + rsp.getBody());
		}
	}

	/**
	 * 获取机器人发送Markdown消息内容
	 * @param title 预警标题
	 * @param text 内容
	 * @param atUserIds 用户ID集合
	 * @param isAtAll 是否所有认
	 * @param robotAccessToken token
	 * @throws ApiException API例外
	 */
	public void getRobotSendMarkdownMsg(String title, StringBuffer text, List<String> atUserIds, boolean isAtAll, String robotAccessToken)
			throws ApiException {
		DingTalkClient client = new DefaultDingTalkClient("https://oapi.dingtalk.com/robot/send?access_token=" + robotAccessToken);
		OapiRobotSendRequest request = new OapiRobotSendRequest();
		request.setMsgtype("markdown");
		OapiRobotSendRequest.Markdown markdown = new OapiRobotSendRequest.Markdown();

		if (!CollectionUtils.isEmpty(atUserIds)) {
			text.append("\n\n@").append(StrUtil.join("@", atUserIds));
			OapiRobotSendRequest.At at = new OapiRobotSendRequest.At();
			// isAtAll类型如果不为Boolean，请升级至最新SDK
			at.setIsAtAll(isAtAll);
			at.setAtUserIds(atUserIds);
			request.setAt(at);
		}

		markdown.setTitle(title);
		markdown.setText(text.toString());
		request.setMarkdown(markdown);
		OapiRobotSendResponse response = client.execute(request);
		JSONObject resJson = JSONObject.parseObject(response.getBody());

		if (resJson.getIntValue("errcode") != 0) {
			// 发送不成功
			throw new RuntimeException("请求异常" + resJson);
		}
	}

	/**
	 * 全部得到树
	 * @param tree 树
	 * @param extand 巨大
	 */
	private static void getTreeAll(List<TreeDepartment> tree, List<TreeDepartment> extand) {
		tree.forEach(e -> {
			if (StringUtils.isNotEmpty(e.getUserid())) {
				return;
			}
			extand.add(e);
			if (e.getChildren().size() > 0) {
				getTreeAll(e.getChildren(), extand);
			}
		});
	}

	/**
	 * 使用 Token 初始化账号Client
	 * @return Client
	 * @throws Exception 例外
	 */
	public static com.aliyun.dingtalkcalendar_1_0.Client createClient() throws Exception {
		com.aliyun.teaopenapi.models.Config config = new com.aliyun.teaopenapi.models.Config();
		config.protocol = "https";
		config.regionId = "central";
		return new com.aliyun.dingtalkcalendar_1_0.Client(config);
	}

}
