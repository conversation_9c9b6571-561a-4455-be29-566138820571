package com.renpho.erp.smc.infrastructure.config;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

import lombok.Data;

@ConfigurationProperties(prefix = "online.config")
@Component
@Data
public class OnlineConfig {

	/**
	 * 是否开启-添加用户发送邮件-开关
	 */
	private Boolean enableAddUserSendEmail = Boolean.TRUE;

	/**
	 * 是否开启-任务发送邮件-开关
	 */
	private Boolean enableTaskSendEmail = Boolean.TRUE;

	/**
	 * 是否开启-三方账号同步-开关
	 */
	private Boolean enableThirdAccountSync = Boolean.TRUE;
}
