package com.renpho.erp.smc.infrastructure.utils.excel;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.converters.Converter;
import com.alibaba.excel.write.builder.ExcelWriterBuilder;
import com.alibaba.excel.write.handler.WriteHandler;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.alibaba.excel.write.metadata.WriteTable;
import lombok.extern.slf4j.Slf4j;

import java.io.File;
import java.util.*;

/**
 * Excel导出工具类.
 *
 * <AUTHOR>
 */
@Slf4j
public final class EasyExcelUtil {

	private EasyExcelUtil() {
	}

	/**
	 * 导出表格，一次性单个sheet导出
	 * @param titleMap 标题头，非空
	 * @param data 表格数据，可为null
	 * @param sheetName sheet名称，可为null
	 * @param fileName 文件名称，非空
	 * @return 临时文件名
	 */
	public static String exportExcelFile(Map<String, Object> titleMap, List data, String fileName, String sheetName) {
		return exportExcelFile(titleMap, data, fileName, sheetName,
				ListUtil.list(true, new CustomLongestMatchColumnWidthStyleStrategy(), new CustomLongestMatchColumnHeightStyleStrategy()));
	}

	public static String exportExcelFile(Map<String, Object> titleMap, List data, String fileName, String sheetName,
			List<WriteHandler> writeHandlers) {
		return exportExcelFile(titleMap, data, fileName, sheetName, writeHandlers, null);
	}

	/**
	 * 导出表格，一次性单个sheet导出
	 * @param titleMap 标题头，非空
	 * @param data 表格数据，可为null
	 * @param sheetName sheet名称，可为null
	 * @param fileName 文件名称，非空
	 * @param writeHandlers 拦截器集合，可为null
	 * @return 临时文件名
	 */
	public static String exportExcelFile(Map<String, Object> titleMap, List data, String fileName, String sheetName,
			List<WriteHandler> writeHandlers, List<Converter> converterList) {
		if (MapUtil.isEmpty(titleMap)) {
			// throw new ErrorCodeException(PlatformErrorCode.SYSTEM_EXCEPTION,
			// "excel.prompt1");
		}
		if (StrUtil.isEmpty(fileName)) {
			// throw new ErrorCodeException(PlatformErrorCode.SYSTEM_EXCEPTION,
			// "excel.prompt2");
		}
		if (StrUtil.isEmpty(sheetName)) {
			sheetName = fileName;
		}
		// fileName = UUID.randomUUID() + "_" + fileName + ".xlsx";
		fileName = fileName + ".xlsx";
		String downloadPath = RenphoConfig.getDownloadPath() + fileName;
		File excelFile = new File(downloadPath);
		if (!excelFile.getParentFile().exists()) {
			excelFile.getParentFile().mkdirs();
		}
		List<List<String>> headList = getHeadList(titleMap);
		ArrayList<List<Object>> rows = getRows(titleMap, data);
		ExcelWriter excelWriter = null;
		try {
			// 注册转换器
			ExcelWriterBuilder writerBuilder = EasyExcel.write(downloadPath);
			if (CollUtil.isNotEmpty(converterList)) {
				converterList.forEach(converter -> writerBuilder.registerConverter(converter));
			}
			excelWriter = writerBuilder.build();
			WriteSheet writeSheet = EasyExcel.writerSheet(sheetName).head(headList).needHead(Boolean.TRUE).build();
			WriteTable writeTable0 = EasyExcel.writerTable(0).needHead(Boolean.FALSE).build();
			writeSheet.setCustomWriteHandlerList(writeHandlers);
			excelWriter.write(rows, writeSheet, writeTable0);
		}
		finally {
			// 千万别忘记finish 会帮忙关闭流
			if (excelWriter != null) {
				excelWriter.finish();
			}
		}
		// FileUploadUtils.uploadExcelS3(fileName, excelFile);
		return fileName;
	}

	/**
	 * 获取标题头
	 *
	 * <AUTHOR>
	 * @date 2022/4/26 14:09
	 * @return 标题
	 */
	public static List<List<String>> getHeadList(Map<String, Object> titleMap) {
		List<List<String>> headList = ListUtil.list(true);
		for (String title : titleMap.keySet()) {
			List<String> head = new ArrayList<>();
			head.add(titleMap.get(title).toString());
			headList.add(head);
		}
		return headList;
	}

	/**
	 * 获取表格内容数据
	 *
	 * <AUTHOR>
	 * @date 2022/4/26 14:08
	 * @return 表格内容数据
	 */
	public static ArrayList<List<Object>> getRows(Map<String, Object> titleMap, List data) {
		// 需要保留字段
		Set<String> includeColumnFieldNames = new HashSet<>(titleMap.keySet());
		ArrayList<List<Object>> rows = new ArrayList<>();
		if (null != data) {
			for (Object obj : data) {
				List<Object> row = new LinkedList<>();
				Map<String, Object> map = new LinkedHashMap<>();
				BeanUtil.beanToMap(obj, map, false, field -> {
					if (includeColumnFieldNames.contains(field)) {
						return field;
					}
					return null;
				});
				for (String title : titleMap.keySet()) {
					row.add(map.get(title));
				}
				rows.add(row);
			}
		}
		return rows;
	}

}
