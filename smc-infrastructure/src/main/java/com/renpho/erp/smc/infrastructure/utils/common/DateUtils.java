package com.renpho.erp.smc.infrastructure.utils.common;

import cn.hutool.core.date.DatePattern;

import java.text.SimpleDateFormat;
import java.time.*;
import java.util.Date;

/**
 * 时间工具类.
 *
 * <AUTHOR>
 */
public final class DateUtils extends org.apache.commons.lang3.time.DateUtils {

	private DateUtils() {
	}

	/**
	 * 获取当前日期, 默认格式为yyyy-MM-dd
	 * @return String
	 */
	public static String getDate() {
		return dateTimeNow(DatePattern.NORM_DATE_PATTERN);
	}

	public static String getTime() {
		return dateTimeNow(DatePattern.NORM_DATETIME_PATTERN);
	}

	public static String dateTimeNow(final String format) {
		return parseDateToStr(format, new Date());
	}

	public static String parseDateToStr(final String format, final Date date) {
		return new SimpleDateFormat(format).format(date);
	}

	public static Date toDate(LocalDateTime temporalAccessor) {
		ZonedDateTime zdt = temporalAccessor.atZone(ZoneId.systemDefault());
		return Date.from(zdt.toInstant());
	}

	public static Date toDate(LocalDate temporalAccessor) {
		LocalDateTime localDateTime = LocalDateTime.of(temporalAccessor, LocalTime.of(0, 0, 0));
		ZonedDateTime zdt = localDateTime.atZone(ZoneId.systemDefault());
		return Date.from(zdt.toInstant());
	}

}
