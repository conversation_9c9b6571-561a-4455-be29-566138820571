package com.renpho.erp.smc.infrastructure.dingtalk;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;

@Data
public class TreeDepartment {

	private long id;

	private String name;

	private String userid;

	private String jobNumber;

	private String avatar;

	/** 父ID */
	private long parentId;

	@JsonInclude(JsonInclude.Include.NON_EMPTY)
	private List<TreeDepartment> children = new ArrayList<>();

}
