package com.renpho.erp.smc.infrastructure.config.mdcThreadPool;

import java.util.Map;
import java.util.concurrent.Callable;
import java.util.concurrent.Future;

import org.slf4j.MDC;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;

/**
 * @desc: 确保在异步线程中也能正确获取父线程的 MDC
 * @time: 2025-03-14 18:32:56
 * @author: Alina
 */

public class MdcThreadPoolTaskExecutor extends ThreadPoolTaskExecutor {
	public MdcThreadPoolTaskExecutor() {
		// 设置一个自定义的 TaskDecorator
		this.setTaskDecorator(new MdcTaskDecorator());
	}

	@Override
	public void execute(Runnable task) {
		super.execute(wrap(task));
	}

	@Override
	public <T> Future<T> submit(Callable<T> task) {
		return super.submit(wrap(task));
	}

	@Override
	public Future<?> submit(Runnable task) {
		return super.submit(wrap(task));
	}

	private Runnable wrap(Runnable task) {
		Map<String, String> contextMap = MDC.getCopyOfContextMap();
		return () -> {
			if (contextMap != null) {
				MDC.setContextMap(contextMap);
			}
			try {
				task.run();
			}
			finally {
				MDC.clear();
			}
		};
	}

	private <T> Callable<T> wrap(Callable<T> task) {
		Map<String, String> contextMap = MDC.getCopyOfContextMap();
		return () -> {
			if (contextMap != null) {
				MDC.setContextMap(contextMap);
			}
			try {
				return task.call();
			}
			finally {
				MDC.clear();
			}
		};
	}
}
