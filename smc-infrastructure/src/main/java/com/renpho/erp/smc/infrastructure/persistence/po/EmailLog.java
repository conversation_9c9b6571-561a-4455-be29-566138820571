package com.renpho.erp.smc.infrastructure.persistence.po;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.renpho.karma.cloud.mybatisplus.po.CreationPO;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDateTime;

@EqualsAndHashCode(callSuper = true)
@Data
@AllArgsConstructor
@NoArgsConstructor
@TableName(value = "sys_email_log")
public class EmailLog extends CreationPO<Integer, Integer, LocalDateTime> implements Serializable {

	/**
	 * 发送邮箱
	 */
	@TableField(value = "from_email")
	private String fromEmail;

	/**
	 * 发送者昵称
	 */
	@TableField(value = "from_nick_name")
	private String fromNickName;

	/**
	 * 接收者邮箱
	 */
	@TableField(value = "to_email")
	private String toEmail;

	/**
	 * 邮件标题
	 */
	@TableField(value = "title")
	private String title;

	/**
	 * 内容
	 */
	@TableField(value = "content")
	private String content;

	/**
	 * 发邮件类型 0 检查密码是否新设备 1用户重置密码 2管理员重置密码 3 新增用户 4 导入更新用户
	 */
	@TableField(value = "`type`")
	private Integer type;

	/**
	 * 邮件发送状态 0 异常 1 正常
	 */
	@TableField(value = "`status`")
	private Integer status;

	/**
	 * 邮件发送异常信息
	 */
	@TableField(value = "err_msg")
	private String errMsg;

	@Serial
	private static final long serialVersionUID = 1L;

}