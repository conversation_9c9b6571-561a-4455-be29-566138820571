package com.renpho.erp.smc.infrastructure.constants;

/**
 * 通用常量信息.
 *
 * <AUTHOR>
 */
public final class Constants {

	private Constants() {
	}

	/**
	 * UTF-8 字符集
	 */
	public static final String UTF8 = "UTF-8";

	/**
	 * GBK 字符集
	 */
	public static final String GBK = "GBK";

	/**
	 * RMI 远程方法调用
	 */
	public static final String LOOKUP_RMI = "rmi:";

	/**
	 * LDAP 远程方法调用
	 */
	public static final String LOOKUP_LDAP = "ldap:";

	/**
	 * LDAPS 远程方法调用
	 */
	public static final String LOOKUP_LDAPS = "ldaps:";

	/**
	 * http请求
	 */
	public static final String HTTP = "http://";

	/**
	 * https请求
	 */
	public static final String HTTPS = "https://";

	/**
	 * 成功标记
	 */
	public static final Integer SUCCESS = 200;

	/**
	 * 失败标记
	 */
	public static final Integer FAIL = 500;

	/**
	 * 登录成功状态
	 */
	public static final String LOGIN_SUCCESS_STATUS = "0";

	/**
	 * 登录失败状态
	 */
	public static final String LOGIN_FAIL_STATUS = "1";

	/**
	 * 登录成功
	 */
	public static final String LOGIN_SUCCESS = "Success";

	/**
	 * 注销
	 */
	public static final String LOGOUT = "Logout";

	/**
	 * 注册
	 */
	public static final String REGISTER = "Register";

	/**
	 * 登录失败
	 */
	public static final String LOGIN_FAIL = "Error";

	/**
	 * 当前记录起始索引
	 */
	public static final String PAGE_NUM = "pageNum";

	/**
	 * 每页显示记录数
	 */
	public static final String PAGE_SIZE = "pageSize";

	/**
	 * 排序列
	 */
	public static final String ORDER_BY_COLUMN = "orderByColumn";

	/**
	 * 排序的方向 "desc" 或者 "asc".
	 */
	public static final String IS_ASC = "isAsc";

	/**
	 * 验证码 redis key
	 */
	public static final String CAPTCHA_CODE_KEY = "captcha_codes:";

	/**
	 * 验证码有效期（分钟）
	 */
	public static final long CAPTCHA_EXPIRATION = 2;

	/**
	 * 参数管理 cache key
	 */
	public static final String SYS_CONFIG_KEY = "sys_config:";

	/**
	 * 字典管理 cache key
	 */
	public static final String SYS_DICT_KEY = "sys_dict:";

	/**
	 * 资源映射路径 前缀
	 */
	public static final String RESOURCE_PREFIX = "/profile";

	/**
	 * 定时任务白名单配置（仅允许访问的包名，如其他需要可以自行添加）
	 */
	public static final String[] JOB_WHITELIST_STR = { "com.renpho" };

	/**
	 * 定时任务违规的字符
	 */
	public static final String[] JOB_ERROR_STR = { "java.net.URL", "javax.naming.InitialContext", "org.yaml.snakeyaml",
			"org.springframework", "org.apache", "com.renpho.common.core.utils.file" };

	/**
	 * 逻辑删除状态-未删除
	 */
	public static final Integer LOGIC_EXIST = 0;

	/**
	 * 逻辑删除状态-已删除
	 */
	public static final Integer LOGIC_DELETE = 1;

	/**
	 * Excel上传目录
	 */
	public static final String SYSTEM_FILE_EXCEL = "system/file/excel/";

	/**
	 * pdf上传目录
	 */
	public static final String SYSTEM_FILE_PDF = "system/file/pdf/";

	/**
	 * 格式化
	 */
	public static final String SERIAL_FORMAT = "%0{0}d";

	/**
	 * 空字符
	 */
	public static final String EMPTY_CHAR = "[]";

	/**
	 * 请求消息唯一ID
	 */
	public static final String MESSAGE_ID = "x-request-message-id";

	/**
	 * 常量0
	 */
	public static final Integer ZERO = 0;

	/**
	 * 常量1
	 */
	public static final Integer ONE = 1;

	/**
	 * 常量2
	 */
	public static final Integer TWO = 2;

	/**
	 * 库存调整单导入最大行数
	 */
	public static final Integer IMPORT_SYSADJUSTWAREHOUSEINFO_MAX = 50;

	/**
	 * 库存调整单导入限制FNSku、poNo长度
	 */
	public static final Integer IMPORT_SYSADJUSTWAREHOUSEINFO_LIMIT = 32;

	/**
	 * 每页最大显示记录数
	 */
	public static final Integer PAGE_SIZE_NUM = 99999;

	/**
	 * 每页最大显示记录数
	 */
	public static final Integer IMPORT_MAX_SIZE = 500;

	/**
	 * 导出最大数量
	 */
	public static final Integer EXPORT_MAXSIZE_NUM = 60000;

	/**
	 * SN码初始值
	 */
	public static final String INITIAL_SN_CODE = "0000000";

	/**
	 * 异步导出最大数量
	 */
	public static final Integer EXPORT_MAXSIZE_NUM_ASYNCHRONOUS = 500000;

	/**
	 * 业务变更单返工流程抄送人员最大限制
	 */
	public static final Integer MAKECOPYFOR_MAX_NUM = 19;

}
