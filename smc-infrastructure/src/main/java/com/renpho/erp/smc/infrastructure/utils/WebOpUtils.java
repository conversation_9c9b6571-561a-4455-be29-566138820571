package com.renpho.erp.smc.infrastructure.utils;

import java.io.BufferedInputStream;
import java.io.IOException;
import java.io.OutputStream;
import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.util.List;

import com.alibaba.excel.EasyExcel;
import com.alibaba.fastjson.JSON;
import com.renpho.karma.dto.R;

import org.springframework.core.io.ClassPathResource;
import org.springframework.http.HttpHeaders;

import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;

@Slf4j
public class WebOpUtils {

	/**
	 * 设置响应结果
	 * @param response 响应结果对象
	 * @param rawFileName 文件名
	 * @throws UnsupportedEncodingException 不支持编码异常
	 */
	public static void setExcelResponseProp(HttpServletResponse response, String rawFileName) throws UnsupportedEncodingException {
		// 设置内容类型
		response.setContentType("application/vnd.vnd.ms-excel");
		// 设置编码格式
		response.setCharacterEncoding("utf-8");
		// 设置导出文件名称（避免乱码）
		String fileName = URLEncoder.encode(rawFileName.concat(".xlsx"), "UTF-8");
		// 设置响应头
		response.setHeader("Content-disposition", "attachment;filename*=utf-8''" + fileName);
	}

	public static <T> void writeErrListToExcel(Class<T> tClass, List<T> errList, HttpServletResponse response) {
		// 设置响应头
		response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
		response.setCharacterEncoding("utf-8");
		// 这里URLEncoder.encode可以防止中文乱码 当然和easyexcel没有关系
		String fileName = null;
		try {
			fileName = URLEncoder.encode("错误文件", "UTF-8").replaceAll("\\+", "%20");
		}
		catch (UnsupportedEncodingException e) {
			throw new RuntimeException(e);
		}
		response.setHeader("Content-disposition", "attachment;filename*=utf-8''" + fileName + ".xlsx");

		// 创建 Excel Writer
		OutputStream outputStream = null;
		try {
			outputStream = response.getOutputStream();
			EasyExcel.write(outputStream, tClass).sheet().doWrite(errList);
		}
		catch (Exception e) {
			log.error("Error writing Excel file", e);
		}
	}

	public static void toWeb(HttpServletResponse response, R<Object> result) {
		response.setContentType("application/json");
		response.setCharacterEncoding("utf-8");
		try {
			response.getWriter().println(JSON.toJSONString(result));
		}
		catch (IOException e) {
			throw new RuntimeException(e);
		}
	}

	/**
	 * 获取币种导入模板文件.
	 */
	public static void getTemplateFile(HttpServletResponse response, String fileName) {
		org.springframework.core.io.Resource resource = new ClassPathResource(fileName);
		// 设置HTTP响应头
		response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
		try {
			response.setHeader(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=" + URLEncoder.encode("月度汇率导入模板.xlsx", "utf-8"));
		}
		catch (UnsupportedEncodingException e) {
			throw new RuntimeException(e);
		}
		response.setCharacterEncoding("utf-8");
		// 使用try-with-resources语句来自动关闭资源
		try (BufferedInputStream in = new BufferedInputStream(resource.getInputStream()); OutputStream out = response.getOutputStream()) {
			byte[] buffer = new byte[4096];
			int bytesRead;
			// 读取文件内容并写入到HTTP响应中
			while ((bytesRead = in.read(buffer)) != -1) {
				out.write(buffer, 0, bytesRead);
			}
			// 刷新输出流以确保所有数据都被发送
			out.flush();
		}
		catch (IOException ex) {
			// 处理异常，例如记录日志或设置错误响应
			throw new RuntimeException("文件下载失败");
		}
	}

}
