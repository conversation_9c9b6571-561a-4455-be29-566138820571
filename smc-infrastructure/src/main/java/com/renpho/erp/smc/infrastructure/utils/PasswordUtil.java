package com.renpho.erp.smc.infrastructure.utils;

import org.springframework.security.crypto.factory.PasswordEncoderFactories;
import org.springframework.security.crypto.password.PasswordEncoder;

import com.github.houbb.heaven.util.secrect.Md5Util;

import cn.hutool.core.util.RandomUtil;

public class PasswordUtil {

	private String test;

	private static PasswordEncoder encoder = PasswordEncoderFactories.createDelegatingPasswordEncoder();

	public static String encode(String password) {
		return encoder.encode(password).replace("{bcrypt}", "");
	}

	public static boolean matches(String password, String encodePassed) {
		return encoder.matches(password, "{bcrypt}" + encodePassed);
	}

	/**
	 * 生成密码
	 * @return String
	 */
	public static String generatePwd() {
		String base = "@#$!^%&*.;+?[]|/-_(){}";
		String base2 = "ABCDEFGHJKMNPQRSTWXYZzxcvbnmasdfghjklqwertyuiop1234567890" + base;
		return RandomUtil.randomString(base2, 8) + RandomUtil.randomString(base, 2);
	}

	/**
	 * md5并且encode
	 * @param pwd 密码
	 * @return String
	 */
	public static String md5Encode(String pwd) {
		String md5Pwd = md5(pwd);
		return encode(md5Pwd);
	}

	/**
	 * md5
	 * @param pwd 密码
	 * @return String
	 */
	public static String md5(String pwd) {
		return Md5Util.md5(pwd.length() + pwd).toLowerCase();
	}
}
