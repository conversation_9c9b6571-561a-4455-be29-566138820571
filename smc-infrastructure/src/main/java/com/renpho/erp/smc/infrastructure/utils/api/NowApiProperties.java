package com.renpho.erp.smc.infrastructure.utils.api;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Component;

import lombok.Data;
import lombok.Getter;

@RefreshScope
@Getter
@Data
@Component
@ConfigurationProperties(prefix = "renpho")
public class NowApiProperties {

	private NowApi nowApi;

	@Data
	public static class NowApi {

		private String appKey;

		private String sign;

		private String url;

		private String timezoneApp;

		private String defaultCity;

	}

}
