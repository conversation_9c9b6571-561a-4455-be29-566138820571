package com.renpho.erp.smc.infrastructure.dingtalk;

public final class ConstantUrl {

	private ConstantUrl() {
	}

	/** 获取access_token */
	public static final String DINGTALK_GET_TOKEN = "https://oapi.dingtalk.com/gettoken";

	/** 根据code获取用户详情userId */
	public static final String DINGTALK_GET_USER_INFO = "https://oapi.dingtalk.com/user/getuserinfo";

	/** 根据userid获取用户详情 */
	public static final String DINGTALK_GET_USER_DETAIL = "https://oapi.dingtalk.com/topapi/v2/user/get";

	/** 根据unionid获取用户userid */
	public static final String DINGTALK_GET_USERID_BYUNIONID = "https://oapi.dingtalk.com/topapi/user/getbyunionid";

	/** 通过临时授权码获取用户信息 */
	public static final String DINGTALK_GET_USERINFO_BYCODE = "https://oapi.dingtalk.com/sns/getuserinfo_bycode";

	/** 通过临时授权码获取用户信息 */
	public static final String DINGTALK_GET_PARENT_BY_DEPT = "https://oapi.dingtalk.com/topapi/v2/department/listparentbydept";

}
