package com.renpho.erp.smc.infrastructure.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

import java.io.Serializable;

@Data
public class OumUserImportAddDto implements Serializable {

	@ExcelProperty(index = 0)
	private String name;

	@ExcelProperty(index = 1)
	private String genderName;

	@ExcelProperty(index = 2)
	private String email;

	@ExcelProperty(index = 3)
	private String phoneNo;

	@ExcelProperty(index = 4)
	private String departmentCode;

	@ExcelProperty(index = 5)
	private String reportUserCode;

	@ExcelProperty(index = 6)
	private String employmentType;

	@ExcelProperty(index = 7)
	private String corporationName;

	@ExcelProperty(index = 8)
	private String positionCode;

	// 合同地址
	@ExcelProperty(index = 9)
	private String contractLocation;

	// 社保电脑号
	@ExcelProperty(index = 10)
	private String socialInsuranceNo;

	@ExcelProperty(index = 11)
	private String housingFundNo;

	@ExcelProperty(index = 12)
	private String hireTime;

	@ExcelProperty(index = 13)
	private String resignationTime;

	@ExcelProperty(index = 14)
	private String roleNames;

	@ExcelProperty(index = 15)
	private String financialInfo;

	@ExcelProperty(index = 16)
	private String bizReportInfo;

	@ExcelProperty(index = 17)
	private String dingDingUserId;

	@ExcelProperty(index = 18)
	private String weWorkUserId;

	@ExcelProperty(index = 19)
	private String reason;

}
