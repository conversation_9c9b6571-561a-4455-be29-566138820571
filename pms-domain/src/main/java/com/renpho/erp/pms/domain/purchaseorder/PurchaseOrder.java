package com.renpho.erp.pms.domain.purchaseorder;

import cn.hutool.core.util.NumberUtil;
import com.renpho.erp.generator.util.GeneratorCodeUtil;
import com.renpho.erp.pms.domain.commom.CreatedContainer;
import com.renpho.erp.pms.domain.commom.Operator;
import com.renpho.erp.pms.domain.commom.ReceiptGenerationTypeEnum;
import com.renpho.erp.pms.domain.product.Product;
import com.renpho.erp.pms.domain.purchaserequest.PurchaseRequest;
import com.renpho.erp.pms.domain.purchaserequest.UpdatedContainer;
import com.renpho.erp.pms.model.purchaseorder.enums.PurchaseOrderReviewStatusEnum;
import com.renpho.erp.pms.model.purchaseorder.enums.PurchaseOrderStatusEnum;
import lombok.Data;
import org.jmolecules.ddd.types.AggregateRoot;

import java.io.Serializable;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2025/3/17
 */
@Data
public class PurchaseOrder implements AggregateRoot<PurchaseOrder, PurchaseOrderId>, CreatedContainer, UpdatedContainer, Serializable {

    private final PurchaseOrderId id;

    /**
     * 产品
     */
    private Product product;

    /**
     * 请购类型
     */
    private List<String> poTypes;

    /**
     * 采购单号
     */
    private String poNo;

    /**
     * 供应商ID
     */
    private Integer supplierId;

    /**
     * 甲方公司ID
     */
    private Integer companyId;

    /**
     * 供应商编码
     */
    private String supplierCode;

    /**
     * 简称
     */
    private String supplierShortName;

    /**
     * 品牌Id
     */
    private Integer brandId;

    /**
     * 型号id
     */
    private Integer modelId;

    /**
     * 类目ID
     */
    private Integer categoryId;

    /**
     * 采购数量
     */
    private Integer poQty;

    /**
     * 正品数量
     */
    private Integer normalQty;

    /**
     * 备品数量
     */
    private Integer spareQty;

    /**
     * 备品数量(旧值)
     */
    private Integer spareQtyOld;

    /**
     * 备品率(‰)
     */
    private Integer spareRate;


    /**
     * 采购单状态 @link {@link PurchaseOrderStatusEnum}
     */
    private PurchaseOrderStatusEnum status;

    /**
     * SN开始码
     */
    private String snStart;

    /**
     * SN结束码
     */
    private String snEnd;

    /**
     * 计划人员
     */
    private Operator planStaff;

    /**
     * 采购人员
     */
    private Operator buyer;

    /**
     * 审核状态:0=初始状态,1=待审核;2=审核通过;3=审核不通过
     */
    private PurchaseOrderReviewStatusEnum reviewStatus;
    /**
     * 流程实例id
     */
    private String processInstanceId;
    /**
     * 是否下载 0=未下载, 1=已经下载
     */
    private Integer downloadFlag;
    /**
     * 是否下载 0=未上传, 1=已经上传
     */
    private Integer uploadFlag;
    /**
     * 上传回签合同时间
     */
    private LocalDate uploadDate;
    /**
     * 期望交期
     */
    private LocalDate expectedDeliveryDate;
    /**
     * 确认交期
     */
    private LocalDate confirmedDeliveryDate;
    /**
     * 变更交期
     */
    private LocalDate changedDeliveryDate;
    /**
     * 复核交期
     */
    private LocalDate checkedDeliveryDate;

    /**
     * 变更交期红点 0=红点,1=无红点
     */
    private Integer changedDeliveryDateStatus;
    /**
     * 复核交期红点 0=红点, 1=无红点
     */
    private Integer checkedDeliveryDateStatus;
    /**
     * 提交时间
     */
    private LocalDateTime submitTime;
    /**
     * 审核完成时间
     */
    private LocalDateTime reviewTime;
    /**
     * 计划备注
     */
    private String planRemark;
    /**
     * 采购备注
     */
    private String purchaseRemark;
    /**
     * 计划变更备注
     */
    private String modifyRemark;
    /**
     * 复核备注
     */
    private String reviewRemark;

    private Operator created;

    private Operator updated;

    /**
     * 采购总额
     */
    private BigDecimal purchaseAmount;

    /**
     * 合同模板ID
     */
    private Integer contractTemplateId;

    /**
     * 合同模板名称
     */
    private String contractTemplateName;

    /**
     * 审核人员
     */
    private Integer reviewId;


    /**
     * 审核人员名称
     */
    private String reviewName;

    /**
     * 审核人员编号
     */
    private String reviewCode;

    /**
     * 图片地址
     */
    private String picture;

    /**
     * 图片地址
     */
    private String psku;

    /**
     * 中文名称
     */
    private String productName;

    /**
     * 品牌
     */
    private String brandName;

    /**
     * 型号
     */
    private String modelName;

    /**
     * 待计划数量
     */
    private Integer pendQty;

    /**
     * 已计划数量
     */
    private Integer pdQty;


    /**
     * 未申请质检数量
     */
    private Integer unApplyCount;

    /**
     * 已申请质检数量
     */
    private Integer applyCount;

    /**
     * 已合格数量
     */
    private Integer qualifiedCount;

    /**
     * 不合格数量
     */
    private Integer unQualifiedCount;

    /**
     * 待定数量
     */
    private Integer pendingCount;

    /**
     * 已收货数量
     */
    private Integer receivedCount;

    /**
     * 未收货数量
     */
    private Integer unReceivedCount;

    /**
     * 存在PCO变更
     */
    private Boolean existPcoChange = false;

    /**
     * 存在PCD变更
     */
    private Boolean existPcdChange = false;

    /**
     * 由PCD 产生
     */
    private Boolean createByPcd = false;

    /**
     * 品类名称
     */
    private String categoryName;

    /**
     * PO id
     */
    private Integer poId;

    private List<PurchaseOrderItem> items;

    private List<PurchaseOrderItemForDetail> detailItems;

    private PurchaseOrderPriceInfo priceInfo;

    private PurchaseOrderSupplierInfo supplierInfo;

    private PurchaseOrderProductInfo productInfo;

    private List<PurchaseOrderFile> fileList;

    /**
     * PR单信息
     */
    private PurchaseRequest request;

    /**
     * PO关联的PR信息
     */
    private List<PurchaseRequest> requestList;

    /**
     * PO值初始化
     */
    public void init(Operator operator) {
        this.status = PurchaseOrderStatusEnum.PENDING;
        this.reviewStatus = PurchaseOrderReviewStatusEnum.INIT;
        this.processInstanceId = "";
        this.downloadFlag = 0;
        this.uploadFlag = 0;
        this.planRemark = "";
        this.purchaseRemark = "";
        this.modifyRemark = "";
        this.reviewRemark = "";
        this.created = operator;
        this.updated = operator;
        calPoQty();
        createPoNo();
    }

    public void createPoNo() {
        this.poNo = GeneratorCodeUtil.generateIncr(ReceiptGenerationTypeEnum.PO.name());
    }


    /**
     * 计算PO单实际SN码生成数量= 采购数量 * sn系数 / 100
     *
     * @param snCoefficient sn系数
     * @return 实际SN码生成数量
     */
    public Integer calculateActualQty(Integer snCoefficient) {
        return NumberUtil.mul(this.poQty, snCoefficient)
                .divide(BigDecimal.valueOf(100), RoundingMode.HALF_UP).intValue();
    }

    /**
     * 设置备品数量
     */
    public void calPoQty() {
        this.poQty = items.stream().mapToInt(PurchaseOrderItem::getApplicationQty).sum();
        this.spareQty = NumberUtil.mul(this.poQty, spareRate, 0.001)
                .setScale(0, RoundingMode.HALF_UP).intValue();
        this.normalQty = this.poQty - this.spareQty;
    }

    /**
     * 指定备品数目
     */
    public void resetSpareQty(Integer spareQty){
        this.spareQty = spareQty;
        this.normalQty = this.poQty - this.spareQty;
        // 备品率 = 备品数 / 采购数 * 1000
        BigDecimal spareRate = new BigDecimal(spareQty.toString())
                .divide(new BigDecimal(poQty.toString()), 4, RoundingMode.HALF_UP)
                .multiply(new BigDecimal("1000"));
        this.spareRate = spareRate.intValue();
    }
}
