package com.renpho.erp.pms.domain.purchaserequest;

import com.renpho.erp.pms.domain.processinstance.ProcessInstanceId;
import com.renpho.erp.pms.domain.purchaseorder.PurchaseOrderId;
import com.renpho.karma.dto.PageQuery;
import com.renpho.karma.dto.Paging;
import org.jmolecules.ddd.integration.AssociationResolver;

import java.util.Collection;
import java.util.List;
import java.util.Optional;

/**
 * 请购单 AssociationResolver
 *
 * <AUTHOR>
 * @since 2025/3/17
 */
public interface PurchaseRequestLookup extends AssociationResolver<PurchaseRequest, PurchaseRequestId> {

    List<PurchaseRequest> findByIds(Collection<PurchaseRequestId> purchaseRequestIds);

    Paging<PurchaseRequest> findPage(PurchaseRequestQuery condition, PageQuery pageQuery);

    List<PurchaseRequest> findAll(PurchaseRequestQuery condition);

    List<PurchaseRequest> findSalesChannelAssociations(Collection<PurchaseRequest> purchaseRequest);

    List<PurchaseRequest> findStoreAssociations(Collection<PurchaseRequest> purchaseRequest);

    Optional<PurchaseRequest> findItemAssociation(PurchaseRequest domain);

    List<PurchaseRequest> findItemAssociations(Collection<PurchaseRequest> purchaseRequest);

    List<PurchaseRequest> findOperatorAssociations(Collection<PurchaseRequest> purchaseRequest);

    List<PurchaseRequest> findProductAssociations(Collection<PurchaseRequest> domains);

    List<PurchaseRequestStatusCountView> countByStatus(Collection<PurchaseRequestStatus> status);

    Optional<PurchaseRequest> findByPrNo(String prNo);

    List<PurchaseRequest> findByPrNos(Collection<String> prNos);

    Optional<PurchaseRequest> findByInstanceId(ProcessInstanceId instanceId);

    List<PurchaseRequest> findProcessInstanceAssociations(Collection<PurchaseRequest> domains);

    List<PurchaseRequest> findPurchaseOrderAssociations(Collection<PurchaseRequest> domains);

    /**
     * 根据POid查询PR
     * @param poIds POid
     * @return PR数据
     */
    List<PurchaseRequest> findByPoIds(Collection<PurchaseOrderId> poIds);
}
