server:
  port: 9096

spring:
  cloud:
    nacos:
      username: renpho-nacos-local
      password: renphon<PERSON><PERSON>local
      config:
        server-addr: renpho.master.com:30848
        group: DEFAULT_GROUP
        namespace: 5c21c56e-3331-44bd-975a-7961c8cf9c30
      discovery:
        server-addr: *************:8848
        namespace: ${spring.cloud.nacos.config.namespace}
        port: ${server.port}
        ip: ************

management:
  endpoints:
    web:
      exposure:
        include: '*'
