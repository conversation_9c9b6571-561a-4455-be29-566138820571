flowchart TB
        a1(["调拨单数组"]) --> a2("点击下发出库")
        a2 --> a3{"校验调拨单状态"}
        a3 -->|" 待下发出库 "| a8{"物流方式、运输类型是否合法"}
        a8 -->|" 是 "| a4{"是否为API出库"}
        a8 -->|" 否 "| a10{"出库单号是否为空"}
        a4 --> a5{"API类型"}
        a5 -->|" 极智佳 "| a9["调用极智佳出库接口"]
        a5 -->|" WMS "| a12["调用WMS出库接口"]
        a5 -->|" KingSpark "| a13["调用KingSpark出库接口"]
        a5 -->|" JD "| a14["调用JD出库接口"]
        a9 --> a6["调用结果"]
        a12 --> a6
        a13 --> a6
        a14 --> a6
        a6 --> a18["记录出库任务"]
        a18 --> a21{"调用是否成功"}
        a21 -->|" 是 "| a19["记录出库单号"]
        a19 --> a7["更新调拨单状态为待发货"]
        a7 --> a20["记录日志"]
        a21 -->|" 否 "| a17["回写错误信息"]
        a17 --> a22["提示调用失败"]
        a3 -->|" 非待下发出库 "| a15["提示调拨单状态不合法"]
        a10 -->|" 是 "| a16["提示出库单号不能为空"]
        a8 -->|" 同时包含API出库和非API出库 "| a11["提示只能选择一种出库"]
        a15 --> a999
        a16 --> a999
        a17 --> a999
        a11 --> a999
        a22 --> a999
        a20 -.-> b1{"是否换标"}
        a20 --> a999(["结束"])
        b1 -->|" 是 "| b2["调用换标接口"]
        b2 -.-> b4["调用IMS创建转让单"]
        b4 --> b5["记录任务"]
        b1 -->|" 否 "| b3(["结束(自动标记换标完成？等待页面手动点击换标完成？)"])
        b5 --> b3
