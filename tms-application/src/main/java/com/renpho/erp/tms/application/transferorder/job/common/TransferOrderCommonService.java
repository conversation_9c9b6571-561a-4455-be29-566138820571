package com.renpho.erp.tms.application.transferorder.job.common;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.renpho.erp.ftm.client.response.FileInfoResponse;
import com.renpho.erp.tms.application.inbound.InboundRecordQueryService;
import com.renpho.erp.tms.application.transferorder.TransferOrderQueryService;
import com.renpho.erp.tms.application.transferorder.TransferOrderService;
import com.renpho.erp.tms.application.transportrequest.PushTaskQueryService;
import com.renpho.erp.tms.application.transportrequest.PushTaskService;
import com.renpho.erp.tms.application.transportrequest.job.common.TransportRequestCommonService;
import com.renpho.erp.tms.domain.cartonlabel.CartonLabelTsData;
import com.renpho.erp.tms.domain.exception.BusinessException;
import com.renpho.erp.tms.domain.inbound.*;
import com.renpho.erp.tms.domain.transferorder.*;

import com.renpho.erp.tms.domain.transportrequest.*;
import com.renpho.erp.tms.domain.transportrequest.dto.WarehouseData;
import com.renpho.erp.tms.infrastructure.util.cartonlabel.CartonLabelTsGenerator;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.*;

/**
 * 调拨-API对接公共服务.
 *
 * <AUTHOR>
 * @since 2025/7/22
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class TransferOrderCommonService {

    private final TransportRequestCommonService transportRequestCommonService;
    private final TransferOrderQueryService transferOrderQueryService;
    private final TransferOrderService transferOrderService;
    private final PushTaskService pushTaskService;
    private final PushTaskQueryService pushTaskQueryService;
    private final InboundRecordQueryService inboundRecordQueryService;

    private final TransferOrderRepository transferOrderRepository;
    private final InboundRecordRepository inboundRecordRepository;

    /**
     * 根据tr单状态查询待补充状态的tr单-运单号
     *
     * @param status        TS单状态,字典: transfer_order_status,可选值: 0-待补充信息 1-待拼柜 2-已拼柜 3-已作废
     * @param warehouseType 推送类型, WMS,京东,极智佳,乐鱼,KingSpark,FBA,AWD,FBT,WFS,手动签收）
     * @param shipmentNeed shipment 过滤条件，是否需要
     * @return 待补充状态的ts单-运单号
     */
    public List<TransferOrderData> selectTsListByStatus(Integer status, WarehouseProviderType warehouseType, boolean shipmentNeed){
        // 状态是待补充信息
        List<TransferOrderData> tsDataListAll = transferOrderQueryService.selectTsListByStatus(status, warehouseType);
        List<TransferOrderData> needList = new ArrayList<>();
        if(tsDataListAll != null && !tsDataListAll.isEmpty()){
            for (TransferOrderData tsData : tsDataListAll) {
                if(shipmentNeed){
                    if(StringUtils.isNotBlank(tsData.getShipmentId())){
                        needList.add(tsData);
                    }
                }else{
                    if(StringUtils.isBlank(tsData.getShipmentId())){
                        needList.add(tsData);
                    }
                }
            }
        }
        return needList;
    }

    /**
     * 根据ts单状态跟仓库类型，查询item明细带有出库单编码的数据
     * @param status TS单状态
     * @param warehouseType 仓库类型
     * @return TS主表对应的item明细带有出库单编码的数据
     */
    public List<TransferOrderData> selectTsListWithItemOutboundNo(TransferOrderStatus status, WarehouseProviderType warehouseType) {
        return transferOrderQueryService.selectTsListWithItemOutboundNo(status, warehouseType);
    }

    /**
     * 根据ts单状态查询待补充状态的ts单-箱唛文件
     * @param status        TS单状态,字典: transfer_order_status,可选值: 0-待补充信息 1-待拼柜 2-已拼柜 3-已作废
     * @param warehouseType 推送类型, WMS,京东,极智佳,乐鱼,KingSpark,FBA,AWD,FBT,WFS,手动签收）
     * @return 待补充状态的ts单-箱唛文件
     */
    public List<TransferOrderData> selectTsPalletListByStatus(Integer status, WarehouseProviderType warehouseType){
        // 状态是待补充信息
        List<TransferOrderData> trDataListAll = transferOrderQueryService.selectTsListByStatus(status, warehouseType);
        List<TransferOrderData> needList = new ArrayList<>();
        if(trDataListAll != null && !trDataListAll.isEmpty()){
            for (TransferOrderData tsData : trDataListAll) {
                // shipmentId 为空的数据
                if(StringUtils.isNotBlank(tsData.getShipmentId())){
                    // 箱唛文件为空就表示要执行创建箱唛文件任务
                    if(!isNonEmptyArray(tsData.getCartonLabelFileIds())){
                        needList.add(tsData);
                    }
                }
            }
        }
        return needList;
    }

    /**
     * 判断json数组是否非空
     * @param jsonArrayStr json数组字符串
     * @return true 非空 false 空
     */
    public boolean isNonEmptyArray(String jsonArrayStr) {
        if (jsonArrayStr == null || jsonArrayStr.isBlank()) {
            return false;
        }
        JSONArray jsonArray = JSON.parseArray(jsonArrayStr);
        return jsonArray != null && !jsonArray.isEmpty();
    }

    /**
     * 填充外部参数
     * @param status TR单状态
     * @param tsDataList TR单列表
     * @param tsNoList TR单号列表
     * @return 填充外部参数后的TR单列表
     */
    public List<TransferOrderData> fillExterWithTsStatus(TransferOrderStatus status, List<TransferOrderData> tsDataList, List<String> tsNoList) {
        if(tsDataList==null){
            tsDataList = new ArrayList<>();
        }
        if (tsNoList != null && !tsNoList.isEmpty()) {
            List<String> needTsNoList = new ArrayList<>();
            Map<String, TransferOrderData> tsDataMap = new HashMap<>();
            for (TransferOrderData tsData : tsDataList) {
                tsDataMap.put(tsData.getTsNo(), tsData);
            }
            for (String tsNo : tsNoList) {
                if (!tsDataMap.containsKey(tsNo)) {
                    needTsNoList.add(tsNo);
                }
            }
            if (!needTsNoList.isEmpty()) {
                List<TransferOrderData> needTsDataList = transferOrderQueryService.selectTsListByTsNoList(needTsNoList);
                if (needTsDataList != null && !needTsDataList.isEmpty()) {
                    for (TransferOrderData tsData : needTsDataList) {
                        if(tsData.getStatus().equals(status.getValue())){
                            tsDataList.add(tsData);
                        }
                    }
                }
            }
        }
        return tsDataList;
    }

    /**
     * 填充外部参数
     * @param shipStatusList 运单状态列表
     * @param tsDataList TR单列表
     * @param tsNoList TS单号列表
     * @return 填充外部参数后的TR单列表
     */
    public List<TransferOrderData> fillExterWithTsStatus(List<TransferOrderStatus> shipStatusList, List<TransferOrderData> tsDataList, List<String> tsNoList) {
        if(tsDataList==null){
            tsDataList = new ArrayList<>();
        }
        if (tsNoList != null && !tsNoList.isEmpty()) {
            List<String> needTsNoList = new ArrayList<>();
            Map<String, TransferOrderData> tsDataMap = new HashMap<>();
            for (TransferOrderData trData : tsDataList) {
                tsDataMap.put(trData.getTsNo(), trData);
            }
            for (String trNo : tsNoList) {
                if (!tsDataMap.containsKey(trNo)) {
                    needTsNoList.add(trNo);
                }
            }
            if (!needTsNoList.isEmpty()) {
                List<TransferOrderData> needTrDataList = transferOrderQueryService.selectTsListByTsNoList(needTsNoList);
                if (needTrDataList != null && !needTrDataList.isEmpty()) {
                    for (TransferOrderData tsData : needTrDataList) {
                        if(shipStatusList.contains(TransferOrderStatus.fromValue(tsData.getStatus()))){
                            tsDataList.add(tsData);
                        }
                    }
                }
            }
        }
        return tsDataList;
    }

    /**
     * 获取WMS授权token.
     * @param warehouseId 仓库ID
     *
     * @return WMS授权token
     */
    public String getWmsAuthToken(Integer warehouseId) {
        return transportRequestCommonService.getWmsAuthToken(warehouseId);
    }

    /**
     * 获取TS单的目的仓的客户编码
     * @param warehouseId 仓库ID
     * @return 客户编码
     */
    public String getConsumerCode(Integer warehouseId) {
        return transportRequestCommonService.getConsumerCode(warehouseId);
    }

    /**
     * 获取TS单的目的仓的仓库编码
     * @param warehouseId 仓库ID
     * @return 仓库编码
     */
    public String getWarehouseCode(Integer warehouseId) {
        return transportRequestCommonService.getWarehouseCode(warehouseId);
    }

    /**
     * 获取京东仓库信息.
     * @param id 仓库ID
     *
     * @return 京东仓库信息
     */
    public WarehouseData getJDWarehouse(Integer id) {
        return transportRequestCommonService.getJDWarehouse(id);
    }

    /**
     * 箱唛任务.
     *
     * @param warehouseType 仓库类型
     */
    public void doingPallet(WarehouseProviderType warehouseType, List<String> tsNoList) {
        String warehouseTypeName = warehouseType.toString();
        try {
            log.info("TS-目的仓为{}的箱唛任务开始", warehouseTypeName);

            // 1. load出箱唛推送任务
            TransferOrderStatus status = TransferOrderStatus.WAIT_CREATE_INBOUND;
            List<TransferOrderData> tsDataList = this.selectTsPalletListByStatus(status.getValue(), warehouseType);

            // 外部参数
            tsDataList = this.fillExterWithTsStatus(status, tsDataList, tsNoList);

            // 3.调用箱唛创建接口，包括箱唛主数据跟箱唛文件数据
            if (!tsDataList.isEmpty()) {
                Map<Integer, TransferOrder> tsMap = transferOrderService.getTsMapByIds(tsDataList.stream().map(TransferOrderData::getId).toList());
                for (TransferOrderData tsData : tsDataList) {
                    TransferOrder ts = tsMap.get(tsData.getId());
                    if (ts == null) {
                        log.error("TS-目的仓为{}的箱唛任务异常, ts源数据找不到, tsId={}", warehouseTypeName, tsData.getId());
                        continue;
                    }
                    // 目的仓限制
                    if (warehouseType != ts.findWarehouseProviderType()) {
                        continue;
                    }
                    // 不是待创建入库单状态就跳过
                    if (TransferOrderStatus.WAIT_CREATE_INBOUND != ts.getStatus()) {
                        continue;
                    }

                    // 生成推送任务
                    PushTask trPushTask = pushTaskQueryService.findByBizAndType(ts, List.of(PushTaskType.CARTON_FILE_GENERATION)).stream().findFirst().orElseThrow(() -> new BusinessException("数据有异常, tsId={}", tsData.getId()));

                    // 保存箱唛信息
                    pushTaskService.execute(() -> this.savePalletInfo(ts, trPushTask), trPushTask, "TR-目的仓为" + warehouseTypeName + "的箱唛任务异常");
                }
            }
        } catch (Exception e) {
            log.error("TS-目的仓为{}的箱唛任务异常", warehouseTypeName, e);
        }
    }

    /**
     * 保存箱唛信息
     *
     * @param ts         ts信息
     * @param tsPushTask 推送任务
     * @throws Exception
     */
    private Boolean savePalletInfo(TransferOrder ts, PushTask tsPushTask) throws Exception {
        // 调用手动创建箱唛接口
        CartonLabelTsData cartonLabelTsData = CartonLabelTsData.extractCartonLabelData(ts);
        List<FileInfoResponse> responseList = CartonLabelTsGenerator.generateTsCartonLabelFilesToS3(cartonLabelTsData);
        ts.setCartonLabelFileIds(responseList.stream().map(FileInfoResponse::getId).toList());

        // 3. 成功的话保存相关信息，并更新状态为'待下发出库'
        ts.setStatus(TransferOrderStatus.WAIT_CREATE_SHIPMENT);
        ts.setSyncApiStatus(SyncApiStatus.SUCCESS);
        transferOrderRepository.updateById(ts);

        // 4. 更新任务状态, 触发下游推送任务
        pushTaskService.cartonFileGeneration(ts, true, tsPushTask.getRetryCount(), PushTaskStatus.SUCCESS.name());
        return true;
    }

    /**
     * 只需要记录，不需要分摊
     */
    public void doRecord(WarehouseProviderType warehouseType, Integer receivedQuantity, Integer receivedGoodQuantity, Integer receivedBadQuantity,
                         Integer putawayQuantity, Integer putawayGoodQuantity, Integer putawayBadQuantity, LocalDateTime signTime, LocalDateTime putWayTime, TransferOrderItem tsItem, InboundRequestHistory hisRecord, InboundStatus inboundStatus, InboundStatus putAwayStatus){
        // 记录record
        InboundRecord lastRecord = inboundRecordQueryService.findLastByBiz(tsItem).orElse(null);
        InboundRecord currentRecord = new InboundRecord(null);
        currentRecord.setBizId(tsItem.getId().id());
        currentRecord.setBizNo(tsItem.getTsNo());
        currentRecord.setBizType(InboundBusinessType.TS);
        currentRecord.setPsku(tsItem.getPsku());
        currentRecord.setFnSku(tsItem.getFnSku());
        currentRecord.setRequestHistoryId(hisRecord.getId());
        currentRecord.buildInfo(receivedQuantity, receivedGoodQuantity, receivedBadQuantity, putawayQuantity, putawayGoodQuantity, putawayBadQuantity, lastRecord);
        currentRecord.setReceivedTime(signTime);
        currentRecord.setPutawayTime(putWayTime);
        currentRecord.setStatus(inboundStatus);
        currentRecord.setPutawayStatus(putAwayStatus);
        currentRecord.setWarehouseType(warehouseType);
        currentRecord = inboundRecordRepository.add(currentRecord);
    }

    /**
     * 签收记录+分摊计算触发
     * @param receiveQty 签收数量
     * @param putAwayQty 上架数量
     * @param signTime 签收时间
     * @param putWayTime 上架时间
     * @param tsItem 调拨单明细
     * @param hisRecord 历史记录
     * @param inboundStatus 签收状态
     */
    public void doRecord(WarehouseProviderType warehouseType,Integer receiveQty, Integer putAwayQty, LocalDateTime signTime, LocalDateTime putWayTime, TransferOrderItem tsItem, InboundRequestHistory hisRecord, InboundStatus inboundStatus){
        // 记录record
        InboundRecord lastRecord = inboundRecordQueryService.findLastByBiz(tsItem).orElse(null);
        InboundRecord currentRecord = new InboundRecord(null);
        currentRecord.setBizId(tsItem.getId().id());
        currentRecord.setBizNo(tsItem.getTsNo());
        currentRecord.setBizType(InboundBusinessType.TS);
        currentRecord.setPsku(tsItem.getPsku());
        currentRecord.setFnSku(tsItem.getFnSku());
        currentRecord.setRequestHistoryId(hisRecord.getId());
        currentRecord.buildPutawayInfo(receiveQty, putAwayQty, lastRecord);
        currentRecord.setReceivedTime(signTime);
        currentRecord.setPutawayTime(putWayTime);
        // 状态值一致，所以直接设置
        currentRecord.setStatus(inboundStatus);
        currentRecord.setPutawayStatus(inboundStatus);
        currentRecord.setWarehouseType(warehouseType);
        currentRecord = inboundRecordRepository.add(currentRecord);
    }

}
