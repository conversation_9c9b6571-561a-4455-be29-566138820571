package com.renpho.erp.tms.application.transferorder.job.wms;

import com.baomidou.lock.annotation.Lock4j;
import com.renpho.erp.tms.application.transferorder.TransferOrderService;
import com.renpho.erp.tms.domain.inbound.WarehouseProviderType;
import com.renpho.erp.tms.domain.transferorder.*;

import com.renpho.erp.tms.domain.transportrequest.SyncApiStatus;
import com.renpho.erp.tms.infrastructure.persistence.transferorder.repository.TransferOrderItemRepository;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;

/**
 * TS-目的仓为WMS-出库单同步处理.
 *
 * <AUTHOR>
 * @since 2025/8/29
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class TransferOrderOutWmsTrackService {

    private final TransferOrderService transferOrderService;

    private final TransferOrderRepository transferOrderRepository;
    private final TransferOrderItemRepository transferOrderItemRepository;

    // 仓库类型
    private final WarehouseProviderType warehouseType = WarehouseProviderType.WMS;

    /**
     * TS-目的仓为WMS-出库单同步处理.
     *
     * @param tsNo        TS单号
     * @param outboundNo  出库单编号
     * @param outboundTime 出库时间
     * @return 处理结果
     */
    @Lock4j(name = "transfer:request:wms:outbound:track:basic")
    @Transactional(rollbackFor = Exception.class)
    public Boolean trackOutboundStatus(String tsNo, String outboundNo, LocalDateTime outboundTime) {
        log.info("TS-目的仓为WMS-出库单同步处理, tsNo={}, outboundNo={}", tsNo, outboundNo);
        TransferOrder ts = transferOrderService.findByTsNo(tsNo);
        // 目的仓限制
        if (warehouseType != ts.findWarehouseProviderType()) {
            log.error("TS-目的仓为WMS-出库单同步处理, TS对应的仓库类型不是WMS, tsNo={}", tsNo);
            return false;
        }

        if(TransferOrderStatus.SHIPPED ==ts.getStatus()){
            log.error("TS-目的仓为WMS-出库单同步处理, TS状态不是待发货, tsNo={}, status={}", tsNo, ts.getStatus());
            return false;
        }

        handlerOutboundStatus(ts, outboundNo, outboundTime);
        return true;
    }

    /**
     * 处理跟踪
     */
    public void handlerOutboundStatus(TransferOrder ts, String outboundNo, LocalDateTime outboundTime) {
        List<TransferOrderItem> tsItems = ts.getItems();
        for (TransferOrderItem item : tsItems) {
            if(item.getOutboundNo().equals(outboundNo)){
                item.setOutboundStatus(1);
                item.setOutboundTime(outboundTime);
                transferOrderItemRepository.updateOutBoundStatus(item);
                break;
            }
        }

        boolean allSuccess = true;
        for(TransferOrderItem item : tsItems){
            if(item.getOutboundStatus() != 1){
                allSuccess = false;
                break;
            }
        }

        if(allSuccess){
            // 全部成功,更新状态
            ts.setSyncApiStatus(SyncApiStatus.SUCCESS);
            ts.setStatus(TransferOrderStatus.SHIPPED);
            transferOrderService.update(ts);
        }
    }

}
