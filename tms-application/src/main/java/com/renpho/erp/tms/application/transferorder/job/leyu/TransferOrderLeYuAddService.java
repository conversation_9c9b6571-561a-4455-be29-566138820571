package com.renpho.erp.tms.application.transferorder.job.leyu;

import com.alibaba.fastjson.JSON;
import com.baomidou.lock.annotation.Lock4j;
import com.renpho.erp.apiproxy.lingxing.LingxingResponse;
import com.renpho.erp.apiproxy.lingxing.SystemAccount;
import com.renpho.erp.apiproxy.lingxing.wms.api.LxwmsInboundOrderApi;
import com.renpho.erp.apiproxy.lingxing.wms.model.inboundOrder.CreateData;
import com.renpho.erp.apiproxy.lingxing.wms.model.inboundOrder.CreateRequest;
import com.renpho.erp.tms.application.inbound.InboundRequestHistoryService;
import com.renpho.erp.tms.application.transferorder.TransferOrderService;
import com.renpho.erp.tms.application.transferorder.job.common.TransferOrderCommonService;
import com.renpho.erp.tms.application.transportrequest.PushTaskService;
import com.renpho.erp.tms.application.transportrequest.TransportRequestQueryService;
import com.renpho.erp.tms.application.transportrequest.job.common.TransportRequestCommonService;
import com.renpho.erp.tms.domain.exception.DingTalkWarning;
import com.renpho.erp.tms.domain.inbound.WarehouseProviderType;
import com.renpho.erp.tms.domain.transferorder.*;
import com.renpho.erp.tms.domain.transportorder.dto.TransportRequestOrderData;
import com.renpho.erp.tms.domain.transportrequest.*;
import com.renpho.erp.tms.infrastructure.persistence.transportrequest.repository.TransportRequestOrderRepository;
import com.renpho.karma.dto.R;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * TS-目的仓为乐鱼-创建入库单任务.
 *
 * <AUTHOR>
 * @since 2025/8/29
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class TransferOrderLeYuAddService {

    private final InboundRequestHistoryService inboundRequestHistoryService;
    private final TransferOrderCommonService transferOrderCommonService;
    private final TransferOrderService transferOrderService;
    private final PushTaskService pushTaskService;

    private final TransferOrderRepository transferOrderRepository;

    private final LxwmsInboundOrderApi lxwmsInboundOrderApi;

    // 仓库类型
    private final WarehouseProviderType warehouseType = WarehouseProviderType.LEYU_XLWMS;

    /**
     * TS-目的仓为乐鱼-创建入库单任务.
     */
    @Lock4j(name = "transfer:leyu:inbound:add:basic")
    //@Transactional(rollbackFor = Exception.class)
    public void createInboundLeYu(List<String> trNoList) {
        try {
            log.info("TS-目的仓为乐鱼的定时器任务开始");

            // 状态是待创建入库单
            TransferOrderStatus status = TransferOrderStatus.WAIT_CREATE_INBOUND;
            List<TransferOrderData> tsDataList = transferOrderCommonService.selectTsListByStatus(status.getValue(), warehouseType,false);

            // 外部参数
            tsDataList = transferOrderCommonService.fillExterWithTsStatus(status, tsDataList, trNoList);

            // 生成入库单推送任务
            if (!tsDataList.isEmpty()) {
                // 获取ts上下文信息
                Map<Integer, TransferOrder> tsMap = transferOrderService.getTsMapByIds(tsDataList.stream().map(TransferOrderData::getId).toList());

                for (TransferOrderData tsData : tsDataList) {
                    try {
                        TransferOrder ts = tsMap.get(tsData.getId());
                        if (ts == null) {
                            log.error("TS-目的仓为乐鱼-创建入库单任务生成异常, ts上下文找不到, tsId={}", tsData.getId());
                            continue;
                        }
                        // 目的仓限制
                        if (WarehouseProviderType.LEYU_XLWMS != ts.findWarehouseProviderType()) {
                            continue;
                        }

                        // 故意抛异常
                        // log.info((6/0)+"");

                        // 生成推送任务
                        PushTask trPushTask = pushTaskService.createInbound(ts, PushTaskStatus.PENDING,0,null);

                        // 执行入库单创建
                        pushTaskService.execute(() -> deliveryInstock(ts), trPushTask);
                    } catch (Exception e) {
                        log.error("TS-目的仓为乐鱼的定时器任务异常, 入库单推送任务生成失败, tsId={}", tsData.getId(), e);
                    }
                }
            }

        } catch (Exception e) {
            log.error("TS-目的仓为乐鱼的定时器任务异常", e);
        }
    }

    /**
     * 执行入库单创建
     *
     * @param ts         ts上下文
     */
    private Boolean deliveryInstock(TransferOrder ts) {
        String account = transferOrderCommonService.getConsumerCode(ts.getDestWarehouse().getId().id());
        if (account == null) {
            log.error("TS-目的仓为乐鱼-执行入库单任务异常, 客户编码找不到, tsNo={}, warehouseId= {}", ts.getTsNo(), ts.getDestWarehouse().getId().id());
            return false;
        }

        String warehouseCode = transferOrderCommonService.getWarehouseCode(ts.getDestWarehouse().getId().id());
        if (warehouseCode == null) {
            log.error("TS-目的仓为乐鱼-执行入库单任务异常, 仓库编码找不到, tsNo={}, warehouseId= {}", ts.getTsNo(), ts.getDestWarehouse().getId().id());
            return false;
        }

        List<TransferOrderItem> tsItems= ts.getItems();

        CreateRequest instockDto = new CreateRequest();
        // IMS仓库主数据-仓库绑定的第三方仓库编码
        instockDto.setWhCode(warehouseCode);
        instockDto.setInboundType(1);  // 默认1
        // 外部单号
        instockDto.setThirdOrderNo(ts.getTsNo());
        instockDto.setArrivalMode(2);  // 传 2-箱

        // 入库明细 , 这里有细节，要拿item表的长宽高，不能拿tr的长宽高，因为tr是基础配置，item是快照
        List<CreateRequest.BoxType> boxTypeList =new ArrayList<>();
        for (TransferOrderItem tsItem : tsItems) {
            CreateRequest.BoxType boxTypeDto = new CreateRequest.BoxType();
            boxTypeDto.setCtns(tsItem.getBoxQty().intValue());
            boxTypeDto.setLength(tsItem.getProduct().getActiveBoxSpec().getBoxLengthMetric());
            boxTypeDto.setWidth(tsItem.getProduct().getActiveBoxSpec().getBoxWidthMetric());
            boxTypeDto.setHeight(tsItem.getProduct().getActiveBoxSpec().getBoxHeightMetric());
            boxTypeDto.setWeight(tsItem.getProduct().getActiveBoxSpec().getGrossWeightPerBoxMetric());

            List<CreateRequest.Product> productList = new ArrayList<>();
            CreateRequest.Product productDto = new CreateRequest.Product();
            productDto.setSku(tsItem.getFnSku());
            productDto.setQuantity(tsItem.getQuantityPerBox());
            productList.add(productDto);

            boxTypeDto.setProductList(productList);
            boxTypeList.add(boxTypeDto);
        }

        instockDto.setBoxTypeList(boxTypeList);

        SystemAccount systemAccount = new SystemAccount();
        systemAccount.setAccount(account);
        log.info("TS-目的仓为乐鱼-执行入库单任务, 仓库编码={}, 参数={}", warehouseCode, JSON.toJSONString(instockDto));
        R<LingxingResponse<List<CreateData>>> result = lxwmsInboundOrderApi.create(systemAccount, instockDto);
        log.info("TS-目的仓为乐鱼-执行入库单任务, 仓库编码={}, 响应={}", warehouseCode, JSON.toJSONString(result));
        if (result != null) {
            if (result.isSuccess()) {
                LingxingResponse<List<CreateData>> inboundOrderResultVo = result.getData();
                if(inboundOrderResultVo!=null && !inboundOrderResultVo.getData().isEmpty()){
                    // 记录请求历史
                    inboundRequestHistoryService.add(ts, WarehouseProviderType.LEYU_XLWMS, null, instockDto, result.getData().getCode(), null, result);

                    List<CreateData> createDataList = inboundOrderResultVo.getData();
                    if(createDataList!=null && !createDataList.isEmpty()){
                        for (CreateData createData : createDataList) {
                             if(createData.getThirdOrderNo().equals(ts.getTsNo())){
                                 if(createData.isSuccess()){
                                     // 创建成功，保存 orderNo
                                     ts.setShipmentId(createData.getOrderNo());

                                     ts.setSyncApiStatus(SyncApiStatus.SUCCESS);
                                     transferOrderRepository.updateById(ts);

                                     // 3. 生成箱唛推送任务(这个跟ts生命周期重复，所以注释掉)
                                     // transportRequestCommonService.createCartonFile(tr, warehouseType);
                                     return true;
                                 } else{
                                     throw new DingTalkWarning("API创建入库单失败，原因：%s".formatted(createData.getMsg()));
                                 }
                             }
                            throw new DingTalkWarning("API创建入库单失败，原因：%s".formatted("没有匹配的单号数据, tsNo= " + ts.getTsNo()));
                        }
                    }
                    throw new DingTalkWarning("API创建入库单失败，原因：%s".formatted("没有返回对应的单号信息, tsNo= " + ts.getTsNo()));
                } else {
                    // 记录请求历史
                    inboundRequestHistoryService.add(ts, WarehouseProviderType.LEYU_XLWMS, null, instockDto, 200, null, result);
                    throw new DingTalkWarning("API创建入库单失败，原因：%s".formatted(result.getMessage()));
                }
            } else {
                // 记录请求历史
                inboundRequestHistoryService.add(ts, WarehouseProviderType.LEYU_XLWMS, null, instockDto, 200, null, result);
                throw new DingTalkWarning("API创建入库单失败，原因：%s".formatted(result.getMessage()));
            }
        }
        return true;
    }

    /**
     * 执行: TS-目的仓为乐鱼-箱唛任务.
     * @param tsNoList ts单号集合，用于异常重试的参数触发机制
     */
    @Lock4j(name = "transfer:leyu:inbound:add:pallet")
    @Transactional(rollbackFor = Exception.class)
    public void doingPalletLeYu(List<String> tsNoList) {
        transferOrderCommonService.doingPallet(warehouseType, tsNoList);
    }

}
